"use strict";var Hf=Object.create;var Ke=Object.defineProperty;var Df=Object.getOwnPropertyDescriptor;var _f=Object.getOwnPropertyNames;var Ff=Object.getPrototypeOf,Mf=Object.prototype.hasOwnProperty;var R=(e,t)=>()=>(t||e((t={exports:{}}).exports,t),t.exports),K=(e,t)=>{for(var r in t)Ke(e,r,{get:t[r],enumerable:!0})},va=(e,t,r,a)=>{if(t&&typeof t=="object"||typeof t=="function")for(let n of _f(t))!Mf.call(e,n)&&n!==r&&Ke(e,n,{get:()=>t[n],enumerable:!(a=Df(t,n))||a.enumerable});return e};var w=(e,t,r)=>(r=e!=null?Hf(Ff(e)):{},va(t||!e||!e.__esModule?Ke(r,"default",{value:e,enumerable:!0}):r,e)),Of=e=>va(Ke({},"__esModule",{value:!0}),e);var Ta=R((dP,Aa)=>{"use strict";var Vf=new Set(["ENOTFOUND","ENETUNREACH","UNABLE_TO_GET_ISSUER_CERT","UNABLE_TO_GET_CRL","UNABLE_TO_DECRYPT_CERT_SIGNATURE","UNABLE_TO_DECRYPT_CRL_SIGNATURE","UNABLE_TO_DECODE_ISSUER_PUBLIC_KEY","CERT_SIGNATURE_FAILURE","CRL_SIGNATURE_FAILURE","CERT_NOT_YET_VALID","CERT_HAS_EXPIRED","CRL_NOT_YET_VALID","CRL_HAS_EXPIRED","ERROR_IN_CERT_NOT_BEFORE_FIELD","ERROR_IN_CERT_NOT_AFTER_FIELD","ERROR_IN_CRL_LAST_UPDATE_FIELD","ERROR_IN_CRL_NEXT_UPDATE_FIELD","OUT_OF_MEM","DEPTH_ZERO_SELF_SIGNED_CERT","SELF_SIGNED_CERT_IN_CHAIN","UNABLE_TO_GET_ISSUER_CERT_LOCALLY","UNABLE_TO_VERIFY_LEAF_SIGNATURE","CERT_CHAIN_TOO_LONG","CERT_REVOKED","INVALID_CA","PATH_LENGTH_EXCEEDED","INVALID_PURPOSE","CERT_UNTRUSTED","CERT_REJECTED","HOSTNAME_MISMATCH"]);Aa.exports=e=>!Vf.has(e&&e.code)});var qs=R((qP,ja)=>{"use strict";ja.exports=Object});var Ja=R((hP,za)=>{"use strict";za.exports=Error});var Za=R((bP,Ya)=>{"use strict";Ya.exports=EvalError});var en=R((fP,Xa)=>{"use strict";Xa.exports=RangeError});var sn=R((xP,tn)=>{"use strict";tn.exports=ReferenceError});var hs=R((IP,rn)=>{"use strict";rn.exports=SyntaxError});var be=R((kP,an)=>{"use strict";an.exports=TypeError});var on=R((PP,nn)=>{"use strict";nn.exports=URIError});var un=R((vP,pn)=>{"use strict";pn.exports=Math.abs});var dn=R((AP,cn)=>{"use strict";cn.exports=Math.floor});var gn=R((TP,ln)=>{"use strict";ln.exports=Math.max});var Rn=R((BP,yn)=>{"use strict";yn.exports=Math.min});var qn=R((CP,mn)=>{"use strict";mn.exports=Math.pow});var bn=R((wP,hn)=>{"use strict";hn.exports=Math.round});var xn=R((UP,fn)=>{"use strict";fn.exports=Number.isNaN||function(t){return t!==t}});var kn=R((GP,In)=>{"use strict";var bx=xn();In.exports=function(t){return bx(t)||t===0?t:t<0?-1:1}});var vn=R((EP,Pn)=>{"use strict";Pn.exports=Object.getOwnPropertyDescriptor});var Se=R((LP,An)=>{"use strict";var bt=vn();if(bt)try{bt([],"length")}catch{bt=null}An.exports=bt});var Qe=R((WP,Tn)=>{"use strict";var ft=Object.defineProperty||!1;if(ft)try{ft({},"a",{value:1})}catch{ft=!1}Tn.exports=ft});var Cn=R((SP,Bn)=>{"use strict";Bn.exports=function(){if(typeof Symbol!="function"||typeof Object.getOwnPropertySymbols!="function")return!1;if(typeof Symbol.iterator=="symbol")return!0;var t={},r=Symbol("test"),a=Object(r);if(typeof r=="string"||Object.prototype.toString.call(r)!=="[object Symbol]"||Object.prototype.toString.call(a)!=="[object Symbol]")return!1;var n=42;t[r]=n;for(var o in t)return!1;if(typeof Object.keys=="function"&&Object.keys(t).length!==0||typeof Object.getOwnPropertyNames=="function"&&Object.getOwnPropertyNames(t).length!==0)return!1;var i=Object.getOwnPropertySymbols(t);if(i.length!==1||i[0]!==r||!Object.prototype.propertyIsEnumerable.call(t,r))return!1;if(typeof Object.getOwnPropertyDescriptor=="function"){var u=Object.getOwnPropertyDescriptor(t,r);if(u.value!==n||u.enumerable!==!0)return!1}return!0}});var Gn=R((QP,Un)=>{"use strict";var wn=typeof Symbol<"u"&&Symbol,fx=Cn();Un.exports=function(){return typeof wn!="function"||typeof Symbol!="function"||typeof wn("foo")!="symbol"||typeof Symbol("bar")!="symbol"?!1:fx()}});var bs=R((HP,En)=>{"use strict";En.exports=typeof Reflect<"u"&&Reflect.getPrototypeOf||null});var fs=R((DP,Ln)=>{"use strict";var xx=qs();Ln.exports=xx.getPrototypeOf||null});var Qn=R((_P,Sn)=>{"use strict";var Ix="Function.prototype.bind called on incompatible ",kx=Object.prototype.toString,Px=Math.max,vx="[object Function]",Wn=function(t,r){for(var a=[],n=0;n<t.length;n+=1)a[n]=t[n];for(var o=0;o<r.length;o+=1)a[o+t.length]=r[o];return a},Ax=function(t,r){for(var a=[],n=r||0,o=0;n<t.length;n+=1,o+=1)a[o]=t[n];return a},Tx=function(e,t){for(var r="",a=0;a<e.length;a+=1)r+=e[a],a+1<e.length&&(r+=t);return r};Sn.exports=function(t){var r=this;if(typeof r!="function"||kx.apply(r)!==vx)throw new TypeError(Ix+r);for(var a=Ax(arguments,1),n,o=function(){if(this instanceof n){var y=r.apply(this,Wn(a,arguments));return Object(y)===y?y:this}return r.apply(t,Wn(a,arguments))},i=Px(0,r.length-a.length),u=[],p=0;p<i;p++)u[p]="$"+p;if(n=Function("binder","return function ("+Tx(u,",")+"){ return binder.apply(this,arguments); }")(o),r.prototype){var s=function(){};s.prototype=r.prototype,n.prototype=new s,s.prototype=null}return n}});var fe=R((FP,Hn)=>{"use strict";var Bx=Qn();Hn.exports=Function.prototype.bind||Bx});var xt=R((MP,Dn)=>{"use strict";Dn.exports=Function.prototype.call});var xs=R((OP,_n)=>{"use strict";_n.exports=Function.prototype.apply});var Mn=R((VP,Fn)=>{"use strict";Fn.exports=typeof Reflect<"u"&&Reflect&&Reflect.apply});var Vn=R((NP,On)=>{"use strict";var Cx=fe(),wx=xs(),Ux=xt(),Gx=Mn();On.exports=Gx||Cx.call(Ux,wx)});var Kn=R((KP,Nn)=>{"use strict";var Ex=fe(),Lx=be(),Wx=xt(),Sx=Vn();Nn.exports=function(t){if(t.length<1||typeof t[0]!="function")throw new Lx("a function is required");return Sx(Ex,Wx,t)}});var Zn=R(($P,Yn)=>{"use strict";var Qx=Kn(),$n=Se(),zn;try{zn=[].__proto__===Array.prototype}catch(e){if(!e||typeof e!="object"||!("code"in e)||e.code!=="ERR_PROTO_ACCESS")throw e}var Is=!!zn&&$n&&$n(Object.prototype,"__proto__"),Jn=Object,jn=Jn.getPrototypeOf;Yn.exports=Is&&typeof Is.get=="function"?Qx([Is.get]):typeof jn=="function"?function(t){return jn(t==null?t:Jn(t))}:!1});var ro=R((jP,so)=>{"use strict";var Xn=bs(),eo=fs(),to=Zn();so.exports=Xn?function(t){return Xn(t)}:eo?function(t){if(!t||typeof t!="object"&&typeof t!="function")throw new TypeError("getProto: not an object");return eo(t)}:to?function(t){return to(t)}:null});var no=R((zP,ao)=>{"use strict";var Hx=Function.prototype.call,Dx=Object.prototype.hasOwnProperty,_x=fe();ao.exports=_x.call(Hx,Dx)});var Fe=R((JP,lo)=>{"use strict";var f,Fx=qs(),Mx=Ja(),Ox=Za(),Vx=en(),Nx=sn(),Pe=hs(),ke=be(),Kx=on(),$x=un(),jx=dn(),zx=gn(),Jx=Rn(),Yx=qn(),Zx=bn(),Xx=kn(),uo=Function,ks=function(e){try{return uo('"use strict"; return ('+e+").constructor;")()}catch{}},He=Se(),eI=Qe(),Ps=function(){throw new ke},tI=He?function(){try{return arguments.callee,Ps}catch{try{return He(arguments,"callee").get}catch{return Ps}}}():Ps,xe=Gn()(),E=ro(),sI=fs(),rI=bs(),co=xs(),De=xt(),Ie={},aI=typeof Uint8Array>"u"||!E?f:E(Uint8Array),de={__proto__:null,"%AggregateError%":typeof AggregateError>"u"?f:AggregateError,"%Array%":Array,"%ArrayBuffer%":typeof ArrayBuffer>"u"?f:ArrayBuffer,"%ArrayIteratorPrototype%":xe&&E?E([][Symbol.iterator]()):f,"%AsyncFromSyncIteratorPrototype%":f,"%AsyncFunction%":Ie,"%AsyncGenerator%":Ie,"%AsyncGeneratorFunction%":Ie,"%AsyncIteratorPrototype%":Ie,"%Atomics%":typeof Atomics>"u"?f:Atomics,"%BigInt%":typeof BigInt>"u"?f:BigInt,"%BigInt64Array%":typeof BigInt64Array>"u"?f:BigInt64Array,"%BigUint64Array%":typeof BigUint64Array>"u"?f:BigUint64Array,"%Boolean%":Boolean,"%DataView%":typeof DataView>"u"?f:DataView,"%Date%":Date,"%decodeURI%":decodeURI,"%decodeURIComponent%":decodeURIComponent,"%encodeURI%":encodeURI,"%encodeURIComponent%":encodeURIComponent,"%Error%":Mx,"%eval%":eval,"%EvalError%":Ox,"%Float32Array%":typeof Float32Array>"u"?f:Float32Array,"%Float64Array%":typeof Float64Array>"u"?f:Float64Array,"%FinalizationRegistry%":typeof FinalizationRegistry>"u"?f:FinalizationRegistry,"%Function%":uo,"%GeneratorFunction%":Ie,"%Int8Array%":typeof Int8Array>"u"?f:Int8Array,"%Int16Array%":typeof Int16Array>"u"?f:Int16Array,"%Int32Array%":typeof Int32Array>"u"?f:Int32Array,"%isFinite%":isFinite,"%isNaN%":isNaN,"%IteratorPrototype%":xe&&E?E(E([][Symbol.iterator]())):f,"%JSON%":typeof JSON=="object"?JSON:f,"%Map%":typeof Map>"u"?f:Map,"%MapIteratorPrototype%":typeof Map>"u"||!xe||!E?f:E(new Map()[Symbol.iterator]()),"%Math%":Math,"%Number%":Number,"%Object%":Fx,"%Object.getOwnPropertyDescriptor%":He,"%parseFloat%":parseFloat,"%parseInt%":parseInt,"%Promise%":typeof Promise>"u"?f:Promise,"%Proxy%":typeof Proxy>"u"?f:Proxy,"%RangeError%":Vx,"%ReferenceError%":Nx,"%Reflect%":typeof Reflect>"u"?f:Reflect,"%RegExp%":RegExp,"%Set%":typeof Set>"u"?f:Set,"%SetIteratorPrototype%":typeof Set>"u"||!xe||!E?f:E(new Set()[Symbol.iterator]()),"%SharedArrayBuffer%":typeof SharedArrayBuffer>"u"?f:SharedArrayBuffer,"%String%":String,"%StringIteratorPrototype%":xe&&E?E(""[Symbol.iterator]()):f,"%Symbol%":xe?Symbol:f,"%SyntaxError%":Pe,"%ThrowTypeError%":tI,"%TypedArray%":aI,"%TypeError%":ke,"%Uint8Array%":typeof Uint8Array>"u"?f:Uint8Array,"%Uint8ClampedArray%":typeof Uint8ClampedArray>"u"?f:Uint8ClampedArray,"%Uint16Array%":typeof Uint16Array>"u"?f:Uint16Array,"%Uint32Array%":typeof Uint32Array>"u"?f:Uint32Array,"%URIError%":Kx,"%WeakMap%":typeof WeakMap>"u"?f:WeakMap,"%WeakRef%":typeof WeakRef>"u"?f:WeakRef,"%WeakSet%":typeof WeakSet>"u"?f:WeakSet,"%Function.prototype.call%":De,"%Function.prototype.apply%":co,"%Object.defineProperty%":eI,"%Object.getPrototypeOf%":sI,"%Math.abs%":$x,"%Math.floor%":jx,"%Math.max%":zx,"%Math.min%":Jx,"%Math.pow%":Yx,"%Math.round%":Zx,"%Math.sign%":Xx,"%Reflect.getPrototypeOf%":rI};if(E)try{null.error}catch(e){oo=E(E(e)),de["%Error.prototype%"]=oo}var oo,nI=function e(t){var r;if(t==="%AsyncFunction%")r=ks("async function () {}");else if(t==="%GeneratorFunction%")r=ks("function* () {}");else if(t==="%AsyncGeneratorFunction%")r=ks("async function* () {}");else if(t==="%AsyncGenerator%"){var a=e("%AsyncGeneratorFunction%");a&&(r=a.prototype)}else if(t==="%AsyncIteratorPrototype%"){var n=e("%AsyncGenerator%");n&&E&&(r=E(n.prototype))}return de[t]=r,r},io={__proto__:null,"%ArrayBufferPrototype%":["ArrayBuffer","prototype"],"%ArrayPrototype%":["Array","prototype"],"%ArrayProto_entries%":["Array","prototype","entries"],"%ArrayProto_forEach%":["Array","prototype","forEach"],"%ArrayProto_keys%":["Array","prototype","keys"],"%ArrayProto_values%":["Array","prototype","values"],"%AsyncFunctionPrototype%":["AsyncFunction","prototype"],"%AsyncGenerator%":["AsyncGeneratorFunction","prototype"],"%AsyncGeneratorPrototype%":["AsyncGeneratorFunction","prototype","prototype"],"%BooleanPrototype%":["Boolean","prototype"],"%DataViewPrototype%":["DataView","prototype"],"%DatePrototype%":["Date","prototype"],"%ErrorPrototype%":["Error","prototype"],"%EvalErrorPrototype%":["EvalError","prototype"],"%Float32ArrayPrototype%":["Float32Array","prototype"],"%Float64ArrayPrototype%":["Float64Array","prototype"],"%FunctionPrototype%":["Function","prototype"],"%Generator%":["GeneratorFunction","prototype"],"%GeneratorPrototype%":["GeneratorFunction","prototype","prototype"],"%Int8ArrayPrototype%":["Int8Array","prototype"],"%Int16ArrayPrototype%":["Int16Array","prototype"],"%Int32ArrayPrototype%":["Int32Array","prototype"],"%JSONParse%":["JSON","parse"],"%JSONStringify%":["JSON","stringify"],"%MapPrototype%":["Map","prototype"],"%NumberPrototype%":["Number","prototype"],"%ObjectPrototype%":["Object","prototype"],"%ObjProto_toString%":["Object","prototype","toString"],"%ObjProto_valueOf%":["Object","prototype","valueOf"],"%PromisePrototype%":["Promise","prototype"],"%PromiseProto_then%":["Promise","prototype","then"],"%Promise_all%":["Promise","all"],"%Promise_reject%":["Promise","reject"],"%Promise_resolve%":["Promise","resolve"],"%RangeErrorPrototype%":["RangeError","prototype"],"%ReferenceErrorPrototype%":["ReferenceError","prototype"],"%RegExpPrototype%":["RegExp","prototype"],"%SetPrototype%":["Set","prototype"],"%SharedArrayBufferPrototype%":["SharedArrayBuffer","prototype"],"%StringPrototype%":["String","prototype"],"%SymbolPrototype%":["Symbol","prototype"],"%SyntaxErrorPrototype%":["SyntaxError","prototype"],"%TypedArrayPrototype%":["TypedArray","prototype"],"%TypeErrorPrototype%":["TypeError","prototype"],"%Uint8ArrayPrototype%":["Uint8Array","prototype"],"%Uint8ClampedArrayPrototype%":["Uint8ClampedArray","prototype"],"%Uint16ArrayPrototype%":["Uint16Array","prototype"],"%Uint32ArrayPrototype%":["Uint32Array","prototype"],"%URIErrorPrototype%":["URIError","prototype"],"%WeakMapPrototype%":["WeakMap","prototype"],"%WeakSetPrototype%":["WeakSet","prototype"]},_e=fe(),It=no(),oI=_e.call(De,Array.prototype.concat),iI=_e.call(co,Array.prototype.splice),po=_e.call(De,String.prototype.replace),kt=_e.call(De,String.prototype.slice),pI=_e.call(De,RegExp.prototype.exec),uI=/[^%.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|%$))/g,cI=/\\(\\)?/g,dI=function(t){var r=kt(t,0,1),a=kt(t,-1);if(r==="%"&&a!=="%")throw new Pe("invalid intrinsic syntax, expected closing `%`");if(a==="%"&&r!=="%")throw new Pe("invalid intrinsic syntax, expected opening `%`");var n=[];return po(t,uI,function(o,i,u,p){n[n.length]=u?po(p,cI,"$1"):i||o}),n},lI=function(t,r){var a=t,n;if(It(io,a)&&(n=io[a],a="%"+n[0]+"%"),It(de,a)){var o=de[a];if(o===Ie&&(o=nI(a)),typeof o>"u"&&!r)throw new ke("intrinsic "+t+" exists, but is not available. Please file an issue!");return{alias:n,name:a,value:o}}throw new Pe("intrinsic "+t+" does not exist!")};lo.exports=function(t,r){if(typeof t!="string"||t.length===0)throw new ke("intrinsic name must be a non-empty string");if(arguments.length>1&&typeof r!="boolean")throw new ke('"allowMissing" argument must be a boolean');if(pI(/^%?[^%]*%?$/,t)===null)throw new Pe("`%` may not be present anywhere but at the beginning and end of the intrinsic name");var a=dI(t),n=a.length>0?a[0]:"",o=lI("%"+n+"%",r),i=o.name,u=o.value,p=!1,s=o.alias;s&&(n=s[0],iI(a,oI([0,1],s)));for(var y=1,P=!0;y<a.length;y+=1){var b=a[y],k=kt(b,0,1),I=kt(b,-1);if((k==='"'||k==="'"||k==="`"||I==='"'||I==="'"||I==="`")&&k!==I)throw new Pe("property names with quotes must have matching quotes");if((b==="constructor"||!P)&&(p=!0),n+="."+b,i="%"+n+"%",It(de,i))u=de[i];else if(u!=null){if(!(b in u)){if(!r)throw new ke("base intrinsic for "+t+" exists, but the property is not available.");return}if(He&&y+1>=a.length){var G=He(u,b);P=!!G,P&&"get"in G&&!("originalValue"in G.get)?u=G.get:u=u[b]}else P=It(u,b),u=u[b];P&&!p&&(de[i]=u)}}return u}});var mo=R((YP,Ro)=>{"use strict";var go=Qe(),gI=hs(),ve=be(),yo=Se();Ro.exports=function(t,r,a){if(!t||typeof t!="object"&&typeof t!="function")throw new ve("`obj` must be an object or a function`");if(typeof r!="string"&&typeof r!="symbol")throw new ve("`property` must be a string or a symbol`");if(arguments.length>3&&typeof arguments[3]!="boolean"&&arguments[3]!==null)throw new ve("`nonEnumerable`, if provided, must be a boolean or null");if(arguments.length>4&&typeof arguments[4]!="boolean"&&arguments[4]!==null)throw new ve("`nonWritable`, if provided, must be a boolean or null");if(arguments.length>5&&typeof arguments[5]!="boolean"&&arguments[5]!==null)throw new ve("`nonConfigurable`, if provided, must be a boolean or null");if(arguments.length>6&&typeof arguments[6]!="boolean")throw new ve("`loose`, if provided, must be a boolean");var n=arguments.length>3?arguments[3]:null,o=arguments.length>4?arguments[4]:null,i=arguments.length>5?arguments[5]:null,u=arguments.length>6?arguments[6]:!1,p=!!yo&&yo(t,r);if(go)go(t,r,{configurable:i===null&&p?p.configurable:!i,enumerable:n===null&&p?p.enumerable:!n,value:a,writable:o===null&&p?p.writable:!o});else if(u||!n&&!o&&!i)t[r]=a;else throw new gI("This environment does not support defining a property as non-configurable, non-writable, or non-enumerable.")}});var bo=R((ZP,ho)=>{"use strict";var vs=Qe(),qo=function(){return!!vs};qo.hasArrayLengthDefineBug=function(){if(!vs)return null;try{return vs([],"length",{value:1}).length!==1}catch{return!0}};ho.exports=qo});var Po=R((XP,ko)=>{"use strict";var yI=Fe(),fo=mo(),RI=bo()(),xo=Se(),Io=be(),mI=yI("%Math.floor%");ko.exports=function(t,r){if(typeof t!="function")throw new Io("`fn` is not a function");if(typeof r!="number"||r<0||r>4294967295||mI(r)!==r)throw new Io("`length` must be a positive 32-bit integer");var a=arguments.length>2&&!!arguments[2],n=!0,o=!0;if("length"in t&&xo){var i=xo(t,"length");i&&!i.configurable&&(n=!1),i&&!i.writable&&(o=!1)}return(n||o||!a)&&(RI?fo(t,"length",r,!0,!0):fo(t,"length",r)),t}});var wo=R((ev,Pt)=>{"use strict";var As=fe(),vt=Fe(),qI=Po(),hI=be(),To=vt("%Function.prototype.apply%"),Bo=vt("%Function.prototype.call%"),Co=vt("%Reflect.apply%",!0)||As.call(Bo,To),vo=Qe(),bI=vt("%Math.max%");Pt.exports=function(t){if(typeof t!="function")throw new hI("a function is required");var r=Co(As,Bo,arguments);return qI(r,1+bI(0,t.length-(arguments.length-1)),!0)};var Ao=function(){return Co(As,To,arguments)};vo?vo(Pt.exports,"apply",{value:Ao}):Pt.exports.apply=Ao});var Lo=R((tv,Eo)=>{"use strict";var Uo=Fe(),Go=wo(),fI=Go(Uo("String.prototype.indexOf"));Eo.exports=function(t,r){var a=Uo(t,!!r);return typeof a=="function"&&fI(t,".prototype.")>-1?Go(a):a}});var So=R((sv,Wo)=>{Wo.exports=require("util").inspect});var ri=R((rv,si)=>{var Ss=typeof Map=="function"&&Map.prototype,Ts=Object.getOwnPropertyDescriptor&&Ss?Object.getOwnPropertyDescriptor(Map.prototype,"size"):null,Tt=Ss&&Ts&&typeof Ts.get=="function"?Ts.get:null,Qo=Ss&&Map.prototype.forEach,Qs=typeof Set=="function"&&Set.prototype,Bs=Object.getOwnPropertyDescriptor&&Qs?Object.getOwnPropertyDescriptor(Set.prototype,"size"):null,Bt=Qs&&Bs&&typeof Bs.get=="function"?Bs.get:null,Ho=Qs&&Set.prototype.forEach,xI=typeof WeakMap=="function"&&WeakMap.prototype,Oe=xI?WeakMap.prototype.has:null,II=typeof WeakSet=="function"&&WeakSet.prototype,Ve=II?WeakSet.prototype.has:null,kI=typeof WeakRef=="function"&&WeakRef.prototype,Do=kI?WeakRef.prototype.deref:null,PI=Boolean.prototype.valueOf,vI=Object.prototype.toString,AI=Function.prototype.toString,TI=String.prototype.match,Hs=String.prototype.slice,ne=String.prototype.replace,BI=String.prototype.toUpperCase,_o=String.prototype.toLowerCase,zo=RegExp.prototype.test,Fo=Array.prototype.concat,$=Array.prototype.join,CI=Array.prototype.slice,Mo=Math.floor,Us=typeof BigInt=="function"?BigInt.prototype.valueOf:null,Cs=Object.getOwnPropertySymbols,Gs=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?Symbol.prototype.toString:null,Ae=typeof Symbol=="function"&&typeof Symbol.iterator=="object",H=typeof Symbol=="function"&&Symbol.toStringTag&&(typeof Symbol.toStringTag===Ae||"symbol")?Symbol.toStringTag:null,Jo=Object.prototype.propertyIsEnumerable,Oo=(typeof Reflect=="function"?Reflect.getPrototypeOf:Object.getPrototypeOf)||([].__proto__===Array.prototype?function(e){return e.__proto__}:null);function Vo(e,t){if(e===1/0||e===-1/0||e!==e||e&&e>-1e3&&e<1e3||zo.call(/e/,t))return t;var r=/[0-9](?=(?:[0-9]{3})+(?![0-9]))/g;if(typeof e=="number"){var a=e<0?-Mo(-e):Mo(e);if(a!==e){var n=String(a),o=Hs.call(t,n.length+1);return ne.call(n,r,"$&_")+"."+ne.call(ne.call(o,/([0-9]{3})/g,"$&_"),/_$/,"")}}return ne.call(t,r,"$&_")}var Es=So(),No=Es.custom,Ko=Xo(No)?No:null,Yo={__proto__:null,double:'"',single:"'"},wI={__proto__:null,double:/(["\\])/g,single:/(['\\])/g};si.exports=function e(t,r,a,n){var o=r||{};if(Z(o,"quoteStyle")&&!Z(Yo,o.quoteStyle))throw new TypeError('option "quoteStyle" must be "single" or "double"');if(Z(o,"maxStringLength")&&(typeof o.maxStringLength=="number"?o.maxStringLength<0&&o.maxStringLength!==1/0:o.maxStringLength!==null))throw new TypeError('option "maxStringLength", if provided, must be a positive integer, Infinity, or `null`');var i=Z(o,"customInspect")?o.customInspect:!0;if(typeof i!="boolean"&&i!=="symbol")throw new TypeError("option \"customInspect\", if provided, must be `true`, `false`, or `'symbol'`");if(Z(o,"indent")&&o.indent!==null&&o.indent!=="	"&&!(parseInt(o.indent,10)===o.indent&&o.indent>0))throw new TypeError('option "indent" must be "\\t", an integer > 0, or `null`');if(Z(o,"numericSeparator")&&typeof o.numericSeparator!="boolean")throw new TypeError('option "numericSeparator", if provided, must be `true` or `false`');var u=o.numericSeparator;if(typeof t>"u")return"undefined";if(t===null)return"null";if(typeof t=="boolean")return t?"true":"false";if(typeof t=="string")return ti(t,o);if(typeof t=="number"){if(t===0)return 1/0/t>0?"0":"-0";var p=String(t);return u?Vo(t,p):p}if(typeof t=="bigint"){var s=String(t)+"n";return u?Vo(t,s):s}var y=typeof o.depth>"u"?5:o.depth;if(typeof a>"u"&&(a=0),a>=y&&y>0&&typeof t=="object")return Ls(t)?"[Array]":"[Object]";var P=jI(o,a);if(typeof n>"u")n=[];else if(ei(n,t)>=0)return"[Circular]";function b(N,se,J){if(se&&(n=CI.call(n),n.push(se)),J){var Ee={depth:o.depth};return Z(o,"quoteStyle")&&(Ee.quoteStyle=o.quoteStyle),e(N,Ee,a+1,n)}return e(N,o,a+1,n)}if(typeof t=="function"&&!$o(t)){var k=DI(t),I=At(t,b);return"[Function"+(k?": "+k:" (anonymous)")+"]"+(I.length>0?" { "+$.call(I,", ")+" }":"")}if(Xo(t)){var G=Ae?ne.call(String(t),/^(Symbol\(.*\))_[^)]*$/,"$1"):Gs.call(t);return typeof t=="object"&&!Ae?Me(G):G}if(NI(t)){for(var U="<"+_o.call(String(t.nodeName)),x=t.attributes||[],z=0;z<x.length;z++)U+=" "+x[z].name+"="+Zo(UI(x[z].value),"double",o);return U+=">",t.childNodes&&t.childNodes.length&&(U+="..."),U+="</"+_o.call(String(t.nodeName))+">",U}if(Ls(t)){if(t.length===0)return"[]";var ee=At(t,b);return P&&!$I(ee)?"["+Ws(ee,P)+"]":"[ "+$.call(ee,", ")+" ]"}if(EI(t)){var me=At(t,b);return!("cause"in Error.prototype)&&"cause"in t&&!Jo.call(t,"cause")?"{ ["+String(t)+"] "+$.call(Fo.call("[cause]: "+b(t.cause),me),", ")+" }":me.length===0?"["+String(t)+"]":"{ ["+String(t)+"] "+$.call(me,", ")+" }"}if(typeof t=="object"&&i){if(Ko&&typeof t[Ko]=="function"&&Es)return Es(t,{depth:y-a});if(i!=="symbol"&&typeof t.inspect=="function")return t.inspect()}if(_I(t)){var we=[];return Qo&&Qo.call(t,function(N,se){we.push(b(se,t,!0)+" => "+b(N,t))}),jo("Map",Tt.call(t),we,P)}if(OI(t)){var Ue=[];return Ho&&Ho.call(t,function(N){Ue.push(b(N,t))}),jo("Set",Bt.call(t),Ue,P)}if(FI(t))return ws("WeakMap");if(VI(t))return ws("WeakSet");if(MI(t))return ws("WeakRef");if(WI(t))return Me(b(Number(t)));if(QI(t))return Me(b(Us.call(t)));if(SI(t))return Me(PI.call(t));if(LI(t))return Me(b(String(t)));if(typeof window<"u"&&t===window)return"{ [object Window] }";if(typeof globalThis<"u"&&t===globalThis||typeof global<"u"&&t===global)return"{ [object globalThis] }";if(!GI(t)&&!$o(t)){var ie=At(t,b),Ge=Oo?Oo(t)===Object.prototype:t instanceof Object||t.constructor===Object,te=t instanceof Object?"":"null prototype",qe=!Ge&&H&&Object(t)===t&&H in t?Hs.call(oe(t),8,-1):te?"Object":"",pe=Ge||typeof t.constructor!="function"?"":t.constructor.name?t.constructor.name+" ":"",he=pe+(qe||te?"["+$.call(Fo.call([],qe||[],te||[]),": ")+"] ":"");return ie.length===0?he+"{}":P?he+"{"+Ws(ie,P)+"}":he+"{ "+$.call(ie,", ")+" }"}return String(t)};function Zo(e,t,r){var a=r.quoteStyle||t,n=Yo[a];return n+e+n}function UI(e){return ne.call(String(e),/"/g,"&quot;")}function Ls(e){return oe(e)==="[object Array]"&&(!H||!(typeof e=="object"&&H in e))}function GI(e){return oe(e)==="[object Date]"&&(!H||!(typeof e=="object"&&H in e))}function $o(e){return oe(e)==="[object RegExp]"&&(!H||!(typeof e=="object"&&H in e))}function EI(e){return oe(e)==="[object Error]"&&(!H||!(typeof e=="object"&&H in e))}function LI(e){return oe(e)==="[object String]"&&(!H||!(typeof e=="object"&&H in e))}function WI(e){return oe(e)==="[object Number]"&&(!H||!(typeof e=="object"&&H in e))}function SI(e){return oe(e)==="[object Boolean]"&&(!H||!(typeof e=="object"&&H in e))}function Xo(e){if(Ae)return e&&typeof e=="object"&&e instanceof Symbol;if(typeof e=="symbol")return!0;if(!e||typeof e!="object"||!Gs)return!1;try{return Gs.call(e),!0}catch{}return!1}function QI(e){if(!e||typeof e!="object"||!Us)return!1;try{return Us.call(e),!0}catch{}return!1}var HI=Object.prototype.hasOwnProperty||function(e){return e in this};function Z(e,t){return HI.call(e,t)}function oe(e){return vI.call(e)}function DI(e){if(e.name)return e.name;var t=TI.call(AI.call(e),/^function\s*([\w$]+)/);return t?t[1]:null}function ei(e,t){if(e.indexOf)return e.indexOf(t);for(var r=0,a=e.length;r<a;r++)if(e[r]===t)return r;return-1}function _I(e){if(!Tt||!e||typeof e!="object")return!1;try{Tt.call(e);try{Bt.call(e)}catch{return!0}return e instanceof Map}catch{}return!1}function FI(e){if(!Oe||!e||typeof e!="object")return!1;try{Oe.call(e,Oe);try{Ve.call(e,Ve)}catch{return!0}return e instanceof WeakMap}catch{}return!1}function MI(e){if(!Do||!e||typeof e!="object")return!1;try{return Do.call(e),!0}catch{}return!1}function OI(e){if(!Bt||!e||typeof e!="object")return!1;try{Bt.call(e);try{Tt.call(e)}catch{return!0}return e instanceof Set}catch{}return!1}function VI(e){if(!Ve||!e||typeof e!="object")return!1;try{Ve.call(e,Ve);try{Oe.call(e,Oe)}catch{return!0}return e instanceof WeakSet}catch{}return!1}function NI(e){return!e||typeof e!="object"?!1:typeof HTMLElement<"u"&&e instanceof HTMLElement?!0:typeof e.nodeName=="string"&&typeof e.getAttribute=="function"}function ti(e,t){if(e.length>t.maxStringLength){var r=e.length-t.maxStringLength,a="... "+r+" more character"+(r>1?"s":"");return ti(Hs.call(e,0,t.maxStringLength),t)+a}var n=wI[t.quoteStyle||"single"];n.lastIndex=0;var o=ne.call(ne.call(e,n,"\\$1"),/[\x00-\x1f]/g,KI);return Zo(o,"single",t)}function KI(e){var t=e.charCodeAt(0),r={8:"b",9:"t",10:"n",12:"f",13:"r"}[t];return r?"\\"+r:"\\x"+(t<16?"0":"")+BI.call(t.toString(16))}function Me(e){return"Object("+e+")"}function ws(e){return e+" { ? }"}function jo(e,t,r,a){var n=a?Ws(r,a):$.call(r,", ");return e+" ("+t+") {"+n+"}"}function $I(e){for(var t=0;t<e.length;t++)if(ei(e[t],`
`)>=0)return!1;return!0}function jI(e,t){var r;if(e.indent==="	")r="	";else if(typeof e.indent=="number"&&e.indent>0)r=$.call(Array(e.indent+1)," ");else return null;return{base:r,prev:$.call(Array(t+1),r)}}function Ws(e,t){if(e.length===0)return"";var r=`
`+t.prev+t.base;return r+$.call(e,","+r)+`
`+t.prev}function At(e,t){var r=Ls(e),a=[];if(r){a.length=e.length;for(var n=0;n<e.length;n++)a[n]=Z(e,n)?t(e[n],e):""}var o=typeof Cs=="function"?Cs(e):[],i;if(Ae){i={};for(var u=0;u<o.length;u++)i["$"+o[u]]=o[u]}for(var p in e)Z(e,p)&&(r&&String(Number(p))===p&&p<e.length||Ae&&i["$"+p]instanceof Symbol||(zo.call(/[^\w$]/,p)?a.push(t(p,e)+": "+t(e[p],e)):a.push(p+": "+t(e[p],e))));if(typeof Cs=="function")for(var s=0;s<o.length;s++)Jo.call(e,o[s])&&a.push("["+t(o[s])+"]: "+t(e[o[s]],e));return a}});var ni=R((av,ai)=>{"use strict";var Ds=Fe(),Te=Lo(),zI=ri(),JI=Ds("%TypeError%"),Ct=Ds("%WeakMap%",!0),wt=Ds("%Map%",!0),YI=Te("WeakMap.prototype.get",!0),ZI=Te("WeakMap.prototype.set",!0),XI=Te("WeakMap.prototype.has",!0),ek=Te("Map.prototype.get",!0),tk=Te("Map.prototype.set",!0),sk=Te("Map.prototype.has",!0),_s=function(e,t){for(var r=e,a;(a=r.next)!==null;r=a)if(a.key===t)return r.next=a.next,a.next=e.next,e.next=a,a},rk=function(e,t){var r=_s(e,t);return r&&r.value},ak=function(e,t,r){var a=_s(e,t);a?a.value=r:e.next={key:t,next:e.next,value:r}},nk=function(e,t){return!!_s(e,t)};ai.exports=function(){var t,r,a,n={assert:function(o){if(!n.has(o))throw new JI("Side channel does not contain "+zI(o))},get:function(o){if(Ct&&o&&(typeof o=="object"||typeof o=="function")){if(t)return YI(t,o)}else if(wt){if(r)return ek(r,o)}else if(a)return rk(a,o)},has:function(o){if(Ct&&o&&(typeof o=="object"||typeof o=="function")){if(t)return XI(t,o)}else if(wt){if(r)return sk(r,o)}else if(a)return nk(a,o);return!1},set:function(o,i){Ct&&o&&(typeof o=="object"||typeof o=="function")?(t||(t=new Ct),ZI(t,o,i)):wt?(r||(r=new wt),tk(r,o,i)):(a||(a={key:{},next:null}),ak(a,o,i))}};return n}});var Ut=R((nv,oi)=>{"use strict";var ok=String.prototype.replace,ik=/%20/g,Fs={RFC1738:"RFC1738",RFC3986:"RFC3986"};oi.exports={default:Fs.RFC3986,formatters:{RFC1738:function(e){return ok.call(e,ik,"+")},RFC3986:function(e){return String(e)}},RFC1738:Fs.RFC1738,RFC3986:Fs.RFC3986}});var Os=R((ov,pi)=>{"use strict";var pk=Ut(),Ms=Object.prototype.hasOwnProperty,le=Array.isArray,j=function(){for(var e=[],t=0;t<256;++t)e.push("%"+((t<16?"0":"")+t.toString(16)).toUpperCase());return e}(),uk=function(t){for(;t.length>1;){var r=t.pop(),a=r.obj[r.prop];if(le(a)){for(var n=[],o=0;o<a.length;++o)typeof a[o]<"u"&&n.push(a[o]);r.obj[r.prop]=n}}},ii=function(t,r){for(var a=r&&r.plainObjects?Object.create(null):{},n=0;n<t.length;++n)typeof t[n]<"u"&&(a[n]=t[n]);return a},ck=function e(t,r,a){if(!r)return t;if(typeof r!="object"){if(le(t))t.push(r);else if(t&&typeof t=="object")(a&&(a.plainObjects||a.allowPrototypes)||!Ms.call(Object.prototype,r))&&(t[r]=!0);else return[t,r];return t}if(!t||typeof t!="object")return[t].concat(r);var n=t;return le(t)&&!le(r)&&(n=ii(t,a)),le(t)&&le(r)?(r.forEach(function(o,i){if(Ms.call(t,i)){var u=t[i];u&&typeof u=="object"&&o&&typeof o=="object"?t[i]=e(u,o,a):t.push(o)}else t[i]=o}),t):Object.keys(r).reduce(function(o,i){var u=r[i];return Ms.call(o,i)?o[i]=e(o[i],u,a):o[i]=u,o},n)},dk=function(t,r){return Object.keys(r).reduce(function(a,n){return a[n]=r[n],a},t)},lk=function(e,t,r){var a=e.replace(/\+/g," ");if(r==="iso-8859-1")return a.replace(/%[0-9a-f]{2}/gi,unescape);try{return decodeURIComponent(a)}catch{return a}},gk=function(t,r,a,n,o){if(t.length===0)return t;var i=t;if(typeof t=="symbol"?i=Symbol.prototype.toString.call(t):typeof t!="string"&&(i=String(t)),a==="iso-8859-1")return escape(i).replace(/%u[0-9a-f]{4}/gi,function(y){return"%26%23"+parseInt(y.slice(2),16)+"%3B"});for(var u="",p=0;p<i.length;++p){var s=i.charCodeAt(p);if(s===45||s===46||s===95||s===126||s>=48&&s<=57||s>=65&&s<=90||s>=97&&s<=122||o===pk.RFC1738&&(s===40||s===41)){u+=i.charAt(p);continue}if(s<128){u=u+j[s];continue}if(s<2048){u=u+(j[192|s>>6]+j[128|s&63]);continue}if(s<55296||s>=57344){u=u+(j[224|s>>12]+j[128|s>>6&63]+j[128|s&63]);continue}p+=1,s=65536+((s&1023)<<10|i.charCodeAt(p)&1023),u+=j[240|s>>18]+j[128|s>>12&63]+j[128|s>>6&63]+j[128|s&63]}return u},yk=function(t){for(var r=[{obj:{o:t},prop:"o"}],a=[],n=0;n<r.length;++n)for(var o=r[n],i=o.obj[o.prop],u=Object.keys(i),p=0;p<u.length;++p){var s=u[p],y=i[s];typeof y=="object"&&y!==null&&a.indexOf(y)===-1&&(r.push({obj:i,prop:s}),a.push(y))}return uk(r),t},Rk=function(t){return Object.prototype.toString.call(t)==="[object RegExp]"},mk=function(t){return!t||typeof t!="object"?!1:!!(t.constructor&&t.constructor.isBuffer&&t.constructor.isBuffer(t))},qk=function(t,r){return[].concat(t,r)},hk=function(t,r){if(le(t)){for(var a=[],n=0;n<t.length;n+=1)a.push(r(t[n]));return a}return r(t)};pi.exports={arrayToObject:ii,assign:dk,combine:qk,compact:yk,decode:lk,encode:gk,isBuffer:mk,isRegExp:Rk,maybeMap:hk,merge:ck}});var yi=R((iv,gi)=>{"use strict";var di=ni(),Ns=Os(),Ne=Ut(),bk=Object.prototype.hasOwnProperty,ui={brackets:function(t){return t+"[]"},comma:"comma",indices:function(t,r){return t+"["+r+"]"},repeat:function(t){return t}},X=Array.isArray,fk=String.prototype.split,xk=Array.prototype.push,li=function(e,t){xk.apply(e,X(t)?t:[t])},Ik=Date.prototype.toISOString,ci=Ne.default,S={addQueryPrefix:!1,allowDots:!1,charset:"utf-8",charsetSentinel:!1,delimiter:"&",encode:!0,encoder:Ns.encode,encodeValuesOnly:!1,format:ci,formatter:Ne.formatters[ci],indices:!1,serializeDate:function(t){return Ik.call(t)},skipNulls:!1,strictNullHandling:!1},kk=function(t){return typeof t=="string"||typeof t=="number"||typeof t=="boolean"||typeof t=="symbol"||typeof t=="bigint"},Vs={},Pk=function e(t,r,a,n,o,i,u,p,s,y,P,b,k,I,G,U){for(var x=t,z=U,ee=0,me=!1;(z=z.get(Vs))!==void 0&&!me;){var we=z.get(t);if(ee+=1,typeof we<"u"){if(we===ee)throw new RangeError("Cyclic object value");me=!0}typeof z.get(Vs)>"u"&&(ee=0)}if(typeof p=="function"?x=p(r,x):x instanceof Date?x=P(x):a==="comma"&&X(x)&&(x=Ns.maybeMap(x,function(Ht){return Ht instanceof Date?P(Ht):Ht})),x===null){if(o)return u&&!I?u(r,S.encoder,G,"key",b):r;x=""}if(kk(x)||Ns.isBuffer(x)){if(u){var Ue=I?r:u(r,S.encoder,G,"key",b);if(a==="comma"&&I){for(var ie=fk.call(String(x),","),Ge="",te=0;te<ie.length;++te)Ge+=(te===0?"":",")+k(u(ie[te],S.encoder,G,"value",b));return[k(Ue)+(n&&X(x)&&ie.length===1?"[]":"")+"="+Ge]}return[k(Ue)+"="+k(u(x,S.encoder,G,"value",b))]}return[k(r)+"="+k(String(x))]}var qe=[];if(typeof x>"u")return qe;var pe;if(a==="comma"&&X(x))pe=[{value:x.length>0?x.join(",")||null:void 0}];else if(X(p))pe=p;else{var he=Object.keys(x);pe=s?he.sort(s):he}for(var N=n&&X(x)&&x.length===1?r+"[]":r,se=0;se<pe.length;++se){var J=pe[se],Ee=typeof J=="object"&&typeof J.value<"u"?J.value:x[J];if(!(i&&Ee===null)){var Qf=X(x)?typeof a=="function"?a(N,J):N:N+(y?"."+J:"["+J+"]");U.set(t,ee);var Pa=di();Pa.set(Vs,U),li(qe,e(Ee,Qf,a,n,o,i,u,p,s,y,P,b,k,I,G,Pa))}}return qe},vk=function(t){if(!t)return S;if(t.encoder!==null&&typeof t.encoder<"u"&&typeof t.encoder!="function")throw new TypeError("Encoder has to be a function.");var r=t.charset||S.charset;if(typeof t.charset<"u"&&t.charset!=="utf-8"&&t.charset!=="iso-8859-1")throw new TypeError("The charset option must be either utf-8, iso-8859-1, or undefined");var a=Ne.default;if(typeof t.format<"u"){if(!bk.call(Ne.formatters,t.format))throw new TypeError("Unknown format option provided.");a=t.format}var n=Ne.formatters[a],o=S.filter;return(typeof t.filter=="function"||X(t.filter))&&(o=t.filter),{addQueryPrefix:typeof t.addQueryPrefix=="boolean"?t.addQueryPrefix:S.addQueryPrefix,allowDots:typeof t.allowDots>"u"?S.allowDots:!!t.allowDots,charset:r,charsetSentinel:typeof t.charsetSentinel=="boolean"?t.charsetSentinel:S.charsetSentinel,delimiter:typeof t.delimiter>"u"?S.delimiter:t.delimiter,encode:typeof t.encode=="boolean"?t.encode:S.encode,encoder:typeof t.encoder=="function"?t.encoder:S.encoder,encodeValuesOnly:typeof t.encodeValuesOnly=="boolean"?t.encodeValuesOnly:S.encodeValuesOnly,filter:o,format:a,formatter:n,serializeDate:typeof t.serializeDate=="function"?t.serializeDate:S.serializeDate,skipNulls:typeof t.skipNulls=="boolean"?t.skipNulls:S.skipNulls,sort:typeof t.sort=="function"?t.sort:null,strictNullHandling:typeof t.strictNullHandling=="boolean"?t.strictNullHandling:S.strictNullHandling}};gi.exports=function(e,t){var r=e,a=vk(t),n,o;typeof a.filter=="function"?(o=a.filter,r=o("",r)):X(a.filter)&&(o=a.filter,n=o);var i=[];if(typeof r!="object"||r===null)return"";var u;t&&t.arrayFormat in ui?u=t.arrayFormat:t&&"indices"in t?u=t.indices?"indices":"repeat":u="indices";var p=ui[u];if(t&&"commaRoundTrip"in t&&typeof t.commaRoundTrip!="boolean")throw new TypeError("`commaRoundTrip` must be a boolean, or absent");var s=p==="comma"&&t&&t.commaRoundTrip;n||(n=Object.keys(r)),a.sort&&n.sort(a.sort);for(var y=di(),P=0;P<n.length;++P){var b=n[P];a.skipNulls&&r[b]===null||li(i,Pk(r[b],b,p,s,a.strictNullHandling,a.skipNulls,a.encode?a.encoder:null,a.filter,a.sort,a.allowDots,a.serializeDate,a.format,a.formatter,a.encodeValuesOnly,a.charset,y))}var k=i.join(a.delimiter),I=a.addQueryPrefix===!0?"?":"";return a.charsetSentinel&&(a.charset==="iso-8859-1"?I+="utf8=%26%2310003%3B&":I+="utf8=%E2%9C%93&"),k.length>0?I+k:""}});var qi=R((pv,mi)=>{"use strict";var Be=Os(),Ks=Object.prototype.hasOwnProperty,Ak=Array.isArray,L={allowDots:!1,allowPrototypes:!1,allowSparse:!1,arrayLimit:20,charset:"utf-8",charsetSentinel:!1,comma:!1,decoder:Be.decode,delimiter:"&",depth:5,ignoreQueryPrefix:!1,interpretNumericEntities:!1,parameterLimit:1e3,parseArrays:!0,plainObjects:!1,strictNullHandling:!1},Tk=function(e){return e.replace(/&#(\d+);/g,function(t,r){return String.fromCharCode(parseInt(r,10))})},Ri=function(e,t){return e&&typeof e=="string"&&t.comma&&e.indexOf(",")>-1?e.split(","):e},Bk="utf8=%26%2310003%3B",Ck="utf8=%E2%9C%93",wk=function(t,r){var a={},n=r.ignoreQueryPrefix?t.replace(/^\?/,""):t,o=r.parameterLimit===1/0?void 0:r.parameterLimit,i=n.split(r.delimiter,o),u=-1,p,s=r.charset;if(r.charsetSentinel)for(p=0;p<i.length;++p)i[p].indexOf("utf8=")===0&&(i[p]===Ck?s="utf-8":i[p]===Bk&&(s="iso-8859-1"),u=p,p=i.length);for(p=0;p<i.length;++p)if(p!==u){var y=i[p],P=y.indexOf("]="),b=P===-1?y.indexOf("="):P+1,k,I;b===-1?(k=r.decoder(y,L.decoder,s,"key"),I=r.strictNullHandling?null:""):(k=r.decoder(y.slice(0,b),L.decoder,s,"key"),I=Be.maybeMap(Ri(y.slice(b+1),r),function(G){return r.decoder(G,L.decoder,s,"value")})),I&&r.interpretNumericEntities&&s==="iso-8859-1"&&(I=Tk(I)),y.indexOf("[]=")>-1&&(I=Ak(I)?[I]:I),Ks.call(a,k)?a[k]=Be.combine(a[k],I):a[k]=I}return a},Uk=function(e,t,r,a){for(var n=a?t:Ri(t,r),o=e.length-1;o>=0;--o){var i,u=e[o];if(u==="[]"&&r.parseArrays)i=[].concat(n);else{i=r.plainObjects?Object.create(null):{};var p=u.charAt(0)==="["&&u.charAt(u.length-1)==="]"?u.slice(1,-1):u,s=parseInt(p,10);!r.parseArrays&&p===""?i={0:n}:!isNaN(s)&&u!==p&&String(s)===p&&s>=0&&r.parseArrays&&s<=r.arrayLimit?(i=[],i[s]=n):p!=="__proto__"&&(i[p]=n)}n=i}return n},Gk=function(t,r,a,n){if(t){var o=a.allowDots?t.replace(/\.([^.[]+)/g,"[$1]"):t,i=/(\[[^[\]]*])/,u=/(\[[^[\]]*])/g,p=a.depth>0&&i.exec(o),s=p?o.slice(0,p.index):o,y=[];if(s){if(!a.plainObjects&&Ks.call(Object.prototype,s)&&!a.allowPrototypes)return;y.push(s)}for(var P=0;a.depth>0&&(p=u.exec(o))!==null&&P<a.depth;){if(P+=1,!a.plainObjects&&Ks.call(Object.prototype,p[1].slice(1,-1))&&!a.allowPrototypes)return;y.push(p[1])}return p&&y.push("["+o.slice(p.index)+"]"),Uk(y,r,a,n)}},Ek=function(t){if(!t)return L;if(t.decoder!==null&&t.decoder!==void 0&&typeof t.decoder!="function")throw new TypeError("Decoder has to be a function.");if(typeof t.charset<"u"&&t.charset!=="utf-8"&&t.charset!=="iso-8859-1")throw new TypeError("The charset option must be either utf-8, iso-8859-1, or undefined");var r=typeof t.charset>"u"?L.charset:t.charset;return{allowDots:typeof t.allowDots>"u"?L.allowDots:!!t.allowDots,allowPrototypes:typeof t.allowPrototypes=="boolean"?t.allowPrototypes:L.allowPrototypes,allowSparse:typeof t.allowSparse=="boolean"?t.allowSparse:L.allowSparse,arrayLimit:typeof t.arrayLimit=="number"?t.arrayLimit:L.arrayLimit,charset:r,charsetSentinel:typeof t.charsetSentinel=="boolean"?t.charsetSentinel:L.charsetSentinel,comma:typeof t.comma=="boolean"?t.comma:L.comma,decoder:typeof t.decoder=="function"?t.decoder:L.decoder,delimiter:typeof t.delimiter=="string"||Be.isRegExp(t.delimiter)?t.delimiter:L.delimiter,depth:typeof t.depth=="number"||t.depth===!1?+t.depth:L.depth,ignoreQueryPrefix:t.ignoreQueryPrefix===!0,interpretNumericEntities:typeof t.interpretNumericEntities=="boolean"?t.interpretNumericEntities:L.interpretNumericEntities,parameterLimit:typeof t.parameterLimit=="number"?t.parameterLimit:L.parameterLimit,parseArrays:t.parseArrays!==!1,plainObjects:typeof t.plainObjects=="boolean"?t.plainObjects:L.plainObjects,strictNullHandling:typeof t.strictNullHandling=="boolean"?t.strictNullHandling:L.strictNullHandling}};mi.exports=function(e,t){var r=Ek(t);if(e===""||e===null||typeof e>"u")return r.plainObjects?Object.create(null):{};for(var a=typeof e=="string"?wk(e,r):e,n=r.plainObjects?Object.create(null):{},o=Object.keys(a),i=0;i<o.length;++i){var u=o[i],p=Gk(u,a[u],r,typeof e=="string");n=Be.merge(n,p,r)}return r.allowSparse===!0?n:Be.compact(n)}});var Ce=R((uv,hi)=>{"use strict";var Lk=yi(),Wk=qi(),Sk=Ut();hi.exports={formats:Sk,parse:Wk,stringify:Lk}});var pP={};K(pP,{AlreadyExistsError:()=>gt,BreakingChangesError:()=>ht,Client:()=>ka,ForbiddenError:()=>Ze,InternalError:()=>Je,InvalidDataFormatError:()=>nt,InvalidIdentifierError:()=>ot,InvalidJsonSchemaError:()=>at,InvalidPayloadError:()=>et,InvalidQueryError:()=>dt,LimitExceededError:()=>qt,MethodNotFoundError:()=>st,PayloadTooLargeError:()=>Xe,PaymentRequiredError:()=>Rt,QuotaExceededError:()=>mt,RateLimitedError:()=>yt,ReferenceConstraintError:()=>pt,ReferenceNotFoundError:()=>ct,RelationConflictError:()=>it,ResourceLockedConflictError:()=>ut,ResourceNotFoundError:()=>rt,RuntimeError:()=>lt,UnauthorizedError:()=>Ye,UnknownError:()=>ae,UnsupportedMediaTypeError:()=>tt,UploadFileError:()=>Y,admin:()=>xr,axios:()=>uP,axiosRetry:()=>Mt,errorFrom:()=>ue,files:()=>zr,isApiError:()=>Va,runtime:()=>js,tables:()=>Ia});module.exports=Of(pP);var uP=w(require("axios"));var Mt={};K(Mt,{DEFAULT_OPTIONS:()=>La,default:()=>V,exponentialDelay:()=>Ga,isIdempotentRequestError:()=>_t,isNetworkError:()=>Dt,isNetworkOrIdempotentRequestError:()=>Ft,isRetryableError:()=>je,isSafeRequestError:()=>Ua,linearDelay:()=>Ea,namespace:()=>$e,retryAfter:()=>ze});var Ca=w(Ta(),1),$e="axios-retry";function Dt(e){let t=["ERR_CANCELED","ECONNABORTED"];return e.response||!e.code||t.includes(e.code)?!1:(0,Ca.default)(e)}var wa=["get","head","options"],Nf=wa.concat(["put","delete"]);function je(e){return e.code!=="ECONNABORTED"&&(!e.response||e.response.status===429||e.response.status>=500&&e.response.status<=599)}function Ua(e){return e.config?.method?je(e)&&wa.indexOf(e.config.method)!==-1:!1}function _t(e){return e.config?.method?je(e)&&Nf.indexOf(e.config.method)!==-1:!1}function Ft(e){return Dt(e)||_t(e)}function ze(e=void 0){let t=e?.response?.headers["retry-after"];if(!t)return 0;let r=(Number(t)||0)*1e3;return r===0&&(r=(new Date(t).valueOf()||0)-Date.now()),Math.max(0,r)}function Kf(e=0,t=void 0){return Math.max(0,ze(t))}function Ga(e=0,t=void 0,r=100){let a=2**e*r,n=Math.max(a,ze(t)),o=n*.2*Math.random();return n+o}function Ea(e=100){return(t=0,r=void 0)=>{let a=t*e;return Math.max(a,ze(r))}}var La={retries:3,retryCondition:Ft,retryDelay:Kf,shouldResetTimeout:!1,onRetry:()=>{},onMaxRetryTimesExceeded:()=>{},validateResponse:null};function $f(e,t){return{...La,...t,...e[$e]}}function Ba(e,t,r=!1){let a=$f(e,t||{});return a.retryCount=a.retryCount||0,(!a.lastRequestTime||r)&&(a.lastRequestTime=Date.now()),e[$e]=a,a}function jf(e,t){e.defaults.agent===t.agent&&delete t.agent,e.defaults.httpAgent===t.httpAgent&&delete t.httpAgent,e.defaults.httpsAgent===t.httpsAgent&&delete t.httpsAgent}async function zf(e,t){let{retries:r,retryCondition:a}=e,n=(e.retryCount||0)<r&&a(t);if(typeof n=="object")try{return await n!==!1}catch{return!1}return n}async function Jf(e,t,r,a){t.retryCount+=1;let{retryDelay:n,shouldResetTimeout:o,onRetry:i}=t,u=n(t.retryCount,r);if(jf(e,a),!o&&a.timeout&&t.lastRequestTime){let p=Date.now()-t.lastRequestTime,s=a.timeout-p-u;if(s<=0)return Promise.reject(r);a.timeout=s}return a.transformRequest=[p=>p],await i(t.retryCount,r,a),a.signal?.aborted?Promise.resolve(e(a)):new Promise(p=>{let s=()=>{clearTimeout(y),p(e(a))},y=setTimeout(()=>{p(e(a)),a.signal?.removeEventListener&&a.signal.removeEventListener("abort",s)},u);a.signal?.addEventListener&&a.signal.addEventListener("abort",s,{once:!0})})}async function Yf(e,t){e.retryCount>=e.retries&&await e.onMaxRetryTimesExceeded(t,e.retryCount)}var re=(e,t)=>{let r=e.interceptors.request.use(n=>(Ba(n,t,!0),n[$e]?.validateResponse&&(n.validateStatus=()=>!1),n)),a=e.interceptors.response.use(null,async n=>{let{config:o}=n;if(!o)return Promise.reject(n);let i=Ba(o,t);return n.response&&i.validateResponse?.(n.response)?n.response:await zf(i,n)?Jf(e,i,n,o):(await Yf(i,n),Promise.reject(n))});return{requestInterceptorId:r,responseInterceptorId:a}};re.isNetworkError=Dt;re.isSafeRequestError=Ua;re.isIdempotentRequestError=_t;re.isNetworkOrIdempotentRequestError=Ft;re.exponentialDelay=Ga;re.linearDelay=Ea;re.isRetryableError=je;var V=re;var js={};K(js,{Client:()=>$s});var ru=w(require("axios"));var Ot=require("browser-or-node"),Wa=w(require("http")),Sa=w(require("https")),Qa=100*1024*1024,Ha=Qa,Da=Qa,_a=Ot.isNode?new Wa.default.Agent({keepAlive:!0}):void 0,Fa=Ot.isNode?new Sa.default.Agent({keepAlive:!0}):void 0;var F={};K(F,{getClientConfig:()=>nx});var Le=require("browser-or-node"),Zf="https://api.botpress.cloud",Xf=6e4,ex="BP_API_URL",tx="BP_BOT_ID",sx="BP_INTEGRATION_ID",rx="BP_WORKSPACE_ID",ax="BP_TOKEN";function nx(e){let t=ox(e),r={};t.workspaceId&&(r["x-workspace-id"]=t.workspaceId),t.botId&&(r["x-bot-id"]=t.botId),t.integrationId&&(r["x-integration-id"]=t.integrationId),t.token&&(r.Authorization=`Bearer ${t.token}`),r={...r,...t.headers};let a=t.apiUrl??Zf,n=t.timeout??Xf;return{apiUrl:a,timeout:n,withCredentials:Le.isBrowser,headers:r}}function ox(e){return Le.isBrowser?e:Le.isNode?ix(e):e}function ix(e){let t={...e,apiUrl:e.apiUrl??process.env[ex],botId:e.botId??process.env[tx],integrationId:e.integrationId??process.env[sx],workspaceId:e.workspaceId??process.env[rx]},r=t.token??process.env[ax];return r&&(t.token=r),t}var h={};K(h,{AsyncCollection:()=>Vt});var Vt=class{constructor(t){this._list=t}async*[Symbol.asyncIterator](){let t;do{let{items:r,meta:a}=await this._list({nextToken:t});t=a.nextToken;for(let n of r)yield n}while(t)}async collect(t={}){let r=t.limit??Number.POSITIVE_INFINITY,a=[],n=0;for await(let o of this)if(a.push(o),n++,n>=r)break;return a}};var M={};K(M,{createAxios:()=>px});var px=e=>({baseURL:e.apiUrl,headers:e.headers,withCredentials:e.withCredentials,timeout:e.timeout,maxBodyLength:Ha,maxContentLength:Da,httpAgent:_a,httpsAgent:Fa});var O={};K(O,{toApiError:()=>gx});var Ka=w(require("axios"));var Oa=w(require("crypto"));var ux={getRandomValues:e=>new Uint8Array(e.map(()=>Math.floor(Math.random()*256)))},Nt=typeof window<"u"&&typeof window.document<"u"?window.crypto:Oa.default;Nt.getRandomValues||(Nt=ux);var v=class extends Error{constructor(r,a,n,o,i,u,p){super(o);this.code=r;this.description=a;this.type=n;this.message=o;this.error=i;this.id=u;this.metadata=p;this.id||(this.id=v.generateId())}isApiError=!0;format(){return`[${this.type}] ${this.message} (Error ID: ${this.id})`}toJSON(){return{id:this.id,code:this.code,type:this.type,message:this.message,metadata:this.metadata}}static generateId(){let r=this.getPrefix(),a=new Date().toISOString().replace(/[\-:TZ]/g,"").split(".")[0],n=4,o=Array.from(Nt.getRandomValues(new Uint8Array(n))).map(i=>i.toString(16).padStart(2,"0")).join("").toUpperCase();return`${r}_${a}x${o}`}static getPrefix(){return typeof window<"u"&&typeof window.document<"u"?"err_bwsr":"err"}},cx=e=>typeof e=="object"&&!Array.isArray(e)&&e!==null,Va=e=>e instanceof v||cx(e)&&e.isApiError===!0,ae=class extends v{constructor(t,r,a,n){super(500,"An unknown error occurred","Unknown",t,r,a,n)}},Je=class extends v{constructor(t,r,a,n){super(500,"An internal error occurred","Internal",t,r,a,n)}},Ye=class extends v{constructor(t,r,a,n){super(401,"The request requires to be authenticated.","Unauthorized",t,r,a,n)}},Ze=class extends v{constructor(t,r,a,n){super(403,"The requested action can't be peform by this resource.","Forbidden",t,r,a,n)}},Xe=class extends v{constructor(t,r,a,n){super(413,"The request payload is too large.","PayloadTooLarge",t,r,a,n)}},et=class extends v{constructor(t,r,a,n){super(400,"The request payload is invalid.","InvalidPayload",t,r,a,n)}},tt=class extends v{constructor(t,r,a,n){super(415,"The request is invalid because the content-type is not supported.","UnsupportedMediaType",t,r,a,n)}},st=class extends v{constructor(t,r,a,n){super(405,"The requested method does not exist.","MethodNotFound",t,r,a,n)}},rt=class extends v{constructor(t,r,a,n){super(404,"The requested resource does not exist.","ResourceNotFound",t,r,a,n)}},at=class extends v{constructor(t,r,a,n){super(400,"The provided JSON schema is invalid.","InvalidJsonSchema",t,r,a,n)}},nt=class extends v{constructor(t,r,a,n){super(400,"The provided data doesn't respect the provided JSON schema.","InvalidDataFormat",t,r,a,n)}},ot=class extends v{constructor(t,r,a,n){super(400,"The provided identifier is not valid. An identifier must start with a lowercase letter, be between 2 and 100 characters long and use only alphanumeric characters.","InvalidIdentifier",t,r,a,n)}},it=class extends v{constructor(t,r,a,n){super(409,"The resource is related with a different resource that the one referenced in the request. This is usually caused when providing two resource identifiers that aren't linked together.","RelationConflict",t,r,a,n)}},pt=class extends v{constructor(t,r,a,n){super(409,"The resource cannot be deleted because it's referenced by another resource","ReferenceConstraint",t,r,a,n)}},ut=class extends v{constructor(t,r,a,n){super(409,"The resource is current locked and cannot be operated on until the lock is released.","ResourceLockedConflict",t,r,a,n)}},ct=class extends v{constructor(t,r,a,n){super(400,"The provided resource reference is missing. This is usually caused when providing an invalid id inside the payload of a request.","ReferenceNotFound",t,r,a,n)}},dt=class extends v{constructor(t,r,a,n){super(400,"The provided query is invalid. This is usually caused when providing an invalid parameter for querying a resource.","InvalidQuery",t,r,a,n)}},lt=class extends v{constructor(t,r,a,n){super(400,"An error happened during the execution of a runtime (bot or integration).","Runtime",t,r,a,n)}},gt=class extends v{constructor(t,r,a,n){super(409,"The record attempted to be created already exists.","AlreadyExists",t,r,a,n)}},yt=class extends v{constructor(t,r,a,n){super(429,"The request has been rate limited.","RateLimited",t,r,a,n)}},Rt=class extends v{constructor(t,r,a,n){super(402,"A payment is required to perform this request.","PaymentRequired",t,r,a,n)}},mt=class extends v{constructor(t,r,a,n){super(403,"The request exceeds the allowed quota. Quotas are a soft limit that can be increased.","QuotaExceeded",t,r,a,n)}},qt=class extends v{constructor(t,r,a,n){super(413,"The request exceeds the allowed limit. Limits are a hard limit that cannot be increased.","LimitExceeded",t,r,a,n)}},ht=class extends v{constructor(t,r,a,n){super(400,"Request payload contains breaking changes which is not allowed for this resource without a version increment.","BreakingChanges",t,r,a,n)}},dx={Unknown:ae,Internal:Je,Unauthorized:Ye,Forbidden:Ze,PayloadTooLarge:Xe,InvalidPayload:et,UnsupportedMediaType:tt,MethodNotFound:st,ResourceNotFound:rt,InvalidJsonSchema:at,InvalidDataFormat:nt,InvalidIdentifier:ot,RelationConflict:it,ReferenceConstraint:pt,ResourceLockedConflict:ut,ReferenceNotFound:ct,InvalidQuery:dt,Runtime:lt,AlreadyExists:gt,RateLimited:yt,PaymentRequired:Rt,QuotaExceeded:mt,LimitExceeded:qt,BreakingChanges:ht},ue=e=>Va(e)?e:e instanceof Error?new ae(e.message,e):typeof e=="string"?new ae(e):lx(e);function lx(e){if(typeof e=="object"&&"code"in e&&"type"in e&&"id"in e&&"message"in e&&typeof e.type=="string"&&typeof e.message=="string"){let t=dx[e.type];return t?new t(e.message,void 0,e.id||"UNKNOWN",e.metadata):new ae(`An unclassified API error occurred: ${e.message} (Type: ${e.type}, Code: ${e.code})`)}return new ae("An invalid error occurred: "+JSON.stringify(e))}var Y=class extends Error{constructor(r,a,n){super(r);this.innerError=a;this.file=n;this.name="FileUploadError"}};var gx=e=>Ka.default.isAxiosError(e)&&e.response?.data?ue(e.response.data):ue(e);var su=w(require("axios"));var $a=w(require("crypto"));var yx={getRandomValues:e=>new Uint8Array(e.map(()=>Math.floor(Math.random()*256)))},Kt=typeof window<"u"&&typeof window.document<"u"?window.crypto:$a.default;Kt.getRandomValues||(Kt=yx);var A=class extends Error{constructor(r,a,n,o,i,u,p){super(o);this.code=r;this.description=a;this.type=n;this.message=o;this.error=i;this.id=u;this.metadata=p;this.id||(this.id=A.generateId())}isApiError=!0;format(){return`[${this.type}] ${this.message} (Error ID: ${this.id})`}toJSON(){return{id:this.id,code:this.code,type:this.type,message:this.message,metadata:this.metadata}}static generateId(){let r=this.getPrefix(),a=new Date().toISOString().replace(/[\-:TZ]/g,"").split(".")[0],n=4,o=Array.from(Kt.getRandomValues(new Uint8Array(n))).map(i=>i.toString(16).padStart(2,"0")).join("").toUpperCase();return`${r}_${a}x${o}`}static getPrefix(){return typeof window<"u"&&typeof window.document<"u"?"err_bwsr":"err"}},Rx=e=>typeof e=="object"&&!Array.isArray(e)&&e!==null,mx=e=>e instanceof A||Rx(e)&&e.isApiError===!0,ce=class extends A{constructor(t,r,a,n){super(500,"An unknown error occurred","Unknown",t,r,a,n)}},$t=class extends A{constructor(t,r,a,n){super(500,"An internal error occurred","Internal",t,r,a,n)}},jt=class extends A{constructor(t,r,a,n){super(401,"The request requires to be authenticated.","Unauthorized",t,r,a,n)}},zt=class extends A{constructor(t,r,a,n){super(403,"The requested action can't be peform by this resource.","Forbidden",t,r,a,n)}},Jt=class extends A{constructor(t,r,a,n){super(413,"The request payload is too large.","PayloadTooLarge",t,r,a,n)}},Yt=class extends A{constructor(t,r,a,n){super(400,"The request payload is invalid.","InvalidPayload",t,r,a,n)}},Zt=class extends A{constructor(t,r,a,n){super(415,"The request is invalid because the content-type is not supported.","UnsupportedMediaType",t,r,a,n)}},Xt=class extends A{constructor(t,r,a,n){super(405,"The requested method does not exist.","MethodNotFound",t,r,a,n)}},es=class extends A{constructor(t,r,a,n){super(404,"The requested resource does not exist.","ResourceNotFound",t,r,a,n)}},ts=class extends A{constructor(t,r,a,n){super(400,"The provided JSON schema is invalid.","InvalidJsonSchema",t,r,a,n)}},ss=class extends A{constructor(t,r,a,n){super(400,"The provided data doesn't respect the provided JSON schema.","InvalidDataFormat",t,r,a,n)}},rs=class extends A{constructor(t,r,a,n){super(400,"The provided identifier is not valid. An identifier must start with a lowercase letter, be between 2 and 100 characters long and use only alphanumeric characters.","InvalidIdentifier",t,r,a,n)}},as=class extends A{constructor(t,r,a,n){super(409,"The resource is related with a different resource that the one referenced in the request. This is usually caused when providing two resource identifiers that aren't linked together.","RelationConflict",t,r,a,n)}},ns=class extends A{constructor(t,r,a,n){super(409,"The resource cannot be deleted because it's referenced by another resource","ReferenceConstraint",t,r,a,n)}},os=class extends A{constructor(t,r,a,n){super(409,"The resource is current locked and cannot be operated on until the lock is released.","ResourceLockedConflict",t,r,a,n)}},is=class extends A{constructor(t,r,a,n){super(400,"The provided resource reference is missing. This is usually caused when providing an invalid id inside the payload of a request.","ReferenceNotFound",t,r,a,n)}},ps=class extends A{constructor(t,r,a,n){super(400,"The provided query is invalid. This is usually caused when providing an invalid parameter for querying a resource.","InvalidQuery",t,r,a,n)}},us=class extends A{constructor(t,r,a,n){super(400,"An error happened during the execution of a runtime (bot or integration).","Runtime",t,r,a,n)}},cs=class extends A{constructor(t,r,a,n){super(409,"The record attempted to be created already exists.","AlreadyExists",t,r,a,n)}},ds=class extends A{constructor(t,r,a,n){super(429,"The request has been rate limited.","RateLimited",t,r,a,n)}},ls=class extends A{constructor(t,r,a,n){super(402,"A payment is required to perform this request.","PaymentRequired",t,r,a,n)}},gs=class extends A{constructor(t,r,a,n){super(403,"The request exceeds the allowed quota. Quotas are a soft limit that can be increased.","QuotaExceeded",t,r,a,n)}},ys=class extends A{constructor(t,r,a,n){super(413,"The request exceeds the allowed limit. Limits are a hard limit that cannot be increased.","LimitExceeded",t,r,a,n)}},Rs=class extends A{constructor(t,r,a,n){super(400,"Request payload contains breaking changes which is not allowed for this resource without a version increment.","BreakingChanges",t,r,a,n)}},qx={Unknown:ce,Internal:$t,Unauthorized:jt,Forbidden:zt,PayloadTooLarge:Jt,InvalidPayload:Yt,UnsupportedMediaType:Zt,MethodNotFound:Xt,ResourceNotFound:es,InvalidJsonSchema:ts,InvalidDataFormat:ss,InvalidIdentifier:rs,RelationConflict:as,ReferenceConstraint:ns,ResourceLockedConflict:os,ReferenceNotFound:is,InvalidQuery:ps,Runtime:us,AlreadyExists:cs,RateLimited:ds,PaymentRequired:ls,QuotaExceeded:gs,LimitExceeded:ys,BreakingChanges:Rs},ms=e=>mx(e)?e:e instanceof Error?new ce(e.message,e):typeof e=="string"?new ce(e):hx(e);function hx(e){if(typeof e=="object"&&"code"in e&&"type"in e&&"id"in e&&"message"in e&&typeof e.type=="string"&&typeof e.message=="string"){let t=qx[e.type];return t?new t(e.message,void 0,e.id||"UNKNOWN",e.metadata):new ce(`An unclassified API error occurred: ${e.message} (Type: ${e.type}, Code: ${e.code})`)}return new ce("An invalid error occurred: "+JSON.stringify(e))}var bi=w(Ce()),Qk=e=>e[1]!==void 0,m=e=>{let{method:t,path:r,query:a,headers:n,body:o}=e,i=Object.entries(n).filter(Qk),u=Object.fromEntries(i),p=bi.default.stringify(a,{encode:!0,arrayFormat:"repeat",allowDots:!0}),s=p?[r,p].join("?"):r,y=["put","post","delete","patch"].includes(t.toLowerCase())?o:void 0;return{method:t,url:s,headers:u,data:y}};var fi=e=>({path:"/v1/chat/conversations",headers:{},query:{},params:{},body:{channel:e.channel,tags:e.tags,integrationName:e.integrationName}});var Ii=e=>({path:`/v1/chat/conversations/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var Pi=e=>({path:"/v1/chat/conversations",headers:{},query:{nextToken:e.nextToken,sortField:e.sortField,sortDirection:e.sortDirection,tags:e.tags,participantIds:e.participantIds,integrationName:e.integrationName,channel:e.channel},params:{},body:{}});var Ai=e=>({path:"/v1/chat/conversations/get-or-create",headers:{},query:{},params:{},body:{channel:e.channel,tags:e.tags,integrationName:e.integrationName,discriminateByTags:e.discriminateByTags}});var Bi=e=>({path:`/v1/chat/conversations/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{currentTaskId:e.currentTaskId,tags:e.tags}});var wi=e=>({path:`/v1/chat/conversations/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var Gi=e=>({path:`/v1/chat/conversations/${encodeURIComponent(e.id)}/participants`,headers:{},query:{nextToken:e.nextToken},params:{id:e.id},body:{}});var Li=e=>({path:`/v1/chat/conversations/${encodeURIComponent(e.id)}/participants`,headers:{},query:{},params:{id:e.id},body:{userId:e.userId}});var Si=e=>({path:`/v1/chat/conversations/${encodeURIComponent(e.id)}/participants/${encodeURIComponent(e.userId)}`,headers:{},query:{},params:{id:e.id,userId:e.userId},body:{}});var Hi=e=>({path:`/v1/chat/conversations/${encodeURIComponent(e.id)}/participants/${encodeURIComponent(e.userId)}`,headers:{},query:{},params:{id:e.id,userId:e.userId},body:{}});var _i=e=>({path:"/v1/chat/events",headers:{},query:{},params:{},body:{type:e.type,payload:e.payload,schedule:e.schedule,conversationId:e.conversationId,userId:e.userId,messageId:e.messageId}});var Mi=e=>({path:`/v1/chat/events/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var Vi=e=>({path:"/v1/chat/events",headers:{},query:{nextToken:e.nextToken,type:e.type,conversationId:e.conversationId,userId:e.userId,messageId:e.messageId,status:e.status},params:{},body:{}});var Ki=e=>({path:"/v1/chat/messages",headers:{},query:{},params:{},body:{payload:e.payload,userId:e.userId,conversationId:e.conversationId,type:e.type,tags:e.tags,schedule:e.schedule}});var ji=e=>({path:"/v1/chat/messages/get-or-create",headers:{},query:{},params:{},body:{payload:e.payload,userId:e.userId,conversationId:e.conversationId,type:e.type,tags:e.tags,schedule:e.schedule,discriminateByTags:e.discriminateByTags}});var Ji=e=>({path:`/v1/chat/messages/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var Zi=e=>({path:`/v1/chat/messages/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{tags:e.tags,payload:e.payload}});var ep=e=>({path:"/v1/chat/messages",headers:{},query:{nextToken:e.nextToken,conversationId:e.conversationId,tags:e.tags},params:{},body:{}});var sp=e=>({path:`/v1/chat/messages/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var ap=e=>({path:"/v1/chat/users",headers:{},query:{},params:{},body:{tags:e.tags,integrationName:e.integrationName,name:e.name,pictureUrl:e.pictureUrl}});var op=e=>({path:`/v1/chat/users/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var pp=e=>({path:"/v1/chat/users",headers:{},query:{nextToken:e.nextToken,conversationId:e.conversationId,tags:e.tags},params:{},body:{}});var cp=e=>({path:"/v1/chat/users/get-or-create",headers:{},query:{},params:{},body:{tags:e.tags,integrationName:e.integrationName,name:e.name,pictureUrl:e.pictureUrl,discriminateByTags:e.discriminateByTags}});var lp=e=>({path:`/v1/chat/users/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{tags:e.tags,name:e.name,pictureUrl:e.pictureUrl}});var yp=e=>({path:`/v1/chat/users/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var mp=e=>({path:`/v1/chat/states/${encodeURIComponent(e.type)}/${encodeURIComponent(e.id)}/${encodeURIComponent(e.name)}/expiry`,headers:{},query:{},params:{type:e.type,id:e.id,name:e.name},body:{expiry:e.expiry}});var hp=e=>({path:`/v1/chat/states/${encodeURIComponent(e.type)}/${encodeURIComponent(e.id)}/${encodeURIComponent(e.name)}`,headers:{},query:{},params:{type:e.type,id:e.id,name:e.name},body:{}});var fp=e=>({path:`/v1/chat/states/${encodeURIComponent(e.type)}/${encodeURIComponent(e.id)}/${encodeURIComponent(e.name)}`,headers:{},query:{},params:{type:e.type,id:e.id,name:e.name},body:{payload:e.payload,expiry:e.expiry}});var Ip=e=>({path:`/v1/chat/states/${encodeURIComponent(e.type)}/${encodeURIComponent(e.id)}/${encodeURIComponent(e.name)}/get-or-set`,headers:{},query:{},params:{type:e.type,id:e.id,name:e.name},body:{payload:e.payload,expiry:e.expiry}});var Pp=e=>({path:`/v1/chat/states/${encodeURIComponent(e.type)}/${encodeURIComponent(e.id)}/${encodeURIComponent(e.name)}`,headers:{},query:{},params:{type:e.type,id:e.id,name:e.name},body:{payload:e.payload}});var Ap=e=>({path:"/v1/chat/actions",headers:{},query:{},params:{},body:{type:e.type,input:e.input}});var Bp=e=>({path:"/v1/chat/integrations/configure",headers:{},query:{},params:{},body:{identifier:e.identifier,scheduleRegisterCall:e.scheduleRegisterCall,sandboxIdentifiers:e.sandboxIdentifiers}});var wp=e=>({path:`/v1/chat/tasks/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var Gp=e=>({path:"/v1/chat/tasks",headers:{},query:{},params:{},body:{title:e.title,description:e.description,type:e.type,data:e.data,parentTaskId:e.parentTaskId,conversationId:e.conversationId,userId:e.userId,timeoutAt:e.timeoutAt,tags:e.tags}});var Lp=e=>({path:`/v1/chat/tasks/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{title:e.title,description:e.description,data:e.data,timeoutAt:e.timeoutAt,status:e.status,tags:e.tags}});var Sp=e=>({path:`/v1/chat/tasks/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var Hp=e=>({path:"/v1/chat/tasks",headers:{},query:{nextToken:e.nextToken,tags:e.tags,conversationId:e.conversationId,userId:e.userId,parentTaskId:e.parentTaskId,status:e.status,type:e.type},params:{},body:{}});var _p=e=>({path:"/v1/chat/workflows",headers:{},query:{},params:{},body:{name:e.name,input:e.input,parentWorkflowId:e.parentWorkflowId,conversationId:e.conversationId,userId:e.userId,timeoutAt:e.timeoutAt,tags:e.tags,status:e.status,eventId:e.eventId}});var Mp=e=>({path:`/v1/chat/workflows/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var Vp=e=>({path:`/v1/chat/workflows/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{output:e.output,timeoutAt:e.timeoutAt,status:e.status,failureReason:e.failureReason,tags:e.tags,userId:e.userId,eventId:e.eventId}});var Kp=e=>({path:`/v1/chat/workflows/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var jp=e=>({path:"/v1/chat/workflows",headers:{},query:{nextToken:e.nextToken,tags:e.tags,conversationId:e.conversationId,userId:e.userId,parentWorkflowId:e.parentWorkflowId,statuses:e.statuses,name:e.name},params:{},body:{}});var Jp=e=>({path:"/v1/chat/workflows/get-or-create",headers:{},query:{},params:{},body:{name:e.name,input:e.input,parentWorkflowId:e.parentWorkflowId,conversationId:e.conversationId,userId:e.userId,timeoutAt:e.timeoutAt,tags:e.tags,status:e.status,eventId:e.eventId}});var Zp=e=>({path:`/v1/chat/tags/${encodeURIComponent(e.key)}/values`,headers:{},query:{nextToken:e.nextToken,type:e.type},params:{key:e.key},body:{}});var eu=e=>({path:"/v1/chat/analytics",headers:{},query:{},params:{},body:{name:e.name,count:e.count}});var Gt=class{constructor(t,r={}){this.axiosInstance=t;this.props=r}createConversation=async t=>{let{path:r,headers:a,query:n,body:o}=fi(t),i=this.props.toAxiosRequest??m,u=this.props.toApiError??q,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getConversation=async t=>{let{path:r,headers:a,query:n,body:o}=Ii(t),i=this.props.toAxiosRequest??m,u=this.props.toApiError??q,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};listConversations=async t=>{let{path:r,headers:a,query:n,body:o}=Pi(t),i=this.props.toAxiosRequest??m,u=this.props.toApiError??q,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getOrCreateConversation=async t=>{let{path:r,headers:a,query:n,body:o}=Ai(t),i=this.props.toAxiosRequest??m,u=this.props.toApiError??q,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};updateConversation=async t=>{let{path:r,headers:a,query:n,body:o}=Bi(t),i=this.props.toAxiosRequest??m,u=this.props.toApiError??q,p=i({method:"put",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};deleteConversation=async t=>{let{path:r,headers:a,query:n,body:o}=wi(t),i=this.props.toAxiosRequest??m,u=this.props.toApiError??q,p=i({method:"delete",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};listParticipants=async t=>{let{path:r,headers:a,query:n,body:o}=Gi(t),i=this.props.toAxiosRequest??m,u=this.props.toApiError??q,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};addParticipant=async t=>{let{path:r,headers:a,query:n,body:o}=Li(t),i=this.props.toAxiosRequest??m,u=this.props.toApiError??q,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getParticipant=async t=>{let{path:r,headers:a,query:n,body:o}=Si(t),i=this.props.toAxiosRequest??m,u=this.props.toApiError??q,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};removeParticipant=async t=>{let{path:r,headers:a,query:n,body:o}=Hi(t),i=this.props.toAxiosRequest??m,u=this.props.toApiError??q,p=i({method:"delete",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};createEvent=async t=>{let{path:r,headers:a,query:n,body:o}=_i(t),i=this.props.toAxiosRequest??m,u=this.props.toApiError??q,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getEvent=async t=>{let{path:r,headers:a,query:n,body:o}=Mi(t),i=this.props.toAxiosRequest??m,u=this.props.toApiError??q,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};listEvents=async t=>{let{path:r,headers:a,query:n,body:o}=Vi(t),i=this.props.toAxiosRequest??m,u=this.props.toApiError??q,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};createMessage=async t=>{let{path:r,headers:a,query:n,body:o}=Ki(t),i=this.props.toAxiosRequest??m,u=this.props.toApiError??q,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getOrCreateMessage=async t=>{let{path:r,headers:a,query:n,body:o}=ji(t),i=this.props.toAxiosRequest??m,u=this.props.toApiError??q,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getMessage=async t=>{let{path:r,headers:a,query:n,body:o}=Ji(t),i=this.props.toAxiosRequest??m,u=this.props.toApiError??q,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};updateMessage=async t=>{let{path:r,headers:a,query:n,body:o}=Zi(t),i=this.props.toAxiosRequest??m,u=this.props.toApiError??q,p=i({method:"put",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};listMessages=async t=>{let{path:r,headers:a,query:n,body:o}=ep(t),i=this.props.toAxiosRequest??m,u=this.props.toApiError??q,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};deleteMessage=async t=>{let{path:r,headers:a,query:n,body:o}=sp(t),i=this.props.toAxiosRequest??m,u=this.props.toApiError??q,p=i({method:"delete",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};createUser=async t=>{let{path:r,headers:a,query:n,body:o}=ap(t),i=this.props.toAxiosRequest??m,u=this.props.toApiError??q,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getUser=async t=>{let{path:r,headers:a,query:n,body:o}=op(t),i=this.props.toAxiosRequest??m,u=this.props.toApiError??q,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};listUsers=async t=>{let{path:r,headers:a,query:n,body:o}=pp(t),i=this.props.toAxiosRequest??m,u=this.props.toApiError??q,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getOrCreateUser=async t=>{let{path:r,headers:a,query:n,body:o}=cp(t),i=this.props.toAxiosRequest??m,u=this.props.toApiError??q,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};updateUser=async t=>{let{path:r,headers:a,query:n,body:o}=lp(t),i=this.props.toAxiosRequest??m,u=this.props.toApiError??q,p=i({method:"put",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};deleteUser=async t=>{let{path:r,headers:a,query:n,body:o}=yp(t),i=this.props.toAxiosRequest??m,u=this.props.toApiError??q,p=i({method:"delete",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};setStateExpiry=async t=>{let{path:r,headers:a,query:n,body:o}=mp(t),i=this.props.toAxiosRequest??m,u=this.props.toApiError??q,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getState=async t=>{let{path:r,headers:a,query:n,body:o}=hp(t),i=this.props.toAxiosRequest??m,u=this.props.toApiError??q,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};setState=async t=>{let{path:r,headers:a,query:n,body:o}=fp(t),i=this.props.toAxiosRequest??m,u=this.props.toApiError??q,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getOrSetState=async t=>{let{path:r,headers:a,query:n,body:o}=Ip(t),i=this.props.toAxiosRequest??m,u=this.props.toApiError??q,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};patchState=async t=>{let{path:r,headers:a,query:n,body:o}=Pp(t),i=this.props.toAxiosRequest??m,u=this.props.toApiError??q,p=i({method:"patch",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};callAction=async t=>{let{path:r,headers:a,query:n,body:o}=Ap(t),i=this.props.toAxiosRequest??m,u=this.props.toApiError??q,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};configureIntegration=async t=>{let{path:r,headers:a,query:n,body:o}=Bp(t),i=this.props.toAxiosRequest??m,u=this.props.toApiError??q,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getTask=async t=>{let{path:r,headers:a,query:n,body:o}=wp(t),i=this.props.toAxiosRequest??m,u=this.props.toApiError??q,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};createTask=async t=>{let{path:r,headers:a,query:n,body:o}=Gp(t),i=this.props.toAxiosRequest??m,u=this.props.toApiError??q,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};updateTask=async t=>{let{path:r,headers:a,query:n,body:o}=Lp(t),i=this.props.toAxiosRequest??m,u=this.props.toApiError??q,p=i({method:"put",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};deleteTask=async t=>{let{path:r,headers:a,query:n,body:o}=Sp(t),i=this.props.toAxiosRequest??m,u=this.props.toApiError??q,p=i({method:"delete",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};listTasks=async t=>{let{path:r,headers:a,query:n,body:o}=Hp(t),i=this.props.toAxiosRequest??m,u=this.props.toApiError??q,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};createWorkflow=async t=>{let{path:r,headers:a,query:n,body:o}=_p(t),i=this.props.toAxiosRequest??m,u=this.props.toApiError??q,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getWorkflow=async t=>{let{path:r,headers:a,query:n,body:o}=Mp(t),i=this.props.toAxiosRequest??m,u=this.props.toApiError??q,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};updateWorkflow=async t=>{let{path:r,headers:a,query:n,body:o}=Vp(t),i=this.props.toAxiosRequest??m,u=this.props.toApiError??q,p=i({method:"put",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};deleteWorkflow=async t=>{let{path:r,headers:a,query:n,body:o}=Kp(t),i=this.props.toAxiosRequest??m,u=this.props.toApiError??q,p=i({method:"delete",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};listWorkflows=async t=>{let{path:r,headers:a,query:n,body:o}=jp(t),i=this.props.toAxiosRequest??m,u=this.props.toApiError??q,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getOrCreateWorkflow=async t=>{let{path:r,headers:a,query:n,body:o}=Jp(t),i=this.props.toAxiosRequest??m,u=this.props.toApiError??q,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};listTagValues=async t=>{let{path:r,headers:a,query:n,body:o}=Zp(t),i=this.props.toAxiosRequest??m,u=this.props.toApiError??q,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};trackAnalytics=async t=>{let{path:r,headers:a,query:n,body:o}=eu(t),i=this.props.toAxiosRequest??m,u=this.props.toApiError??q,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})}};function q(e){return su.default.isAxiosError(e)&&e.response?.data?ms(e.response.data):ms(e)}var $s=class extends Gt{config;constructor(t){let r=F.getClientConfig(t),a=M.createAxios(r),n=ru.default.create(a);super(n,{toApiError:O.toApiError}),t.retry&&V(n,t.retry),this.config=r}get list(){return{conversations:t=>new h.AsyncCollection(({nextToken:r})=>this.listConversations({nextToken:r,...t}).then(a=>({...a,items:a.conversations}))),participants:t=>new h.AsyncCollection(({nextToken:r})=>this.listParticipants({nextToken:r,...t}).then(a=>({...a,items:a.participants}))),events:t=>new h.AsyncCollection(({nextToken:r})=>this.listEvents({nextToken:r,...t}).then(a=>({...a,items:a.events}))),messages:t=>new h.AsyncCollection(({nextToken:r})=>this.listMessages({nextToken:r,...t}).then(a=>({...a,items:a.messages}))),users:t=>new h.AsyncCollection(({nextToken:r})=>this.listUsers({nextToken:r,...t}).then(a=>({...a,items:a.users}))),tasks:t=>new h.AsyncCollection(({nextToken:r})=>this.listTasks({nextToken:r,...t}).then(a=>({...a,items:a.tasks})))}}};var xr={};K(xr,{Client:()=>fr});var Fl=w(require("axios"));var _l=w(require("axios"));var au=w(require("crypto"));var Dk={getRandomValues:e=>new Uint8Array(e.map(()=>Math.floor(Math.random()*256)))},zs=typeof window<"u"&&typeof window.document<"u"?window.crypto:au.default;zs.getRandomValues||(zs=Dk);var T=class extends Error{constructor(r,a,n,o,i,u,p){super(o);this.code=r;this.description=a;this.type=n;this.message=o;this.error=i;this.id=u;this.metadata=p;this.id||(this.id=T.generateId())}isApiError=!0;format(){return`[${this.type}] ${this.message} (Error ID: ${this.id})`}toJSON(){return{id:this.id,code:this.code,type:this.type,message:this.message,metadata:this.metadata}}static generateId(){let r=this.getPrefix(),a=new Date().toISOString().replace(/[\-:TZ]/g,"").split(".")[0],n=4,o=Array.from(zs.getRandomValues(new Uint8Array(n))).map(i=>i.toString(16).padStart(2,"0")).join("").toUpperCase();return`${r}_${a}x${o}`}static getPrefix(){return typeof window<"u"&&typeof window.document<"u"?"err_bwsr":"err"}},_k=e=>typeof e=="object"&&!Array.isArray(e)&&e!==null,Fk=e=>e instanceof T||_k(e)&&e.isApiError===!0,ge=class extends T{constructor(t,r,a,n){super(500,"An unknown error occurred","Unknown",t,r,a,n)}},Js=class extends T{constructor(t,r,a,n){super(500,"An internal error occurred","Internal",t,r,a,n)}},Ys=class extends T{constructor(t,r,a,n){super(401,"The request requires to be authenticated.","Unauthorized",t,r,a,n)}},Zs=class extends T{constructor(t,r,a,n){super(403,"The requested action can't be peform by this resource.","Forbidden",t,r,a,n)}},Xs=class extends T{constructor(t,r,a,n){super(413,"The request payload is too large.","PayloadTooLarge",t,r,a,n)}},er=class extends T{constructor(t,r,a,n){super(400,"The request payload is invalid.","InvalidPayload",t,r,a,n)}},tr=class extends T{constructor(t,r,a,n){super(415,"The request is invalid because the content-type is not supported.","UnsupportedMediaType",t,r,a,n)}},sr=class extends T{constructor(t,r,a,n){super(405,"The requested method does not exist.","MethodNotFound",t,r,a,n)}},rr=class extends T{constructor(t,r,a,n){super(404,"The requested resource does not exist.","ResourceNotFound",t,r,a,n)}},ar=class extends T{constructor(t,r,a,n){super(400,"The provided JSON schema is invalid.","InvalidJsonSchema",t,r,a,n)}},nr=class extends T{constructor(t,r,a,n){super(400,"The provided data doesn't respect the provided JSON schema.","InvalidDataFormat",t,r,a,n)}},or=class extends T{constructor(t,r,a,n){super(400,"The provided identifier is not valid. An identifier must start with a lowercase letter, be between 2 and 100 characters long and use only alphanumeric characters.","InvalidIdentifier",t,r,a,n)}},ir=class extends T{constructor(t,r,a,n){super(409,"The resource is related with a different resource that the one referenced in the request. This is usually caused when providing two resource identifiers that aren't linked together.","RelationConflict",t,r,a,n)}},pr=class extends T{constructor(t,r,a,n){super(409,"The resource cannot be deleted because it's referenced by another resource","ReferenceConstraint",t,r,a,n)}},ur=class extends T{constructor(t,r,a,n){super(409,"The resource is current locked and cannot be operated on until the lock is released.","ResourceLockedConflict",t,r,a,n)}},cr=class extends T{constructor(t,r,a,n){super(400,"The provided resource reference is missing. This is usually caused when providing an invalid id inside the payload of a request.","ReferenceNotFound",t,r,a,n)}},dr=class extends T{constructor(t,r,a,n){super(400,"The provided query is invalid. This is usually caused when providing an invalid parameter for querying a resource.","InvalidQuery",t,r,a,n)}},lr=class extends T{constructor(t,r,a,n){super(400,"An error happened during the execution of a runtime (bot or integration).","Runtime",t,r,a,n)}},gr=class extends T{constructor(t,r,a,n){super(409,"The record attempted to be created already exists.","AlreadyExists",t,r,a,n)}},yr=class extends T{constructor(t,r,a,n){super(429,"The request has been rate limited.","RateLimited",t,r,a,n)}},Rr=class extends T{constructor(t,r,a,n){super(402,"A payment is required to perform this request.","PaymentRequired",t,r,a,n)}},mr=class extends T{constructor(t,r,a,n){super(403,"The request exceeds the allowed quota. Quotas are a soft limit that can be increased.","QuotaExceeded",t,r,a,n)}},qr=class extends T{constructor(t,r,a,n){super(413,"The request exceeds the allowed limit. Limits are a hard limit that cannot be increased.","LimitExceeded",t,r,a,n)}},hr=class extends T{constructor(t,r,a,n){super(400,"Request payload contains breaking changes which is not allowed for this resource without a version increment.","BreakingChanges",t,r,a,n)}},Mk={Unknown:ge,Internal:Js,Unauthorized:Ys,Forbidden:Zs,PayloadTooLarge:Xs,InvalidPayload:er,UnsupportedMediaType:tr,MethodNotFound:sr,ResourceNotFound:rr,InvalidJsonSchema:ar,InvalidDataFormat:nr,InvalidIdentifier:or,RelationConflict:ir,ReferenceConstraint:pr,ResourceLockedConflict:ur,ReferenceNotFound:cr,InvalidQuery:dr,Runtime:lr,AlreadyExists:gr,RateLimited:yr,PaymentRequired:Rr,QuotaExceeded:mr,LimitExceeded:qr,BreakingChanges:hr},br=e=>Fk(e)?e:e instanceof Error?new ge(e.message,e):typeof e=="string"?new ge(e):Ok(e);function Ok(e){if(typeof e=="object"&&"code"in e&&"type"in e&&"id"in e&&"message"in e&&typeof e.type=="string"&&typeof e.message=="string"){let t=Mk[e.type];return t?new t(e.message,void 0,e.id||"UNKNOWN",e.metadata):new ge(`An unclassified API error occurred: ${e.message} (Type: ${e.type}, Code: ${e.code})`)}return new ge("An invalid error occurred: "+JSON.stringify(e))}var nu=w(Ce()),Vk=e=>e[1]!==void 0,l=e=>{let{method:t,path:r,query:a,headers:n,body:o}=e,i=Object.entries(n).filter(Vk),u=Object.fromEntries(i),p=nu.default.stringify(a,{encode:!0,arrayFormat:"repeat",allowDots:!0}),s=p?[r,p].join("?"):r,y=["put","post","delete","patch"].includes(t.toLowerCase())?o:void 0;return{method:t,url:s,headers:u,data:y}};var ou=e=>({path:"/v1/admin/helper/vrl",headers:{},query:{},params:{},body:{data:e.data,script:e.script}});var pu=e=>({path:"/v1/admin/account/me",headers:{},query:{},params:{},body:{}});var cu=e=>({path:"/v1/admin/account/me",headers:{},query:{},params:{},body:{displayName:e.displayName,profilePicture:e.profilePicture,refresh:e.refresh}});var lu=e=>({path:"/v1/admin/account/pats",headers:{},query:{},params:{},body:{}});var yu=e=>({path:"/v1/admin/account/pats",headers:{},query:{},params:{},body:{note:e.note}});var mu=e=>({path:`/v1/admin/account/pats/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var hu=e=>({path:`/v1/admin/account/preferences/${encodeURIComponent(e.key)}`,headers:{},query:{},params:{key:e.key},body:{value:e.value}});var fu=e=>({path:`/v1/admin/account/preferences/${encodeURIComponent(e.key)}`,headers:{},query:{},params:{key:e.key},body:{}});var Iu=e=>({path:"/v1/admin/hub/integrations",headers:{},query:{nextToken:e.nextToken,limit:e.limit,name:e.name,version:e.version,interfaceId:e.interfaceId,interfaceName:e.interfaceName,installedByBotId:e.installedByBotId,verificationStatus:e.verificationStatus,search:e.search,sortBy:e.sortBy,direction:e.direction},params:{},body:{}});var Pu=e=>({path:`/v1/admin/hub/integrations/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var Au=e=>({path:`/v1/admin/hub/integrations/${encodeURIComponent(e.name)}/${encodeURIComponent(e.version)}`,headers:{},query:{},params:{name:e.name,version:e.version},body:{}});var Bu=e=>({path:"/v1/admin/hub/plugins",headers:{},query:{nextToken:e.nextToken,name:e.name,version:e.version},params:{},body:{}});var wu=e=>({path:`/v1/admin/hub/plugins/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var Gu=e=>({path:`/v1/admin/hub/plugins/${encodeURIComponent(e.name)}/${encodeURIComponent(e.version)}`,headers:{},query:{},params:{name:e.name,version:e.version},body:{}});var Lu=e=>({path:`/v1/admin/hub/plugins/${encodeURIComponent(e.id)}/code/${encodeURIComponent(e.platform)}`,headers:{},query:{},params:{id:e.id,platform:e.platform},body:{}});var Su=e=>({path:"/v1/admin/hub/interfaces",headers:{},query:{nextToken:e.nextToken,name:e.name,version:e.version},params:{},body:{}});var Hu=e=>({path:`/v1/admin/hub/interfaces/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var _u=e=>({path:`/v1/admin/hub/interfaces/${encodeURIComponent(e.name)}/${encodeURIComponent(e.version)}`,headers:{},query:{},params:{name:e.name,version:e.version},body:{}});var Mu=e=>({path:"/v1/admin/bots",headers:{},query:{},params:{},body:{states:e.states,events:e.events,recurringEvents:e.recurringEvents,subscriptions:e.subscriptions,actions:e.actions,configuration:e.configuration,user:e.user,conversation:e.conversation,message:e.message,tags:e.tags,code:e.code,name:e.name,medias:e.medias,url:e.url,dev:e.dev}});var Vu=e=>({path:`/v1/admin/bots/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{url:e.url,authentication:e.authentication,configuration:e.configuration,tags:e.tags,blocked:e.blocked,alwaysAlive:e.alwaysAlive,user:e.user,message:e.message,conversation:e.conversation,events:e.events,actions:e.actions,states:e.states,recurringEvents:e.recurringEvents,integrations:e.integrations,plugins:e.plugins,subscriptions:e.subscriptions,code:e.code,name:e.name,medias:e.medias,layers:e.layers}});var Ku=e=>({path:`/v1/admin/bots/${encodeURIComponent(e.id)}/transfer`,headers:{},query:{},params:{id:e.id},body:{targetWorkspaceId:e.targetWorkspaceId}});var ju=e=>({path:"/v1/admin/bots",headers:{},query:{dev:e.dev,tags:e.tags,nextToken:e.nextToken,sortField:e.sortField,sortDirection:e.sortDirection},params:{},body:{}});var Ju=e=>({path:`/v1/admin/bots/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var Zu=e=>({path:`/v1/admin/bots/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var ec=e=>({path:`/v1/admin/bots/${encodeURIComponent(e.id)}/logs`,headers:{},query:{timeStart:e.timeStart,timeEnd:e.timeEnd,level:e.level,userId:e.userId,workflowId:e.workflowId,conversationId:e.conversationId,messageContains:e.messageContains,nextToken:e.nextToken},params:{id:e.id},body:{}});var sc=e=>({path:`/v1/admin/bots/${encodeURIComponent(e.id)}/webchat`,headers:{},query:{type:e.type},params:{id:e.id},body:{}});var ac=e=>({path:`/v1/admin/bots/${encodeURIComponent(e.id)}/analytics`,headers:{},query:{startDate:e.startDate,endDate:e.endDate},params:{id:e.id},body:{}});var oc=e=>({path:`/v1/admin/bots/${encodeURIComponent(e.id)}/issues/${encodeURIComponent(e.issueId)}`,headers:{},query:{},params:{id:e.id,issueId:e.issueId},body:{}});var pc=e=>({path:`/v1/admin/bots/${encodeURIComponent(e.id)}/issues`,headers:{},query:{nextToken:e.nextToken},params:{id:e.id},body:{}});var cc=e=>({path:`/v1/admin/bots/${encodeURIComponent(e.id)}/issues/${encodeURIComponent(e.issueId)}`,headers:{},query:{},params:{id:e.id,issueId:e.issueId},body:{}});var lc=e=>({path:`/v1/admin/bots/${encodeURIComponent(e.id)}/issues/${encodeURIComponent(e.issueId)}/events`,headers:{},query:{},params:{id:e.id,issueId:e.issueId},body:{}});var yc=e=>({path:`/v1/admin/bots/${encodeURIComponent(e.id)}/versions`,headers:{},query:{},params:{id:e.id},body:{}});var mc=e=>({path:`/v1/admin/bots/${encodeURIComponent(e.id)}/versions/${encodeURIComponent(e.versionId)}`,headers:{},query:{},params:{id:e.id,versionId:e.versionId},body:{}});var hc=e=>({path:`/v1/admin/bots/${encodeURIComponent(e.id)}/versions`,headers:{},query:{},params:{id:e.id},body:{name:e.name,description:e.description}});var fc=e=>({path:`/v1/admin/bots/${encodeURIComponent(e.id)}/versions/deploy`,headers:{},query:{},params:{id:e.id},body:{versionId:e.versionId}});var Ic=e=>({path:`/v1/admin/bots/${encodeURIComponent(e.botId)}/integrations/${encodeURIComponent(e.integrationId)}/shareable-id`,headers:{},query:{},params:{botId:e.botId,integrationId:e.integrationId},body:{}});var Pc=e=>({path:`/v1/admin/bots/${encodeURIComponent(e.botId)}/integrations/${encodeURIComponent(e.integrationId)}/shareable-id`,headers:{},query:{},params:{botId:e.botId,integrationId:e.integrationId},body:{}});var Ac=e=>({path:`/v1/admin/bots/${encodeURIComponent(e.botId)}/integrations/${encodeURIComponent(e.integrationId)}/shareable-id`,headers:{},query:{},params:{botId:e.botId,integrationId:e.integrationId},body:{}});var Bc=e=>({path:`/v1/admin/bots/${encodeURIComponent(e.botId)}/integrations/${encodeURIComponent(e.integrationId)}/sandboxed-conversations`,headers:{},query:{},params:{botId:e.botId,integrationId:e.integrationId},body:{}});var wc=e=>({path:"/v1/admin/bots/baks",headers:{},query:{botId:e.botId},params:{},body:{}});var Gc=e=>({path:"/v1/admin/bots/baks",headers:{},query:{},params:{},body:{botId:e.botId,note:e.note}});var Lc=e=>({path:`/v1/admin/bots/baks/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var Sc=e=>({path:`/v1/admin/workspaces/${encodeURIComponent(e.id)}/billing/invoices`,headers:{},query:{},params:{id:e.id},body:{}});var Hc=e=>({path:`/v1/admin/workspaces/${encodeURIComponent(e.id)}/billing/upcoming-invoice`,headers:{},query:{},params:{id:e.id},body:{}});var _c=e=>({path:`/v1/admin/workspaces/${encodeURIComponent(e.id)}/billing/invoices/charge-unpaid`,headers:{},query:{},params:{id:e.id},body:{invoiceIds:e.invoiceIds}});var Mc=e=>({path:"/v1/admin/workspaces",headers:{},query:{},params:{},body:{name:e.name}});var Vc=e=>({path:`/v1/admin/workspaces/${encodeURIComponent(e.id)}/public`,headers:{},query:{},params:{id:e.id},body:{}});var Kc=e=>({path:`/v1/admin/workspaces/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var jc=e=>({path:`/v1/admin/workspaces/${encodeURIComponent(e.id)}/usages`,headers:{},query:{type:e.type,period:e.period},params:{id:e.id},body:{}});var Jc=e=>({path:`/v1/admin/workspaces/${encodeURIComponent(e.id)}/usages/by-bot`,headers:{},query:{type:e.type,period:e.period},params:{id:e.id},body:{}});var Zc=e=>({path:"/v1/admin/workspaces/usages/quota-completion",headers:{},query:{},params:{},body:{}});var ed=e=>({path:`/v1/admin/workspaces/${encodeURIComponent(e.id)}/quota`,headers:{},query:{type:e.type,period:e.period},params:{id:e.id},body:{}});var sd=e=>({path:`/v1/admin/workspaces/${encodeURIComponent(e.id)}/quotas`,headers:{},query:{period:e.period},params:{id:e.id},body:{}});var ad=e=>({path:`/v1/admin/workspaces/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{name:e.name,spendingLimit:e.spendingLimit,about:e.about,profilePicture:e.profilePicture,contactEmail:e.contactEmail,website:e.website,socialAccounts:e.socialAccounts,isPublic:e.isPublic,handle:e.handle}});var od=e=>({path:"/v1/admin/workspaces/handle-availability",headers:{},query:{},params:{},body:{handle:e.handle}});var pd=e=>({path:"/v1/admin/workspaces",headers:{},query:{nextToken:e.nextToken,handle:e.handle},params:{},body:{}});var cd=e=>({path:"/v1/admin/workspaces/public",headers:{},query:{nextToken:e.nextToken,workspaceIds:e.workspaceIds,search:e.search},params:{},body:{}});var ld=e=>({path:`/v1/admin/workspaces/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var yd=e=>({path:`/v1/admin/workspaces/${encodeURIComponent(e.id)}/audit-records`,headers:{},query:{nextToken:e.nextToken},params:{id:e.id},body:{}});var md=e=>({path:"/v1/admin/workspace-members",headers:{},query:{nextToken:e.nextToken},params:{},body:{}});var hd=e=>({path:"/v1/admin/workspace-members/me",headers:{},query:{},params:{},body:{}});var fd=e=>({path:`/v1/admin/workspace-members/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var Id=e=>({path:"/v1/admin/workspace-members",headers:{},query:{},params:{},body:{email:e.email,role:e.role}});var Pd=e=>({path:`/v1/admin/workspace-members/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{role:e.role}});var Ad=e=>({path:"/v1/admin/integrations/iaks",headers:{},query:{integrationId:e.integrationId},params:{},body:{}});var Bd=e=>({path:"/v1/admin/integrations/iaks",headers:{},query:{},params:{},body:{integrationId:e.integrationId,note:e.note}});var wd=e=>({path:`/v1/admin/integrations/iaks/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var Gd=e=>({path:"/v1/admin/integrations",headers:{},query:{},params:{},body:{name:e.name,version:e.version,configuration:e.configuration,configurations:e.configurations,states:e.states,events:e.events,actions:e.actions,entities:e.entities,attributes:e.attributes,identifier:e.identifier,channels:e.channels,user:e.user,interfaces:e.interfaces,secrets:e.secrets,code:e.code,url:e.url,dev:e.dev,icon:e.icon,readme:e.readme,title:e.title,description:e.description,public:e.public,layers:e.layers}});var Ld=e=>({path:"/v1/admin/integrations/validate",headers:{},query:{},params:{},body:{name:e.name,version:e.version,configuration:e.configuration,configurations:e.configurations,states:e.states,events:e.events,actions:e.actions,entities:e.entities,attributes:e.attributes,identifier:e.identifier,channels:e.channels,user:e.user,interfaces:e.interfaces,secrets:e.secrets,code:e.code,url:e.url,dev:e.dev,icon:e.icon,readme:e.readme,title:e.title,description:e.description,public:e.public,layers:e.layers}});var Sd=e=>({path:`/v1/admin/integrations/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{configuration:e.configuration,configurations:e.configurations,channels:e.channels,identifier:e.identifier,actions:e.actions,events:e.events,states:e.states,user:e.user,entities:e.entities,interfaces:e.interfaces,sandbox:e.sandbox,attributes:e.attributes,secrets:e.secrets,code:e.code,icon:e.icon,readme:e.readme,title:e.title,description:e.description,url:e.url,public:e.public,layers:e.layers}});var Hd=e=>({path:`/v1/admin/integrations/${encodeURIComponent(e.id)}/validate`,headers:{},query:{},params:{id:e.id},body:{configuration:e.configuration,configurations:e.configurations,channels:e.channels,identifier:e.identifier,actions:e.actions,events:e.events,states:e.states,user:e.user,entities:e.entities,interfaces:e.interfaces,sandbox:e.sandbox,attributes:e.attributes,secrets:e.secrets,code:e.code,icon:e.icon,readme:e.readme,title:e.title,description:e.description,url:e.url,public:e.public,layers:e.layers}});var _d=e=>({path:"/v1/admin/integrations",headers:{},query:{nextToken:e.nextToken,limit:e.limit,name:e.name,version:e.version,interfaceId:e.interfaceId,interfaceName:e.interfaceName,installedByBotId:e.installedByBotId,verificationStatus:e.verificationStatus,search:e.search,sortBy:e.sortBy,direction:e.direction,visibility:e.visibility,dev:e.dev},params:{},body:{}});var Md=e=>({path:`/v1/admin/integrations/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var Vd=e=>({path:`/v1/admin/integrations/${encodeURIComponent(e.id)}/logs`,headers:{},query:{timeStart:e.timeStart,timeEnd:e.timeEnd,level:e.level,userId:e.userId,conversationId:e.conversationId,messageContains:e.messageContains,nextToken:e.nextToken},params:{id:e.id},body:{}});var Kd=e=>({path:`/v1/admin/integrations/${encodeURIComponent(e.name)}/${encodeURIComponent(e.version)}`,headers:{},query:{},params:{name:e.name,version:e.version},body:{}});var jd=e=>({path:`/v1/admin/integrations/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var Jd=e=>({path:"/v1/admin/integrations/request-verification",headers:{},query:{},params:{},body:{integrationId:e.integrationId}});var Zd=e=>({path:"/v1/admin/interfaces",headers:{},query:{},params:{},body:{name:e.name,version:e.version,entities:e.entities,events:e.events,actions:e.actions,channels:e.channels,nameTemplate:e.nameTemplate,attributes:e.attributes,icon:e.icon,readme:e.readme,title:e.title,description:e.description,public:e.public}});var el=e=>({path:`/v1/admin/interfaces/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var sl=e=>({path:`/v1/admin/interfaces/${encodeURIComponent(e.name)}/${encodeURIComponent(e.version)}`,headers:{},query:{},params:{name:e.name,version:e.version},body:{}});var al=e=>({path:`/v1/admin/interfaces/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{entities:e.entities,events:e.events,actions:e.actions,channels:e.channels,nameTemplate:e.nameTemplate,attributes:e.attributes,icon:e.icon,readme:e.readme,title:e.title,description:e.description,public:e.public}});var ol=e=>({path:`/v1/admin/interfaces/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var pl=e=>({path:"/v1/admin/interfaces",headers:{},query:{nextToken:e.nextToken,name:e.name,version:e.version},params:{},body:{}});var cl=e=>({path:"/v1/admin/plugins",headers:{},query:{},params:{},body:{name:e.name,version:e.version,configuration:e.configuration,states:e.states,events:e.events,actions:e.actions,user:e.user,conversation:e.conversation,dependencies:e.dependencies,attributes:e.attributes,code:e.code,icon:e.icon,readme:e.readme,title:e.title,description:e.description,public:e.public}});var ll=e=>({path:`/v1/admin/plugins/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var yl=e=>({path:`/v1/admin/plugins/${encodeURIComponent(e.name)}/${encodeURIComponent(e.version)}`,headers:{},query:{},params:{name:e.name,version:e.version},body:{}});var ml=e=>({path:`/v1/admin/plugins/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{configuration:e.configuration,states:e.states,events:e.events,actions:e.actions,user:e.user,conversation:e.conversation,dependencies:e.dependencies,attributes:e.attributes,code:e.code,icon:e.icon,readme:e.readme,title:e.title,description:e.description,public:e.public}});var hl=e=>({path:`/v1/admin/plugins/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var fl=e=>({path:"/v1/admin/plugins",headers:{},query:{nextToken:e.nextToken,name:e.name,version:e.version},params:{},body:{}});var Il=e=>({path:`/v1/admin/plugins/${encodeURIComponent(e.id)}/code/${encodeURIComponent(e.platform)}`,headers:{},query:{},params:{id:e.id,platform:e.platform},body:{}});var Pl=e=>({path:`/v1/admin/usages/${encodeURIComponent(e.id)}`,headers:{},query:{type:e.type,period:e.period},params:{id:e.id},body:{}});var Al=e=>({path:"/v1/admin/usages/multiple",headers:{},query:{types:e.types,ids:e.ids,period:e.period},params:{},body:{}});var Bl=e=>({path:`/v1/admin/usages/${encodeURIComponent(e.id)}/history`,headers:{},query:{type:e.type},params:{id:e.id},body:{}});var wl=e=>({path:`/v1/admin/usages/${encodeURIComponent(e.id)}/activity`,headers:{},query:{type:e.type,timestampFrom:e.timestampFrom,timestampUntil:e.timestampUntil,nextToken:e.nextToken},params:{id:e.id},body:{}});var Gl=e=>({path:`/v1/admin/usages/${encodeURIComponent(e.id)}/daily-activity`,headers:{},query:{type:e.type,dateFrom:e.dateFrom,dateUntil:e.dateUntil,nextToken:e.nextToken},params:{id:e.id},body:{}});var Ll=e=>({path:"/v1/admin/quotas/ai-spend",headers:{},query:{},params:{},body:{monthlySpendingLimit:e.monthlySpendingLimit}});var Sl=e=>({path:"/v1/admin/activities",headers:{},query:{nextToken:e.nextToken,taskId:e.taskId,botId:e.botId},params:{},body:{}});var Hl=e=>({path:"/v1/admin/introspect",headers:{},query:{},params:{},body:{botId:e.botId}});var Et=class{constructor(t,r={}){this.axiosInstance=t;this.props=r}runVrl=async t=>{let{path:r,headers:a,query:n,body:o}=ou(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getAccount=async t=>{let{path:r,headers:a,query:n,body:o}=pu(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};updateAccount=async t=>{let{path:r,headers:a,query:n,body:o}=cu(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"put",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};listPersonalAccessTokens=async t=>{let{path:r,headers:a,query:n,body:o}=lu(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};createPersonalAccessToken=async t=>{let{path:r,headers:a,query:n,body:o}=yu(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};deletePersonalAccessToken=async t=>{let{path:r,headers:a,query:n,body:o}=mu(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"delete",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};setAccountPreference=async t=>{let{path:r,headers:a,query:n,body:o}=hu(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getAccountPreference=async t=>{let{path:r,headers:a,query:n,body:o}=fu(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};listPublicIntegrations=async t=>{let{path:r,headers:a,query:n,body:o}=Iu(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getPublicIntegrationById=async t=>{let{path:r,headers:a,query:n,body:o}=Pu(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getPublicIntegration=async t=>{let{path:r,headers:a,query:n,body:o}=Au(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};listPublicPlugins=async t=>{let{path:r,headers:a,query:n,body:o}=Bu(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getPublicPluginById=async t=>{let{path:r,headers:a,query:n,body:o}=wu(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getPublicPlugin=async t=>{let{path:r,headers:a,query:n,body:o}=Gu(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getPublicPluginCode=async t=>{let{path:r,headers:a,query:n,body:o}=Lu(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};listPublicInterfaces=async t=>{let{path:r,headers:a,query:n,body:o}=Su(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getPublicInterfaceById=async t=>{let{path:r,headers:a,query:n,body:o}=Hu(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getPublicInterface=async t=>{let{path:r,headers:a,query:n,body:o}=_u(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};createBot=async t=>{let{path:r,headers:a,query:n,body:o}=Mu(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};updateBot=async t=>{let{path:r,headers:a,query:n,body:o}=Vu(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"put",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};transferBot=async t=>{let{path:r,headers:a,query:n,body:o}=Ku(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};listBots=async t=>{let{path:r,headers:a,query:n,body:o}=ju(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getBot=async t=>{let{path:r,headers:a,query:n,body:o}=Ju(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};deleteBot=async t=>{let{path:r,headers:a,query:n,body:o}=Zu(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"delete",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getBotLogs=async t=>{let{path:r,headers:a,query:n,body:o}=ec(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getBotWebchat=async t=>{let{path:r,headers:a,query:n,body:o}=sc(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getBotAnalytics=async t=>{let{path:r,headers:a,query:n,body:o}=ac(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getBotIssue=async t=>{let{path:r,headers:a,query:n,body:o}=oc(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};listBotIssues=async t=>{let{path:r,headers:a,query:n,body:o}=pc(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};deleteBotIssue=async t=>{let{path:r,headers:a,query:n,body:o}=cc(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"delete",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};listBotIssueEvents=async t=>{let{path:r,headers:a,query:n,body:o}=lc(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};listBotVersions=async t=>{let{path:r,headers:a,query:n,body:o}=yc(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getBotVersion=async t=>{let{path:r,headers:a,query:n,body:o}=mc(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};createBotVersion=async t=>{let{path:r,headers:a,query:n,body:o}=hc(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};deployBotVersion=async t=>{let{path:r,headers:a,query:n,body:o}=fc(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};createIntegrationShareableId=async t=>{let{path:r,headers:a,query:n,body:o}=Ic(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};deleteIntegrationShareableId=async t=>{let{path:r,headers:a,query:n,body:o}=Pc(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"delete",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getIntegrationShareableId=async t=>{let{path:r,headers:a,query:n,body:o}=Ac(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};unlinkSandboxedConversations=async t=>{let{path:r,headers:a,query:n,body:o}=Bc(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"delete",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};listBotApiKeys=async t=>{let{path:r,headers:a,query:n,body:o}=wc(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};createBotApiKey=async t=>{let{path:r,headers:a,query:n,body:o}=Gc(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};deleteBotApiKey=async t=>{let{path:r,headers:a,query:n,body:o}=Lc(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"delete",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};listWorkspaceInvoices=async t=>{let{path:r,headers:a,query:n,body:o}=Sc(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getUpcomingInvoice=async t=>{let{path:r,headers:a,query:n,body:o}=Hc(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};chargeWorkspaceUnpaidInvoices=async t=>{let{path:r,headers:a,query:n,body:o}=_c(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};createWorkspace=async t=>{let{path:r,headers:a,query:n,body:o}=Mc(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getPublicWorkspace=async t=>{let{path:r,headers:a,query:n,body:o}=Vc(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getWorkspace=async t=>{let{path:r,headers:a,query:n,body:o}=Kc(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};listWorkspaceUsages=async t=>{let{path:r,headers:a,query:n,body:o}=jc(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};breakDownWorkspaceUsageByBot=async t=>{let{path:r,headers:a,query:n,body:o}=Jc(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getAllWorkspaceQuotaCompletion=async t=>{let{path:r,headers:a,query:n,body:o}=Zc(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getWorkspaceQuota=async t=>{let{path:r,headers:a,query:n,body:o}=ed(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};listWorkspaceQuotas=async t=>{let{path:r,headers:a,query:n,body:o}=sd(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};updateWorkspace=async t=>{let{path:r,headers:a,query:n,body:o}=ad(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"put",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};checkHandleAvailability=async t=>{let{path:r,headers:a,query:n,body:o}=od(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"put",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};listWorkspaces=async t=>{let{path:r,headers:a,query:n,body:o}=pd(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};listPublicWorkspaces=async t=>{let{path:r,headers:a,query:n,body:o}=cd(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};deleteWorkspace=async t=>{let{path:r,headers:a,query:n,body:o}=ld(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"delete",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getAuditRecords=async t=>{let{path:r,headers:a,query:n,body:o}=yd(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};listWorkspaceMembers=async t=>{let{path:r,headers:a,query:n,body:o}=md(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getWorkspaceMember=async t=>{let{path:r,headers:a,query:n,body:o}=hd(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};deleteWorkspaceMember=async t=>{let{path:r,headers:a,query:n,body:o}=fd(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"delete",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};createWorkspaceMember=async t=>{let{path:r,headers:a,query:n,body:o}=Id(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};updateWorkspaceMember=async t=>{let{path:r,headers:a,query:n,body:o}=Pd(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"put",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};listIntegrationApiKeys=async t=>{let{path:r,headers:a,query:n,body:o}=Ad(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};createIntegrationApiKey=async t=>{let{path:r,headers:a,query:n,body:o}=Bd(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};deleteIntegrationApiKey=async t=>{let{path:r,headers:a,query:n,body:o}=wd(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"delete",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};createIntegration=async t=>{let{path:r,headers:a,query:n,body:o}=Gd(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};validateIntegrationCreation=async t=>{let{path:r,headers:a,query:n,body:o}=Ld(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};updateIntegration=async t=>{let{path:r,headers:a,query:n,body:o}=Sd(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"put",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};validateIntegrationUpdate=async t=>{let{path:r,headers:a,query:n,body:o}=Hd(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"put",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};listIntegrations=async t=>{let{path:r,headers:a,query:n,body:o}=_d(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getIntegration=async t=>{let{path:r,headers:a,query:n,body:o}=Md(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getIntegrationLogs=async t=>{let{path:r,headers:a,query:n,body:o}=Vd(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getIntegrationByName=async t=>{let{path:r,headers:a,query:n,body:o}=Kd(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};deleteIntegration=async t=>{let{path:r,headers:a,query:n,body:o}=jd(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"delete",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};requestIntegrationVerification=async t=>{let{path:r,headers:a,query:n,body:o}=Jd(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};createInterface=async t=>{let{path:r,headers:a,query:n,body:o}=Zd(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getInterface=async t=>{let{path:r,headers:a,query:n,body:o}=el(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getInterfaceByName=async t=>{let{path:r,headers:a,query:n,body:o}=sl(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};updateInterface=async t=>{let{path:r,headers:a,query:n,body:o}=al(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"put",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};deleteInterface=async t=>{let{path:r,headers:a,query:n,body:o}=ol(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"delete",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};listInterfaces=async t=>{let{path:r,headers:a,query:n,body:o}=pl(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};createPlugin=async t=>{let{path:r,headers:a,query:n,body:o}=cl(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getPlugin=async t=>{let{path:r,headers:a,query:n,body:o}=ll(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getPluginByName=async t=>{let{path:r,headers:a,query:n,body:o}=yl(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};updatePlugin=async t=>{let{path:r,headers:a,query:n,body:o}=ml(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"put",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};deletePlugin=async t=>{let{path:r,headers:a,query:n,body:o}=hl(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"delete",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};listPlugins=async t=>{let{path:r,headers:a,query:n,body:o}=fl(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getPluginCode=async t=>{let{path:r,headers:a,query:n,body:o}=Il(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getUsage=async t=>{let{path:r,headers:a,query:n,body:o}=Pl(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getMultipleUsages=async t=>{let{path:r,headers:a,query:n,body:o}=Al(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};listUsageHistory=async t=>{let{path:r,headers:a,query:n,body:o}=Bl(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};listUsageActivity=async t=>{let{path:r,headers:a,query:n,body:o}=wl(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};listUsageActivityDaily=async t=>{let{path:r,headers:a,query:n,body:o}=Gl(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};changeAISpendQuota=async t=>{let{path:r,headers:a,query:n,body:o}=Ll(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"put",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};listActivities=async t=>{let{path:r,headers:a,query:n,body:o}=Sl(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};introspect=async t=>{let{path:r,headers:a,query:n,body:o}=Hl(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})}};function g(e){return _l.default.isAxiosError(e)&&e.response?.data?br(e.response.data):br(e)}var fr=class extends Et{config;constructor(t){let r=F.getClientConfig(t),a=M.createAxios(r),n=Fl.default.create(a);super(n,{toApiError:O.toApiError}),t.retry&&V(n,t.retry),this.config=r}get list(){return{publicIntegrations:t=>new h.AsyncCollection(({nextToken:r})=>this.listPublicIntegrations({nextToken:r,...t}).then(a=>({...a,items:a.integrations}))),bots:t=>new h.AsyncCollection(({nextToken:r})=>this.listBots({nextToken:r,...t}).then(a=>({...a,items:a.bots}))),botIssues:t=>new h.AsyncCollection(({nextToken:r})=>this.listBotIssues({nextToken:r,...t}).then(a=>({...a,items:a.issues}))),workspaces:t=>new h.AsyncCollection(({nextToken:r})=>this.listWorkspaces({nextToken:r,...t}).then(a=>({...a,items:a.workspaces}))),publicWorkspaces:t=>new h.AsyncCollection(({nextToken:r})=>this.listPublicWorkspaces({nextToken:r,...t}).then(a=>({...a,items:a.workspaces}))),workspaceMembers:t=>new h.AsyncCollection(({nextToken:r})=>this.listWorkspaceMembers({nextToken:r,...t}).then(a=>({...a,items:a.members}))),integrations:t=>new h.AsyncCollection(({nextToken:r})=>this.listIntegrations({nextToken:r,...t}).then(a=>({...a,items:a.integrations}))),interfaces:t=>new h.AsyncCollection(({nextToken:r})=>this.listInterfaces({nextToken:r,...t}).then(a=>({...a,items:a.interfaces}))),activities:t=>new h.AsyncCollection(({nextToken:r})=>this.listActivities({nextToken:r,...t}).then(a=>({...a,items:a.activities}))),usageActivity:t=>new h.AsyncCollection(({nextToken:r})=>this.listUsageActivity({nextToken:r,...t}).then(a=>({...a,items:a.data}))),usageActivityDaily:t=>new h.AsyncCollection(({nextToken:r})=>this.listUsageActivityDaily({nextToken:r,...t}).then(a=>({...a,items:a.data})))}}};var zr={};K(zr,{Client:()=>jr});var Ig=w(require("axios"));var fg=w(require("axios"));var Ml=w(require("crypto"));var Kk={getRandomValues:e=>new Uint8Array(e.map(()=>Math.floor(Math.random()*256)))},Ir=typeof window<"u"&&typeof window.document<"u"?window.crypto:Ml.default;Ir.getRandomValues||(Ir=Kk);var B=class extends Error{constructor(r,a,n,o,i,u,p){super(o);this.code=r;this.description=a;this.type=n;this.message=o;this.error=i;this.id=u;this.metadata=p;this.id||(this.id=B.generateId())}isApiError=!0;format(){return`[${this.type}] ${this.message} (Error ID: ${this.id})`}toJSON(){return{id:this.id,code:this.code,type:this.type,message:this.message,metadata:this.metadata}}static generateId(){let r=this.getPrefix(),a=new Date().toISOString().replace(/[\-:TZ]/g,"").split(".")[0],n=4,o=Array.from(Ir.getRandomValues(new Uint8Array(n))).map(i=>i.toString(16).padStart(2,"0")).join("").toUpperCase();return`${r}_${a}x${o}`}static getPrefix(){return typeof window<"u"&&typeof window.document<"u"?"err_bwsr":"err"}},$k=e=>typeof e=="object"&&!Array.isArray(e)&&e!==null,jk=e=>e instanceof B||$k(e)&&e.isApiError===!0,ye=class extends B{constructor(t,r,a,n){super(500,"An unknown error occurred","Unknown",t,r,a,n)}},kr=class extends B{constructor(t,r,a,n){super(500,"An internal error occurred","Internal",t,r,a,n)}},Pr=class extends B{constructor(t,r,a,n){super(401,"The request requires to be authenticated.","Unauthorized",t,r,a,n)}},vr=class extends B{constructor(t,r,a,n){super(403,"The requested action can't be peform by this resource.","Forbidden",t,r,a,n)}},Ar=class extends B{constructor(t,r,a,n){super(413,"The request payload is too large.","PayloadTooLarge",t,r,a,n)}},Tr=class extends B{constructor(t,r,a,n){super(400,"The request payload is invalid.","InvalidPayload",t,r,a,n)}},Br=class extends B{constructor(t,r,a,n){super(415,"The request is invalid because the content-type is not supported.","UnsupportedMediaType",t,r,a,n)}},Cr=class extends B{constructor(t,r,a,n){super(405,"The requested method does not exist.","MethodNotFound",t,r,a,n)}},wr=class extends B{constructor(t,r,a,n){super(404,"The requested resource does not exist.","ResourceNotFound",t,r,a,n)}},Ur=class extends B{constructor(t,r,a,n){super(400,"The provided JSON schema is invalid.","InvalidJsonSchema",t,r,a,n)}},Gr=class extends B{constructor(t,r,a,n){super(400,"The provided data doesn't respect the provided JSON schema.","InvalidDataFormat",t,r,a,n)}},Er=class extends B{constructor(t,r,a,n){super(400,"The provided identifier is not valid. An identifier must start with a lowercase letter, be between 2 and 100 characters long and use only alphanumeric characters.","InvalidIdentifier",t,r,a,n)}},Lr=class extends B{constructor(t,r,a,n){super(409,"The resource is related with a different resource that the one referenced in the request. This is usually caused when providing two resource identifiers that aren't linked together.","RelationConflict",t,r,a,n)}},Wr=class extends B{constructor(t,r,a,n){super(409,"The resource cannot be deleted because it's referenced by another resource","ReferenceConstraint",t,r,a,n)}},Sr=class extends B{constructor(t,r,a,n){super(409,"The resource is current locked and cannot be operated on until the lock is released.","ResourceLockedConflict",t,r,a,n)}},Qr=class extends B{constructor(t,r,a,n){super(400,"The provided resource reference is missing. This is usually caused when providing an invalid id inside the payload of a request.","ReferenceNotFound",t,r,a,n)}},Hr=class extends B{constructor(t,r,a,n){super(400,"The provided query is invalid. This is usually caused when providing an invalid parameter for querying a resource.","InvalidQuery",t,r,a,n)}},Dr=class extends B{constructor(t,r,a,n){super(400,"An error happened during the execution of a runtime (bot or integration).","Runtime",t,r,a,n)}},_r=class extends B{constructor(t,r,a,n){super(409,"The record attempted to be created already exists.","AlreadyExists",t,r,a,n)}},Fr=class extends B{constructor(t,r,a,n){super(429,"The request has been rate limited.","RateLimited",t,r,a,n)}},Mr=class extends B{constructor(t,r,a,n){super(402,"A payment is required to perform this request.","PaymentRequired",t,r,a,n)}},Or=class extends B{constructor(t,r,a,n){super(403,"The request exceeds the allowed quota. Quotas are a soft limit that can be increased.","QuotaExceeded",t,r,a,n)}},Vr=class extends B{constructor(t,r,a,n){super(413,"The request exceeds the allowed limit. Limits are a hard limit that cannot be increased.","LimitExceeded",t,r,a,n)}},Nr=class extends B{constructor(t,r,a,n){super(400,"Request payload contains breaking changes which is not allowed for this resource without a version increment.","BreakingChanges",t,r,a,n)}},zk={Unknown:ye,Internal:kr,Unauthorized:Pr,Forbidden:vr,PayloadTooLarge:Ar,InvalidPayload:Tr,UnsupportedMediaType:Br,MethodNotFound:Cr,ResourceNotFound:wr,InvalidJsonSchema:Ur,InvalidDataFormat:Gr,InvalidIdentifier:Er,RelationConflict:Lr,ReferenceConstraint:Wr,ResourceLockedConflict:Sr,ReferenceNotFound:Qr,InvalidQuery:Hr,Runtime:Dr,AlreadyExists:_r,RateLimited:Fr,PaymentRequired:Mr,QuotaExceeded:Or,LimitExceeded:Vr,BreakingChanges:Nr},Kr=e=>jk(e)?e:e instanceof Error?new ye(e.message,e):typeof e=="string"?new ye(e):Jk(e);function Jk(e){if(typeof e=="object"&&"code"in e&&"type"in e&&"id"in e&&"message"in e&&typeof e.type=="string"&&typeof e.message=="string"){let t=zk[e.type];return t?new t(e.message,void 0,e.id||"UNKNOWN",e.metadata):new ye(`An unclassified API error occurred: ${e.message} (Type: ${e.type}, Code: ${e.code})`)}return new ye("An invalid error occurred: "+JSON.stringify(e))}var Ol=w(Ce()),Yk=e=>e[1]!==void 0,D=e=>{let{method:t,path:r,query:a,headers:n,body:o}=e,i=Object.entries(n).filter(Yk),u=Object.fromEntries(i),p=Ol.default.stringify(a,{encode:!0,arrayFormat:"repeat",allowDots:!0}),s=p?[r,p].join("?"):r,y=["put","post","delete","patch"].includes(t.toLowerCase())?o:void 0;return{method:t,url:s,headers:u,data:y}};var Vl=e=>({path:"/v1/files",headers:{},query:{},params:{},body:{key:e.key,tags:e.tags,size:e.size,index:e.index,indexing:e.indexing,accessPolicies:e.accessPolicies,contentType:e.contentType,expiresAt:e.expiresAt,publicContentImmediatelyAccessible:e.publicContentImmediatelyAccessible,metadata:e.metadata}});var Kl=e=>({path:`/v1/files/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var jl=e=>({path:"/v1/files",headers:{},query:{nextToken:e.nextToken,sortField:e.sortField,sortDirection:e.sortDirection,tags:e.tags,ids:e.ids},params:{},body:{}});var Jl=e=>({path:`/v1/files/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var Zl=e=>({path:`/v1/files/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{metadata:e.metadata,tags:e.tags,accessPolicies:e.accessPolicies,expiresAt:e.expiresAt}});var eg=e=>({path:`/v1/files/${encodeURIComponent(e.idOrKey)}/${encodeURIComponent(e.destinationKey)}`,headers:{"x-destination-bot-id":e["x-destination-bot-id"]},query:{},params:{idOrKey:e.idOrKey,destinationKey:e.destinationKey},body:{overwrite:e.overwrite}});var sg=e=>({path:"/v1/files/search",headers:{},query:{tags:e.tags,query:e.query,contextDepth:e.contextDepth,limit:e.limit,consolidate:e.consolidate,includeBreadcrumb:e.includeBreadcrumb},params:{},body:{}});var ag=e=>({path:`/v1/files/${encodeURIComponent(e.id)}/passages`,headers:{},query:{nextToken:e.nextToken,limit:e.limit},params:{id:e.id},body:{}});var og=e=>({path:`/v1/files/${encodeURIComponent(e.id)}/passages`,headers:{},query:{},params:{id:e.id},body:{passages:e.passages}});var pg=e=>({path:"/v1/files/tags",headers:{},query:{nextToken:e.nextToken},params:{},body:{}});var cg=e=>({path:`/v1/files/tags/${encodeURIComponent(e.tag)}/values`,headers:{},query:{nextToken:e.nextToken},params:{tag:e.tag},body:{}});var lg=e=>({path:"/v1/files/knowledge-bases",headers:{},query:{},params:{},body:{name:e.name}});var yg=e=>({path:`/v1/files/knowledge-bases/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var mg=e=>({path:`/v1/files/knowledge-bases/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{name:e.name}});var hg=e=>({path:"/v1/files/knowledge-bases",headers:{},query:{nextToken:e.nextToken},params:{},body:{}});var Lt=class{constructor(t,r={}){this.axiosInstance=t;this.props=r}upsertFile=async t=>{let{path:r,headers:a,query:n,body:o}=Vl(t),i=this.props.toAxiosRequest??D,u=this.props.toApiError??_,p=i({method:"put",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};deleteFile=async t=>{let{path:r,headers:a,query:n,body:o}=Kl(t),i=this.props.toAxiosRequest??D,u=this.props.toApiError??_,p=i({method:"delete",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};listFiles=async t=>{let{path:r,headers:a,query:n,body:o}=jl(t),i=this.props.toAxiosRequest??D,u=this.props.toApiError??_,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getFile=async t=>{let{path:r,headers:a,query:n,body:o}=Jl(t),i=this.props.toAxiosRequest??D,u=this.props.toApiError??_,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};updateFileMetadata=async t=>{let{path:r,headers:a,query:n,body:o}=Zl(t),i=this.props.toAxiosRequest??D,u=this.props.toApiError??_,p=i({method:"put",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};copyFile=async t=>{let{path:r,headers:a,query:n,body:o}=eg(t),i=this.props.toAxiosRequest??D,u=this.props.toApiError??_,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};searchFiles=async t=>{let{path:r,headers:a,query:n,body:o}=sg(t),i=this.props.toAxiosRequest??D,u=this.props.toApiError??_,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};listFilePassages=async t=>{let{path:r,headers:a,query:n,body:o}=ag(t),i=this.props.toAxiosRequest??D,u=this.props.toApiError??_,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};setFilePassages=async t=>{let{path:r,headers:a,query:n,body:o}=og(t),i=this.props.toAxiosRequest??D,u=this.props.toApiError??_,p=i({method:"put",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};listFileTags=async t=>{let{path:r,headers:a,query:n,body:o}=pg(t),i=this.props.toAxiosRequest??D,u=this.props.toApiError??_,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};listFileTagValues=async t=>{let{path:r,headers:a,query:n,body:o}=cg(t),i=this.props.toAxiosRequest??D,u=this.props.toApiError??_,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};createKnowledgeBase=async t=>{let{path:r,headers:a,query:n,body:o}=lg(t),i=this.props.toAxiosRequest??D,u=this.props.toApiError??_,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};deleteKnowledgeBase=async t=>{let{path:r,headers:a,query:n,body:o}=yg(t),i=this.props.toAxiosRequest??D,u=this.props.toApiError??_,p=i({method:"delete",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};updateKnowledgeBase=async t=>{let{path:r,headers:a,query:n,body:o}=mg(t),i=this.props.toAxiosRequest??D,u=this.props.toApiError??_,p=i({method:"put",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};listKnowledgeBases=async t=>{let{path:r,headers:a,query:n,body:o}=hg(t),i=this.props.toAxiosRequest??D,u=this.props.toApiError??_,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})}};function _(e){return fg.default.isAxiosError(e)&&e.response?.data?Kr(e.response.data):Kr(e)}var $r=w(require("axios"));var Wt=async(e,{key:t,index:r,tags:a,contentType:n,accessPolicies:o,content:i,url:u,indexing:p,expiresAt:s,metadata:y,publicContentImmediatelyAccessible:P})=>{if(u&&i)throw new Y("Cannot provide both content and URL, please provide only one of them");if(u&&(i=await $r.default.get(u,{responseType:"arraybuffer"}).then(U=>U.data).catch(U=>{throw new Y(`Failed to download file from provided URL: ${U.message}`,U)})),!i)throw new Y("No content was provided for the file");let b,k;if(typeof i=="string"){let x=new TextEncoder().encode(i);b=x,k=x.byteLength}else if(i instanceof Uint8Array)b=i,k=b.byteLength;else if(i instanceof ArrayBuffer)b=i,k=b.byteLength;else if(i instanceof Blob)b=i,k=i.size;else throw new Y("The provided content is not supported");let{file:I}=await e.upsertFile({key:t,tags:a,index:r,accessPolicies:o,contentType:n,metadata:y,size:k,expiresAt:s,indexing:p,publicContentImmediatelyAccessible:P}),G={"Content-Type":I.contentType};P&&(G["x-amz-tagging"]="public=true");try{await $r.default.put(I.uploadUrl,b,{maxBodyLength:1/0,headers:G})}catch(U){let x=U instanceof Error?U:new Error(String(U));throw new Y(`Failed to upload file: ${x.message}`,x,I)}return{file:{...I,size:k}}};var jr=class extends Lt{config;constructor(t){let r=F.getClientConfig(t),a=M.createAxios(r),n=Ig.default.create(a);super(n,{toApiError:O.toApiError}),t.retry&&V(n,t.retry),this.config=r}get list(){return{files:t=>new h.AsyncCollection(({nextToken:r})=>this.listFiles({nextToken:r,...t}).then(a=>({...a,items:a.files}))),filePassages:t=>new h.AsyncCollection(({nextToken:r})=>this.listFilePassages({nextToken:r,...t}).then(a=>({...a,items:a.passages})))}}async uploadFile(t){return await Wt(this,t)}};var Ia={};K(Ia,{Client:()=>xa});var oy=w(require("axios"));var ny=w(require("axios"));var kg=w(require("crypto"));var Xk={getRandomValues:e=>new Uint8Array(e.map(()=>Math.floor(Math.random()*256)))},Jr=typeof window<"u"&&typeof window.document<"u"?window.crypto:kg.default;Jr.getRandomValues||(Jr=Xk);var C=class extends Error{constructor(r,a,n,o,i,u,p){super(o);this.code=r;this.description=a;this.type=n;this.message=o;this.error=i;this.id=u;this.metadata=p;this.id||(this.id=C.generateId())}isApiError=!0;format(){return`[${this.type}] ${this.message} (Error ID: ${this.id})`}toJSON(){return{id:this.id,code:this.code,type:this.type,message:this.message,metadata:this.metadata}}static generateId(){let r=this.getPrefix(),a=new Date().toISOString().replace(/[\-:TZ]/g,"").split(".")[0],n=4,o=Array.from(Jr.getRandomValues(new Uint8Array(n))).map(i=>i.toString(16).padStart(2,"0")).join("").toUpperCase();return`${r}_${a}x${o}`}static getPrefix(){return typeof window<"u"&&typeof window.document<"u"?"err_bwsr":"err"}},eP=e=>typeof e=="object"&&!Array.isArray(e)&&e!==null,tP=e=>e instanceof C||eP(e)&&e.isApiError===!0,Re=class extends C{constructor(t,r,a,n){super(500,"An unknown error occurred","Unknown",t,r,a,n)}},Yr=class extends C{constructor(t,r,a,n){super(500,"An internal error occurred","Internal",t,r,a,n)}},Zr=class extends C{constructor(t,r,a,n){super(401,"The request requires to be authenticated.","Unauthorized",t,r,a,n)}},Xr=class extends C{constructor(t,r,a,n){super(403,"The requested action can't be peform by this resource.","Forbidden",t,r,a,n)}},ea=class extends C{constructor(t,r,a,n){super(413,"The request payload is too large.","PayloadTooLarge",t,r,a,n)}},ta=class extends C{constructor(t,r,a,n){super(400,"The request payload is invalid.","InvalidPayload",t,r,a,n)}},sa=class extends C{constructor(t,r,a,n){super(415,"The request is invalid because the content-type is not supported.","UnsupportedMediaType",t,r,a,n)}},ra=class extends C{constructor(t,r,a,n){super(405,"The requested method does not exist.","MethodNotFound",t,r,a,n)}},aa=class extends C{constructor(t,r,a,n){super(404,"The requested resource does not exist.","ResourceNotFound",t,r,a,n)}},na=class extends C{constructor(t,r,a,n){super(400,"The provided JSON schema is invalid.","InvalidJsonSchema",t,r,a,n)}},oa=class extends C{constructor(t,r,a,n){super(400,"The provided data doesn't respect the provided JSON schema.","InvalidDataFormat",t,r,a,n)}},ia=class extends C{constructor(t,r,a,n){super(400,"The provided identifier is not valid. An identifier must start with a lowercase letter, be between 2 and 100 characters long and use only alphanumeric characters.","InvalidIdentifier",t,r,a,n)}},pa=class extends C{constructor(t,r,a,n){super(409,"The resource is related with a different resource that the one referenced in the request. This is usually caused when providing two resource identifiers that aren't linked together.","RelationConflict",t,r,a,n)}},ua=class extends C{constructor(t,r,a,n){super(409,"The resource cannot be deleted because it's referenced by another resource","ReferenceConstraint",t,r,a,n)}},ca=class extends C{constructor(t,r,a,n){super(409,"The resource is current locked and cannot be operated on until the lock is released.","ResourceLockedConflict",t,r,a,n)}},da=class extends C{constructor(t,r,a,n){super(400,"The provided resource reference is missing. This is usually caused when providing an invalid id inside the payload of a request.","ReferenceNotFound",t,r,a,n)}},la=class extends C{constructor(t,r,a,n){super(400,"The provided query is invalid. This is usually caused when providing an invalid parameter for querying a resource.","InvalidQuery",t,r,a,n)}},ga=class extends C{constructor(t,r,a,n){super(400,"An error happened during the execution of a runtime (bot or integration).","Runtime",t,r,a,n)}},ya=class extends C{constructor(t,r,a,n){super(409,"The record attempted to be created already exists.","AlreadyExists",t,r,a,n)}},Ra=class extends C{constructor(t,r,a,n){super(429,"The request has been rate limited.","RateLimited",t,r,a,n)}},ma=class extends C{constructor(t,r,a,n){super(402,"A payment is required to perform this request.","PaymentRequired",t,r,a,n)}},qa=class extends C{constructor(t,r,a,n){super(403,"The request exceeds the allowed quota. Quotas are a soft limit that can be increased.","QuotaExceeded",t,r,a,n)}},ha=class extends C{constructor(t,r,a,n){super(413,"The request exceeds the allowed limit. Limits are a hard limit that cannot be increased.","LimitExceeded",t,r,a,n)}},ba=class extends C{constructor(t,r,a,n){super(400,"Request payload contains breaking changes which is not allowed for this resource without a version increment.","BreakingChanges",t,r,a,n)}},sP={Unknown:Re,Internal:Yr,Unauthorized:Zr,Forbidden:Xr,PayloadTooLarge:ea,InvalidPayload:ta,UnsupportedMediaType:sa,MethodNotFound:ra,ResourceNotFound:aa,InvalidJsonSchema:na,InvalidDataFormat:oa,InvalidIdentifier:ia,RelationConflict:pa,ReferenceConstraint:ua,ResourceLockedConflict:ca,ReferenceNotFound:da,InvalidQuery:la,Runtime:ga,AlreadyExists:ya,RateLimited:Ra,PaymentRequired:ma,QuotaExceeded:qa,LimitExceeded:ha,BreakingChanges:ba},fa=e=>tP(e)?e:e instanceof Error?new Re(e.message,e):typeof e=="string"?new Re(e):rP(e);function rP(e){if(typeof e=="object"&&"code"in e&&"type"in e&&"id"in e&&"message"in e&&typeof e.type=="string"&&typeof e.message=="string"){let t=sP[e.type];return t?new t(e.message,void 0,e.id||"UNKNOWN",e.metadata):new Re(`An unclassified API error occurred: ${e.message} (Type: ${e.type}, Code: ${e.code})`)}return new Re("An invalid error occurred: "+JSON.stringify(e))}var Pg=w(Ce()),aP=e=>e[1]!==void 0,W=e=>{let{method:t,path:r,query:a,headers:n,body:o}=e,i=Object.entries(n).filter(aP),u=Object.fromEntries(i),p=Pg.default.stringify(a,{encode:!0,arrayFormat:"repeat",allowDots:!0}),s=p?[r,p].join("?"):r,y=["put","post","delete","patch"].includes(t.toLowerCase())?o:void 0;return{method:t,url:s,headers:u,data:y}};var vg=e=>({path:"/v1/tables",headers:{},query:{tags:e.tags},params:{},body:{}});var Tg=e=>({path:`/v1/tables/${encodeURIComponent(e.table)}`,headers:{},query:{},params:{table:e.table},body:{}});var Cg=e=>({path:`/v1/tables/${encodeURIComponent(e.table)}`,headers:{},query:{},params:{table:e.table},body:{factor:e.factor,frozen:e.frozen,schema:e.schema,tags:e.tags,isComputeEnabled:e.isComputeEnabled}});var Ug=e=>({path:"/v1/tables",headers:{},query:{},params:{},body:{name:e.name,factor:e.factor,frozen:e.frozen,schema:e.schema,tags:e.tags,isComputeEnabled:e.isComputeEnabled}});var Eg=e=>({path:`/v1/tables/${encodeURIComponent(e.sourceTableId)}/duplicate`,headers:{},query:{},params:{sourceTableId:e.sourceTableId},body:{tableName:e.tableName,schemaOnly:e.schemaOnly,factor:e.factor}});var Wg=e=>({path:`/v1/tables/${encodeURIComponent(e.table)}/export`,headers:{},query:{format:e.format,compress:e.compress},params:{table:e.table},body:{}});var Qg=e=>({path:`/v1/tables/${encodeURIComponent(e.table)}/jobs`,headers:{},query:{},params:{table:e.table},body:{}});var Dg=e=>({path:`/v1/tables/${encodeURIComponent(e.table)}/import`,headers:{},query:{},params:{table:e.table},body:{fileId:e.fileId}});var Fg=e=>({path:`/v1/tables/${encodeURIComponent(e.table)}`,headers:{},query:{},params:{table:e.table},body:{name:e.name,frozen:e.frozen,schema:e.schema,tags:e.tags,isComputeEnabled:e.isComputeEnabled}});var Og=e=>({path:`/v1/tables/${encodeURIComponent(e.table)}/column`,headers:{},query:{},params:{table:e.table},body:{name:e.name,newName:e.newName}});var Ng=e=>({path:`/v1/tables/${encodeURIComponent(e.table)}`,headers:{},query:{},params:{table:e.table},body:{}});var $g=e=>({path:`/v1/tables/${encodeURIComponent(e.table)}/row`,headers:{},query:{id:e.id},params:{table:e.table},body:{}});var zg=e=>({path:`/v1/tables/${encodeURIComponent(e.table)}/rows/find`,headers:{},query:{},params:{table:e.table},body:{limit:e.limit,offset:e.offset,filter:e.filter,group:e.group,search:e.search,orderBy:e.orderBy,orderDirection:e.orderDirection}});var Yg=e=>({path:`/v1/tables/${encodeURIComponent(e.table)}/rows`,headers:{},query:{},params:{table:e.table},body:{rows:e.rows,waitComputed:e.waitComputed}});var Xg=e=>({path:`/v1/tables/${encodeURIComponent(e.table)}/rows/delete`,headers:{},query:{},params:{table:e.table},body:{ids:e.ids,filter:e.filter,deleteAllRows:e.deleteAllRows}});var ty=e=>({path:`/v1/tables/${encodeURIComponent(e.table)}/rows`,headers:{},query:{},params:{table:e.table},body:{rows:e.rows,waitComputed:e.waitComputed}});var ry=e=>({path:`/v1/tables/${encodeURIComponent(e.table)}/rows/upsert`,headers:{},query:{},params:{table:e.table},body:{rows:e.rows,keyColumn:e.keyColumn,waitComputed:e.waitComputed}});var St=class{constructor(t,r={}){this.axiosInstance=t;this.props=r}listTables=async t=>{let{path:r,headers:a,query:n,body:o}=vg(t),i=this.props.toAxiosRequest??W,u=this.props.toApiError??Q,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getTable=async t=>{let{path:r,headers:a,query:n,body:o}=Tg(t),i=this.props.toAxiosRequest??W,u=this.props.toApiError??Q,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getOrCreateTable=async t=>{let{path:r,headers:a,query:n,body:o}=Cg(t),i=this.props.toAxiosRequest??W,u=this.props.toApiError??Q,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};createTable=async t=>{let{path:r,headers:a,query:n,body:o}=Ug(t),i=this.props.toAxiosRequest??W,u=this.props.toApiError??Q,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};duplicateTable=async t=>{let{path:r,headers:a,query:n,body:o}=Eg(t),i=this.props.toAxiosRequest??W,u=this.props.toApiError??Q,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};exportTable=async t=>{let{path:r,headers:a,query:n,body:o}=Wg(t),i=this.props.toAxiosRequest??W,u=this.props.toApiError??Q,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getTableJobs=async t=>{let{path:r,headers:a,query:n,body:o}=Qg(t),i=this.props.toAxiosRequest??W,u=this.props.toApiError??Q,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};importTable=async t=>{let{path:r,headers:a,query:n,body:o}=Dg(t),i=this.props.toAxiosRequest??W,u=this.props.toApiError??Q,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};updateTable=async t=>{let{path:r,headers:a,query:n,body:o}=Fg(t),i=this.props.toAxiosRequest??W,u=this.props.toApiError??Q,p=i({method:"put",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};renameTableColumn=async t=>{let{path:r,headers:a,query:n,body:o}=Og(t),i=this.props.toAxiosRequest??W,u=this.props.toApiError??Q,p=i({method:"put",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};deleteTable=async t=>{let{path:r,headers:a,query:n,body:o}=Ng(t),i=this.props.toAxiosRequest??W,u=this.props.toApiError??Q,p=i({method:"delete",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getTableRow=async t=>{let{path:r,headers:a,query:n,body:o}=$g(t),i=this.props.toAxiosRequest??W,u=this.props.toApiError??Q,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};findTableRows=async t=>{let{path:r,headers:a,query:n,body:o}=zg(t),i=this.props.toAxiosRequest??W,u=this.props.toApiError??Q,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};createTableRows=async t=>{let{path:r,headers:a,query:n,body:o}=Yg(t),i=this.props.toAxiosRequest??W,u=this.props.toApiError??Q,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};deleteTableRows=async t=>{let{path:r,headers:a,query:n,body:o}=Xg(t),i=this.props.toAxiosRequest??W,u=this.props.toApiError??Q,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};updateTableRows=async t=>{let{path:r,headers:a,query:n,body:o}=ty(t),i=this.props.toAxiosRequest??W,u=this.props.toApiError??Q,p=i({method:"put",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};upsertTableRows=async t=>{let{path:r,headers:a,query:n,body:o}=ry(t),i=this.props.toAxiosRequest??W,u=this.props.toApiError??Q,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})}};function Q(e){return ny.default.isAxiosError(e)&&e.response?.data?fa(e.response.data):fa(e)}var xa=class extends St{config;constructor(t){let r=F.getClientConfig(t),a=M.createAxios(r),n=oy.default.create(a);super(n,{toApiError:O.toApiError}),t.retry&&V(n,t.retry),this.config=r}};var Sf=w(require("axios"));var Wf=w(require("axios"));var iy=w(Ce()),oP=e=>e[1]!==void 0,c=e=>{let{method:t,path:r,query:a,headers:n,body:o}=e,i=Object.entries(n).filter(oP),u=Object.fromEntries(i),p=iy.default.stringify(a,{encode:!0,arrayFormat:"repeat",allowDots:!0}),s=p?[r,p].join("?"):r,y=["put","post","delete","patch"].includes(t.toLowerCase())?o:void 0;return{method:t,url:s,headers:u,data:y}};var py=e=>({path:"/v1/chat/conversations",headers:{},query:{},params:{},body:{channel:e.channel,tags:e.tags,integrationName:e.integrationName}});var cy=e=>({path:`/v1/chat/conversations/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var ly=e=>({path:"/v1/chat/conversations",headers:{},query:{nextToken:e.nextToken,sortField:e.sortField,sortDirection:e.sortDirection,tags:e.tags,participantIds:e.participantIds,integrationName:e.integrationName,channel:e.channel},params:{},body:{}});var yy=e=>({path:"/v1/chat/conversations/get-or-create",headers:{},query:{},params:{},body:{channel:e.channel,tags:e.tags,integrationName:e.integrationName,discriminateByTags:e.discriminateByTags}});var my=e=>({path:`/v1/chat/conversations/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{currentTaskId:e.currentTaskId,tags:e.tags}});var hy=e=>({path:`/v1/chat/conversations/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var fy=e=>({path:`/v1/chat/conversations/${encodeURIComponent(e.id)}/participants`,headers:{},query:{nextToken:e.nextToken},params:{id:e.id},body:{}});var Iy=e=>({path:`/v1/chat/conversations/${encodeURIComponent(e.id)}/participants`,headers:{},query:{},params:{id:e.id},body:{userId:e.userId}});var Py=e=>({path:`/v1/chat/conversations/${encodeURIComponent(e.id)}/participants/${encodeURIComponent(e.userId)}`,headers:{},query:{},params:{id:e.id,userId:e.userId},body:{}});var Ay=e=>({path:`/v1/chat/conversations/${encodeURIComponent(e.id)}/participants/${encodeURIComponent(e.userId)}`,headers:{},query:{},params:{id:e.id,userId:e.userId},body:{}});var By=e=>({path:"/v1/chat/events",headers:{},query:{},params:{},body:{type:e.type,payload:e.payload,schedule:e.schedule,conversationId:e.conversationId,userId:e.userId,messageId:e.messageId}});var wy=e=>({path:`/v1/chat/events/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var Gy=e=>({path:"/v1/chat/events",headers:{},query:{nextToken:e.nextToken,type:e.type,conversationId:e.conversationId,userId:e.userId,messageId:e.messageId,status:e.status},params:{},body:{}});var Ly=e=>({path:"/v1/chat/messages",headers:{},query:{},params:{},body:{payload:e.payload,userId:e.userId,conversationId:e.conversationId,type:e.type,tags:e.tags,schedule:e.schedule}});var Sy=e=>({path:"/v1/chat/messages/get-or-create",headers:{},query:{},params:{},body:{payload:e.payload,userId:e.userId,conversationId:e.conversationId,type:e.type,tags:e.tags,schedule:e.schedule,discriminateByTags:e.discriminateByTags}});var Hy=e=>({path:`/v1/chat/messages/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var _y=e=>({path:`/v1/chat/messages/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{tags:e.tags,payload:e.payload}});var My=e=>({path:"/v1/chat/messages",headers:{},query:{nextToken:e.nextToken,conversationId:e.conversationId,tags:e.tags},params:{},body:{}});var Vy=e=>({path:`/v1/chat/messages/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var Ky=e=>({path:"/v1/chat/users",headers:{},query:{},params:{},body:{tags:e.tags,integrationName:e.integrationName,name:e.name,pictureUrl:e.pictureUrl}});var jy=e=>({path:`/v1/chat/users/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var Jy=e=>({path:"/v1/chat/users",headers:{},query:{nextToken:e.nextToken,conversationId:e.conversationId,tags:e.tags},params:{},body:{}});var Zy=e=>({path:"/v1/chat/users/get-or-create",headers:{},query:{},params:{},body:{tags:e.tags,integrationName:e.integrationName,name:e.name,pictureUrl:e.pictureUrl,discriminateByTags:e.discriminateByTags}});var eR=e=>({path:`/v1/chat/users/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{tags:e.tags,name:e.name,pictureUrl:e.pictureUrl}});var sR=e=>({path:`/v1/chat/users/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var aR=e=>({path:`/v1/chat/states/${encodeURIComponent(e.type)}/${encodeURIComponent(e.id)}/${encodeURIComponent(e.name)}/expiry`,headers:{},query:{},params:{type:e.type,id:e.id,name:e.name},body:{expiry:e.expiry}});var oR=e=>({path:`/v1/chat/states/${encodeURIComponent(e.type)}/${encodeURIComponent(e.id)}/${encodeURIComponent(e.name)}`,headers:{},query:{},params:{type:e.type,id:e.id,name:e.name},body:{}});var pR=e=>({path:`/v1/chat/states/${encodeURIComponent(e.type)}/${encodeURIComponent(e.id)}/${encodeURIComponent(e.name)}`,headers:{},query:{},params:{type:e.type,id:e.id,name:e.name},body:{payload:e.payload,expiry:e.expiry}});var cR=e=>({path:`/v1/chat/states/${encodeURIComponent(e.type)}/${encodeURIComponent(e.id)}/${encodeURIComponent(e.name)}/get-or-set`,headers:{},query:{},params:{type:e.type,id:e.id,name:e.name},body:{payload:e.payload,expiry:e.expiry}});var lR=e=>({path:`/v1/chat/states/${encodeURIComponent(e.type)}/${encodeURIComponent(e.id)}/${encodeURIComponent(e.name)}`,headers:{},query:{},params:{type:e.type,id:e.id,name:e.name},body:{payload:e.payload}});var yR=e=>({path:"/v1/chat/actions",headers:{},query:{},params:{},body:{type:e.type,input:e.input}});var mR=e=>({path:"/v1/chat/integrations/configure",headers:{},query:{},params:{},body:{identifier:e.identifier,scheduleRegisterCall:e.scheduleRegisterCall,sandboxIdentifiers:e.sandboxIdentifiers}});var hR=e=>({path:`/v1/chat/tasks/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var fR=e=>({path:"/v1/chat/tasks",headers:{},query:{},params:{},body:{title:e.title,description:e.description,type:e.type,data:e.data,parentTaskId:e.parentTaskId,conversationId:e.conversationId,userId:e.userId,timeoutAt:e.timeoutAt,tags:e.tags}});var IR=e=>({path:`/v1/chat/tasks/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{title:e.title,description:e.description,data:e.data,timeoutAt:e.timeoutAt,status:e.status,tags:e.tags}});var PR=e=>({path:`/v1/chat/tasks/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var AR=e=>({path:"/v1/chat/tasks",headers:{},query:{nextToken:e.nextToken,tags:e.tags,conversationId:e.conversationId,userId:e.userId,parentTaskId:e.parentTaskId,status:e.status,type:e.type},params:{},body:{}});var BR=e=>({path:"/v1/chat/workflows",headers:{},query:{},params:{},body:{name:e.name,input:e.input,parentWorkflowId:e.parentWorkflowId,conversationId:e.conversationId,userId:e.userId,timeoutAt:e.timeoutAt,tags:e.tags,status:e.status,eventId:e.eventId}});var wR=e=>({path:`/v1/chat/workflows/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var GR=e=>({path:`/v1/chat/workflows/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{output:e.output,timeoutAt:e.timeoutAt,status:e.status,failureReason:e.failureReason,tags:e.tags,userId:e.userId,eventId:e.eventId}});var LR=e=>({path:`/v1/chat/workflows/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var SR=e=>({path:"/v1/chat/workflows",headers:{},query:{nextToken:e.nextToken,tags:e.tags,conversationId:e.conversationId,userId:e.userId,parentWorkflowId:e.parentWorkflowId,statuses:e.statuses,name:e.name},params:{},body:{}});var HR=e=>({path:"/v1/chat/workflows/get-or-create",headers:{},query:{},params:{},body:{name:e.name,input:e.input,parentWorkflowId:e.parentWorkflowId,conversationId:e.conversationId,userId:e.userId,timeoutAt:e.timeoutAt,tags:e.tags,status:e.status,eventId:e.eventId}});var _R=e=>({path:`/v1/chat/tags/${encodeURIComponent(e.key)}/values`,headers:{},query:{nextToken:e.nextToken,type:e.type},params:{key:e.key},body:{}});var MR=e=>({path:"/v1/chat/analytics",headers:{},query:{},params:{},body:{name:e.name,count:e.count}});var VR=e=>({path:"/v1/admin/helper/vrl",headers:{},query:{},params:{},body:{data:e.data,script:e.script}});var KR=e=>({path:"/v1/admin/account/me",headers:{},query:{},params:{},body:{}});var jR=e=>({path:"/v1/admin/account/me",headers:{},query:{},params:{},body:{displayName:e.displayName,profilePicture:e.profilePicture,refresh:e.refresh}});var JR=e=>({path:"/v1/admin/account/pats",headers:{},query:{},params:{},body:{}});var ZR=e=>({path:"/v1/admin/account/pats",headers:{},query:{},params:{},body:{note:e.note}});var em=e=>({path:`/v1/admin/account/pats/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var sm=e=>({path:`/v1/admin/account/preferences/${encodeURIComponent(e.key)}`,headers:{},query:{},params:{key:e.key},body:{value:e.value}});var am=e=>({path:`/v1/admin/account/preferences/${encodeURIComponent(e.key)}`,headers:{},query:{},params:{key:e.key},body:{}});var om=e=>({path:"/v1/admin/hub/integrations",headers:{},query:{nextToken:e.nextToken,limit:e.limit,name:e.name,version:e.version,interfaceId:e.interfaceId,interfaceName:e.interfaceName,installedByBotId:e.installedByBotId,verificationStatus:e.verificationStatus,search:e.search,sortBy:e.sortBy,direction:e.direction},params:{},body:{}});var pm=e=>({path:`/v1/admin/hub/integrations/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var cm=e=>({path:`/v1/admin/hub/integrations/${encodeURIComponent(e.name)}/${encodeURIComponent(e.version)}`,headers:{},query:{},params:{name:e.name,version:e.version},body:{}});var lm=e=>({path:"/v1/admin/hub/plugins",headers:{},query:{nextToken:e.nextToken,name:e.name,version:e.version},params:{},body:{}});var ym=e=>({path:`/v1/admin/hub/plugins/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var mm=e=>({path:`/v1/admin/hub/plugins/${encodeURIComponent(e.name)}/${encodeURIComponent(e.version)}`,headers:{},query:{},params:{name:e.name,version:e.version},body:{}});var hm=e=>({path:`/v1/admin/hub/plugins/${encodeURIComponent(e.id)}/code/${encodeURIComponent(e.platform)}`,headers:{},query:{},params:{id:e.id,platform:e.platform},body:{}});var fm=e=>({path:"/v1/admin/hub/interfaces",headers:{},query:{nextToken:e.nextToken,name:e.name,version:e.version},params:{},body:{}});var Im=e=>({path:`/v1/admin/hub/interfaces/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var Pm=e=>({path:`/v1/admin/hub/interfaces/${encodeURIComponent(e.name)}/${encodeURIComponent(e.version)}`,headers:{},query:{},params:{name:e.name,version:e.version},body:{}});var Am=e=>({path:"/v1/admin/bots",headers:{},query:{},params:{},body:{states:e.states,events:e.events,recurringEvents:e.recurringEvents,subscriptions:e.subscriptions,actions:e.actions,configuration:e.configuration,user:e.user,conversation:e.conversation,message:e.message,tags:e.tags,code:e.code,name:e.name,medias:e.medias,url:e.url,dev:e.dev}});var Bm=e=>({path:`/v1/admin/bots/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{url:e.url,authentication:e.authentication,configuration:e.configuration,tags:e.tags,blocked:e.blocked,alwaysAlive:e.alwaysAlive,user:e.user,message:e.message,conversation:e.conversation,events:e.events,actions:e.actions,states:e.states,recurringEvents:e.recurringEvents,integrations:e.integrations,plugins:e.plugins,subscriptions:e.subscriptions,code:e.code,name:e.name,medias:e.medias,layers:e.layers}});var wm=e=>({path:`/v1/admin/bots/${encodeURIComponent(e.id)}/transfer`,headers:{},query:{},params:{id:e.id},body:{targetWorkspaceId:e.targetWorkspaceId}});var Gm=e=>({path:"/v1/admin/bots",headers:{},query:{dev:e.dev,tags:e.tags,nextToken:e.nextToken,sortField:e.sortField,sortDirection:e.sortDirection},params:{},body:{}});var Lm=e=>({path:`/v1/admin/bots/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var Sm=e=>({path:`/v1/admin/bots/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var Hm=e=>({path:`/v1/admin/bots/${encodeURIComponent(e.id)}/logs`,headers:{},query:{timeStart:e.timeStart,timeEnd:e.timeEnd,level:e.level,userId:e.userId,workflowId:e.workflowId,conversationId:e.conversationId,messageContains:e.messageContains,nextToken:e.nextToken},params:{id:e.id},body:{}});var _m=e=>({path:`/v1/admin/bots/${encodeURIComponent(e.id)}/webchat`,headers:{},query:{type:e.type},params:{id:e.id},body:{}});var Mm=e=>({path:`/v1/admin/bots/${encodeURIComponent(e.id)}/analytics`,headers:{},query:{startDate:e.startDate,endDate:e.endDate},params:{id:e.id},body:{}});var Vm=e=>({path:`/v1/admin/bots/${encodeURIComponent(e.id)}/issues/${encodeURIComponent(e.issueId)}`,headers:{},query:{},params:{id:e.id,issueId:e.issueId},body:{}});var Km=e=>({path:`/v1/admin/bots/${encodeURIComponent(e.id)}/issues`,headers:{},query:{nextToken:e.nextToken},params:{id:e.id},body:{}});var jm=e=>({path:`/v1/admin/bots/${encodeURIComponent(e.id)}/issues/${encodeURIComponent(e.issueId)}`,headers:{},query:{},params:{id:e.id,issueId:e.issueId},body:{}});var Jm=e=>({path:`/v1/admin/bots/${encodeURIComponent(e.id)}/issues/${encodeURIComponent(e.issueId)}/events`,headers:{},query:{},params:{id:e.id,issueId:e.issueId},body:{}});var Zm=e=>({path:`/v1/admin/bots/${encodeURIComponent(e.id)}/versions`,headers:{},query:{},params:{id:e.id},body:{}});var eq=e=>({path:`/v1/admin/bots/${encodeURIComponent(e.id)}/versions/${encodeURIComponent(e.versionId)}`,headers:{},query:{},params:{id:e.id,versionId:e.versionId},body:{}});var sq=e=>({path:`/v1/admin/bots/${encodeURIComponent(e.id)}/versions`,headers:{},query:{},params:{id:e.id},body:{name:e.name,description:e.description}});var aq=e=>({path:`/v1/admin/bots/${encodeURIComponent(e.id)}/versions/deploy`,headers:{},query:{},params:{id:e.id},body:{versionId:e.versionId}});var oq=e=>({path:`/v1/admin/bots/${encodeURIComponent(e.botId)}/integrations/${encodeURIComponent(e.integrationId)}/shareable-id`,headers:{},query:{},params:{botId:e.botId,integrationId:e.integrationId},body:{}});var pq=e=>({path:`/v1/admin/bots/${encodeURIComponent(e.botId)}/integrations/${encodeURIComponent(e.integrationId)}/shareable-id`,headers:{},query:{},params:{botId:e.botId,integrationId:e.integrationId},body:{}});var cq=e=>({path:`/v1/admin/bots/${encodeURIComponent(e.botId)}/integrations/${encodeURIComponent(e.integrationId)}/shareable-id`,headers:{},query:{},params:{botId:e.botId,integrationId:e.integrationId},body:{}});var lq=e=>({path:`/v1/admin/bots/${encodeURIComponent(e.botId)}/integrations/${encodeURIComponent(e.integrationId)}/sandboxed-conversations`,headers:{},query:{},params:{botId:e.botId,integrationId:e.integrationId},body:{}});var yq=e=>({path:"/v1/admin/bots/baks",headers:{},query:{botId:e.botId},params:{},body:{}});var mq=e=>({path:"/v1/admin/bots/baks",headers:{},query:{},params:{},body:{botId:e.botId,note:e.note}});var hq=e=>({path:`/v1/admin/bots/baks/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var fq=e=>({path:`/v1/admin/workspaces/${encodeURIComponent(e.id)}/billing/invoices`,headers:{},query:{},params:{id:e.id},body:{}});var Iq=e=>({path:`/v1/admin/workspaces/${encodeURIComponent(e.id)}/billing/upcoming-invoice`,headers:{},query:{},params:{id:e.id},body:{}});var Pq=e=>({path:`/v1/admin/workspaces/${encodeURIComponent(e.id)}/billing/invoices/charge-unpaid`,headers:{},query:{},params:{id:e.id},body:{invoiceIds:e.invoiceIds}});var Aq=e=>({path:"/v1/admin/workspaces",headers:{},query:{},params:{},body:{name:e.name}});var Bq=e=>({path:`/v1/admin/workspaces/${encodeURIComponent(e.id)}/public`,headers:{},query:{},params:{id:e.id},body:{}});var wq=e=>({path:`/v1/admin/workspaces/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var Gq=e=>({path:`/v1/admin/workspaces/${encodeURIComponent(e.id)}/usages`,headers:{},query:{type:e.type,period:e.period},params:{id:e.id},body:{}});var Lq=e=>({path:`/v1/admin/workspaces/${encodeURIComponent(e.id)}/usages/by-bot`,headers:{},query:{type:e.type,period:e.period},params:{id:e.id},body:{}});var Sq=e=>({path:"/v1/admin/workspaces/usages/quota-completion",headers:{},query:{},params:{},body:{}});var Hq=e=>({path:`/v1/admin/workspaces/${encodeURIComponent(e.id)}/quota`,headers:{},query:{type:e.type,period:e.period},params:{id:e.id},body:{}});var _q=e=>({path:`/v1/admin/workspaces/${encodeURIComponent(e.id)}/quotas`,headers:{},query:{period:e.period},params:{id:e.id},body:{}});var Mq=e=>({path:`/v1/admin/workspaces/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{name:e.name,spendingLimit:e.spendingLimit,about:e.about,profilePicture:e.profilePicture,contactEmail:e.contactEmail,website:e.website,socialAccounts:e.socialAccounts,isPublic:e.isPublic,handle:e.handle}});var Vq=e=>({path:"/v1/admin/workspaces/handle-availability",headers:{},query:{},params:{},body:{handle:e.handle}});var Kq=e=>({path:"/v1/admin/workspaces",headers:{},query:{nextToken:e.nextToken,handle:e.handle},params:{},body:{}});var jq=e=>({path:"/v1/admin/workspaces/public",headers:{},query:{nextToken:e.nextToken,workspaceIds:e.workspaceIds,search:e.search},params:{},body:{}});var Jq=e=>({path:`/v1/admin/workspaces/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var Zq=e=>({path:`/v1/admin/workspaces/${encodeURIComponent(e.id)}/audit-records`,headers:{},query:{nextToken:e.nextToken},params:{id:e.id},body:{}});var eh=e=>({path:"/v1/admin/workspace-members",headers:{},query:{nextToken:e.nextToken},params:{},body:{}});var sh=e=>({path:"/v1/admin/workspace-members/me",headers:{},query:{},params:{},body:{}});var ah=e=>({path:`/v1/admin/workspace-members/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var oh=e=>({path:"/v1/admin/workspace-members",headers:{},query:{},params:{},body:{email:e.email,role:e.role}});var ph=e=>({path:`/v1/admin/workspace-members/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{role:e.role}});var ch=e=>({path:"/v1/admin/integrations/iaks",headers:{},query:{integrationId:e.integrationId},params:{},body:{}});var lh=e=>({path:"/v1/admin/integrations/iaks",headers:{},query:{},params:{},body:{integrationId:e.integrationId,note:e.note}});var yh=e=>({path:`/v1/admin/integrations/iaks/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var mh=e=>({path:"/v1/admin/integrations",headers:{},query:{},params:{},body:{name:e.name,version:e.version,configuration:e.configuration,configurations:e.configurations,states:e.states,events:e.events,actions:e.actions,entities:e.entities,attributes:e.attributes,identifier:e.identifier,channels:e.channels,user:e.user,interfaces:e.interfaces,secrets:e.secrets,code:e.code,url:e.url,dev:e.dev,icon:e.icon,readme:e.readme,title:e.title,description:e.description,public:e.public,layers:e.layers}});var hh=e=>({path:"/v1/admin/integrations/validate",headers:{},query:{},params:{},body:{name:e.name,version:e.version,configuration:e.configuration,configurations:e.configurations,states:e.states,events:e.events,actions:e.actions,entities:e.entities,attributes:e.attributes,identifier:e.identifier,channels:e.channels,user:e.user,interfaces:e.interfaces,secrets:e.secrets,code:e.code,url:e.url,dev:e.dev,icon:e.icon,readme:e.readme,title:e.title,description:e.description,public:e.public,layers:e.layers}});var fh=e=>({path:`/v1/admin/integrations/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{configuration:e.configuration,configurations:e.configurations,channels:e.channels,identifier:e.identifier,actions:e.actions,events:e.events,states:e.states,user:e.user,entities:e.entities,interfaces:e.interfaces,sandbox:e.sandbox,attributes:e.attributes,secrets:e.secrets,code:e.code,icon:e.icon,readme:e.readme,title:e.title,description:e.description,url:e.url,public:e.public,layers:e.layers}});var Ih=e=>({path:`/v1/admin/integrations/${encodeURIComponent(e.id)}/validate`,headers:{},query:{},params:{id:e.id},body:{configuration:e.configuration,configurations:e.configurations,channels:e.channels,identifier:e.identifier,actions:e.actions,events:e.events,states:e.states,user:e.user,entities:e.entities,interfaces:e.interfaces,sandbox:e.sandbox,attributes:e.attributes,secrets:e.secrets,code:e.code,icon:e.icon,readme:e.readme,title:e.title,description:e.description,url:e.url,public:e.public,layers:e.layers}});var Ph=e=>({path:"/v1/admin/integrations",headers:{},query:{nextToken:e.nextToken,limit:e.limit,name:e.name,version:e.version,interfaceId:e.interfaceId,interfaceName:e.interfaceName,installedByBotId:e.installedByBotId,verificationStatus:e.verificationStatus,search:e.search,sortBy:e.sortBy,direction:e.direction,visibility:e.visibility,dev:e.dev},params:{},body:{}});var Ah=e=>({path:`/v1/admin/integrations/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var Bh=e=>({path:`/v1/admin/integrations/${encodeURIComponent(e.id)}/logs`,headers:{},query:{timeStart:e.timeStart,timeEnd:e.timeEnd,level:e.level,userId:e.userId,conversationId:e.conversationId,messageContains:e.messageContains,nextToken:e.nextToken},params:{id:e.id},body:{}});var wh=e=>({path:`/v1/admin/integrations/${encodeURIComponent(e.name)}/${encodeURIComponent(e.version)}`,headers:{},query:{},params:{name:e.name,version:e.version},body:{}});var Gh=e=>({path:`/v1/admin/integrations/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var Lh=e=>({path:"/v1/admin/integrations/request-verification",headers:{},query:{},params:{},body:{integrationId:e.integrationId}});var Sh=e=>({path:"/v1/admin/interfaces",headers:{},query:{},params:{},body:{name:e.name,version:e.version,entities:e.entities,events:e.events,actions:e.actions,channels:e.channels,nameTemplate:e.nameTemplate,attributes:e.attributes,icon:e.icon,readme:e.readme,title:e.title,description:e.description,public:e.public}});var Hh=e=>({path:`/v1/admin/interfaces/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var _h=e=>({path:`/v1/admin/interfaces/${encodeURIComponent(e.name)}/${encodeURIComponent(e.version)}`,headers:{},query:{},params:{name:e.name,version:e.version},body:{}});var Mh=e=>({path:`/v1/admin/interfaces/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{entities:e.entities,events:e.events,actions:e.actions,channels:e.channels,nameTemplate:e.nameTemplate,attributes:e.attributes,icon:e.icon,readme:e.readme,title:e.title,description:e.description,public:e.public}});var Vh=e=>({path:`/v1/admin/interfaces/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var Kh=e=>({path:"/v1/admin/interfaces",headers:{},query:{nextToken:e.nextToken,name:e.name,version:e.version},params:{},body:{}});var jh=e=>({path:"/v1/admin/plugins",headers:{},query:{},params:{},body:{name:e.name,version:e.version,configuration:e.configuration,states:e.states,events:e.events,actions:e.actions,user:e.user,conversation:e.conversation,dependencies:e.dependencies,attributes:e.attributes,code:e.code,icon:e.icon,readme:e.readme,title:e.title,description:e.description,public:e.public}});var Jh=e=>({path:`/v1/admin/plugins/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var Zh=e=>({path:`/v1/admin/plugins/${encodeURIComponent(e.name)}/${encodeURIComponent(e.version)}`,headers:{},query:{},params:{name:e.name,version:e.version},body:{}});var eb=e=>({path:`/v1/admin/plugins/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{configuration:e.configuration,states:e.states,events:e.events,actions:e.actions,user:e.user,conversation:e.conversation,dependencies:e.dependencies,attributes:e.attributes,code:e.code,icon:e.icon,readme:e.readme,title:e.title,description:e.description,public:e.public}});var sb=e=>({path:`/v1/admin/plugins/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var ab=e=>({path:"/v1/admin/plugins",headers:{},query:{nextToken:e.nextToken,name:e.name,version:e.version},params:{},body:{}});var ob=e=>({path:`/v1/admin/plugins/${encodeURIComponent(e.id)}/code/${encodeURIComponent(e.platform)}`,headers:{},query:{},params:{id:e.id,platform:e.platform},body:{}});var pb=e=>({path:`/v1/admin/usages/${encodeURIComponent(e.id)}`,headers:{},query:{type:e.type,period:e.period},params:{id:e.id},body:{}});var cb=e=>({path:"/v1/admin/usages/multiple",headers:{},query:{types:e.types,ids:e.ids,period:e.period},params:{},body:{}});var lb=e=>({path:`/v1/admin/usages/${encodeURIComponent(e.id)}/history`,headers:{},query:{type:e.type},params:{id:e.id},body:{}});var yb=e=>({path:`/v1/admin/usages/${encodeURIComponent(e.id)}/activity`,headers:{},query:{type:e.type,timestampFrom:e.timestampFrom,timestampUntil:e.timestampUntil,nextToken:e.nextToken},params:{id:e.id},body:{}});var mb=e=>({path:`/v1/admin/usages/${encodeURIComponent(e.id)}/daily-activity`,headers:{},query:{type:e.type,dateFrom:e.dateFrom,dateUntil:e.dateUntil,nextToken:e.nextToken},params:{id:e.id},body:{}});var hb=e=>({path:"/v1/admin/quotas/ai-spend",headers:{},query:{},params:{},body:{monthlySpendingLimit:e.monthlySpendingLimit}});var fb=e=>({path:"/v1/admin/activities",headers:{},query:{nextToken:e.nextToken,taskId:e.taskId,botId:e.botId},params:{},body:{}});var Ib=e=>({path:"/v1/admin/introspect",headers:{},query:{},params:{},body:{botId:e.botId}});var Pb=e=>({path:"/v1/files",headers:{},query:{},params:{},body:{key:e.key,tags:e.tags,size:e.size,index:e.index,indexing:e.indexing,accessPolicies:e.accessPolicies,contentType:e.contentType,expiresAt:e.expiresAt,publicContentImmediatelyAccessible:e.publicContentImmediatelyAccessible,metadata:e.metadata}});var Ab=e=>({path:`/v1/files/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var Bb=e=>({path:"/v1/files",headers:{},query:{nextToken:e.nextToken,sortField:e.sortField,sortDirection:e.sortDirection,tags:e.tags,ids:e.ids},params:{},body:{}});var wb=e=>({path:`/v1/files/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var Gb=e=>({path:`/v1/files/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{metadata:e.metadata,tags:e.tags,accessPolicies:e.accessPolicies,expiresAt:e.expiresAt}});var Lb=e=>({path:`/v1/files/${encodeURIComponent(e.idOrKey)}/${encodeURIComponent(e.destinationKey)}`,headers:{"x-destination-bot-id":e["x-destination-bot-id"]},query:{},params:{idOrKey:e.idOrKey,destinationKey:e.destinationKey},body:{overwrite:e.overwrite}});var Sb=e=>({path:"/v1/files/search",headers:{},query:{tags:e.tags,query:e.query,contextDepth:e.contextDepth,limit:e.limit,consolidate:e.consolidate,includeBreadcrumb:e.includeBreadcrumb},params:{},body:{}});var Hb=e=>({path:`/v1/files/${encodeURIComponent(e.id)}/passages`,headers:{},query:{nextToken:e.nextToken,limit:e.limit},params:{id:e.id},body:{}});var _b=e=>({path:`/v1/files/${encodeURIComponent(e.id)}/passages`,headers:{},query:{},params:{id:e.id},body:{passages:e.passages}});var Mb=e=>({path:"/v1/files/tags",headers:{},query:{nextToken:e.nextToken},params:{},body:{}});var Vb=e=>({path:`/v1/files/tags/${encodeURIComponent(e.tag)}/values`,headers:{},query:{nextToken:e.nextToken},params:{tag:e.tag},body:{}});var Kb=e=>({path:"/v1/files/knowledge-bases",headers:{},query:{},params:{},body:{name:e.name}});var jb=e=>({path:`/v1/files/knowledge-bases/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var Jb=e=>({path:`/v1/files/knowledge-bases/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{name:e.name}});var Zb=e=>({path:"/v1/files/knowledge-bases",headers:{},query:{nextToken:e.nextToken},params:{},body:{}});var ef=e=>({path:"/v1/tables",headers:{},query:{tags:e.tags},params:{},body:{}});var sf=e=>({path:`/v1/tables/${encodeURIComponent(e.table)}`,headers:{},query:{},params:{table:e.table},body:{}});var af=e=>({path:`/v1/tables/${encodeURIComponent(e.table)}`,headers:{},query:{},params:{table:e.table},body:{factor:e.factor,frozen:e.frozen,schema:e.schema,tags:e.tags,isComputeEnabled:e.isComputeEnabled}});var of=e=>({path:"/v1/tables",headers:{},query:{},params:{},body:{name:e.name,factor:e.factor,frozen:e.frozen,schema:e.schema,tags:e.tags,isComputeEnabled:e.isComputeEnabled}});var uf=e=>({path:`/v1/tables/${encodeURIComponent(e.sourceTableId)}/duplicate`,headers:{},query:{},params:{sourceTableId:e.sourceTableId},body:{tableName:e.tableName,schemaOnly:e.schemaOnly,factor:e.factor}});var df=e=>({path:`/v1/tables/${encodeURIComponent(e.table)}/export`,headers:{},query:{format:e.format,compress:e.compress},params:{table:e.table},body:{}});var gf=e=>({path:`/v1/tables/${encodeURIComponent(e.table)}/jobs`,headers:{},query:{},params:{table:e.table},body:{}});var Rf=e=>({path:`/v1/tables/${encodeURIComponent(e.table)}/import`,headers:{},query:{},params:{table:e.table},body:{fileId:e.fileId}});var qf=e=>({path:`/v1/tables/${encodeURIComponent(e.table)}`,headers:{},query:{},params:{table:e.table},body:{name:e.name,frozen:e.frozen,schema:e.schema,tags:e.tags,isComputeEnabled:e.isComputeEnabled}});var bf=e=>({path:`/v1/tables/${encodeURIComponent(e.table)}/column`,headers:{},query:{},params:{table:e.table},body:{name:e.name,newName:e.newName}});var xf=e=>({path:`/v1/tables/${encodeURIComponent(e.table)}`,headers:{},query:{},params:{table:e.table},body:{}});var kf=e=>({path:`/v1/tables/${encodeURIComponent(e.table)}/row`,headers:{},query:{id:e.id},params:{table:e.table},body:{}});var vf=e=>({path:`/v1/tables/${encodeURIComponent(e.table)}/rows/find`,headers:{},query:{},params:{table:e.table},body:{limit:e.limit,offset:e.offset,filter:e.filter,group:e.group,search:e.search,orderBy:e.orderBy,orderDirection:e.orderDirection}});var Tf=e=>({path:`/v1/tables/${encodeURIComponent(e.table)}/rows`,headers:{},query:{},params:{table:e.table},body:{rows:e.rows,waitComputed:e.waitComputed}});var Cf=e=>({path:`/v1/tables/${encodeURIComponent(e.table)}/rows/delete`,headers:{},query:{},params:{table:e.table},body:{ids:e.ids,filter:e.filter,deleteAllRows:e.deleteAllRows}});var Uf=e=>({path:`/v1/tables/${encodeURIComponent(e.table)}/rows`,headers:{},query:{},params:{table:e.table},body:{rows:e.rows,waitComputed:e.waitComputed}});var Ef=e=>({path:`/v1/tables/${encodeURIComponent(e.table)}/rows/upsert`,headers:{},query:{},params:{table:e.table},body:{rows:e.rows,keyColumn:e.keyColumn,waitComputed:e.waitComputed}});var Qt=class{constructor(t,r={}){this.axiosInstance=t;this.props=r}createConversation=async t=>{let{path:r,headers:a,query:n,body:o}=py(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getConversation=async t=>{let{path:r,headers:a,query:n,body:o}=cy(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};listConversations=async t=>{let{path:r,headers:a,query:n,body:o}=ly(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getOrCreateConversation=async t=>{let{path:r,headers:a,query:n,body:o}=yy(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};updateConversation=async t=>{let{path:r,headers:a,query:n,body:o}=my(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"put",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};deleteConversation=async t=>{let{path:r,headers:a,query:n,body:o}=hy(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"delete",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};listParticipants=async t=>{let{path:r,headers:a,query:n,body:o}=fy(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};addParticipant=async t=>{let{path:r,headers:a,query:n,body:o}=Iy(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getParticipant=async t=>{let{path:r,headers:a,query:n,body:o}=Py(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};removeParticipant=async t=>{let{path:r,headers:a,query:n,body:o}=Ay(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"delete",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};createEvent=async t=>{let{path:r,headers:a,query:n,body:o}=By(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getEvent=async t=>{let{path:r,headers:a,query:n,body:o}=wy(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};listEvents=async t=>{let{path:r,headers:a,query:n,body:o}=Gy(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};createMessage=async t=>{let{path:r,headers:a,query:n,body:o}=Ly(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getOrCreateMessage=async t=>{let{path:r,headers:a,query:n,body:o}=Sy(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getMessage=async t=>{let{path:r,headers:a,query:n,body:o}=Hy(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};updateMessage=async t=>{let{path:r,headers:a,query:n,body:o}=_y(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"put",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};listMessages=async t=>{let{path:r,headers:a,query:n,body:o}=My(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};deleteMessage=async t=>{let{path:r,headers:a,query:n,body:o}=Vy(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"delete",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};createUser=async t=>{let{path:r,headers:a,query:n,body:o}=Ky(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getUser=async t=>{let{path:r,headers:a,query:n,body:o}=jy(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};listUsers=async t=>{let{path:r,headers:a,query:n,body:o}=Jy(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getOrCreateUser=async t=>{let{path:r,headers:a,query:n,body:o}=Zy(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};updateUser=async t=>{let{path:r,headers:a,query:n,body:o}=eR(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"put",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};deleteUser=async t=>{let{path:r,headers:a,query:n,body:o}=sR(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"delete",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};setStateExpiry=async t=>{let{path:r,headers:a,query:n,body:o}=aR(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getState=async t=>{let{path:r,headers:a,query:n,body:o}=oR(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};setState=async t=>{let{path:r,headers:a,query:n,body:o}=pR(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getOrSetState=async t=>{let{path:r,headers:a,query:n,body:o}=cR(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};patchState=async t=>{let{path:r,headers:a,query:n,body:o}=lR(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"patch",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};callAction=async t=>{let{path:r,headers:a,query:n,body:o}=yR(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};configureIntegration=async t=>{let{path:r,headers:a,query:n,body:o}=mR(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getTask=async t=>{let{path:r,headers:a,query:n,body:o}=hR(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};createTask=async t=>{let{path:r,headers:a,query:n,body:o}=fR(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};updateTask=async t=>{let{path:r,headers:a,query:n,body:o}=IR(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"put",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};deleteTask=async t=>{let{path:r,headers:a,query:n,body:o}=PR(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"delete",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};listTasks=async t=>{let{path:r,headers:a,query:n,body:o}=AR(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};createWorkflow=async t=>{let{path:r,headers:a,query:n,body:o}=BR(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getWorkflow=async t=>{let{path:r,headers:a,query:n,body:o}=wR(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};updateWorkflow=async t=>{let{path:r,headers:a,query:n,body:o}=GR(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"put",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};deleteWorkflow=async t=>{let{path:r,headers:a,query:n,body:o}=LR(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"delete",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};listWorkflows=async t=>{let{path:r,headers:a,query:n,body:o}=SR(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getOrCreateWorkflow=async t=>{let{path:r,headers:a,query:n,body:o}=HR(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};listTagValues=async t=>{let{path:r,headers:a,query:n,body:o}=_R(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};trackAnalytics=async t=>{let{path:r,headers:a,query:n,body:o}=MR(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};runVrl=async t=>{let{path:r,headers:a,query:n,body:o}=VR(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getAccount=async t=>{let{path:r,headers:a,query:n,body:o}=KR(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};updateAccount=async t=>{let{path:r,headers:a,query:n,body:o}=jR(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"put",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};listPersonalAccessTokens=async t=>{let{path:r,headers:a,query:n,body:o}=JR(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};createPersonalAccessToken=async t=>{let{path:r,headers:a,query:n,body:o}=ZR(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};deletePersonalAccessToken=async t=>{let{path:r,headers:a,query:n,body:o}=em(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"delete",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};setAccountPreference=async t=>{let{path:r,headers:a,query:n,body:o}=sm(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getAccountPreference=async t=>{let{path:r,headers:a,query:n,body:o}=am(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};listPublicIntegrations=async t=>{let{path:r,headers:a,query:n,body:o}=om(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getPublicIntegrationById=async t=>{let{path:r,headers:a,query:n,body:o}=pm(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getPublicIntegration=async t=>{let{path:r,headers:a,query:n,body:o}=cm(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};listPublicPlugins=async t=>{let{path:r,headers:a,query:n,body:o}=lm(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getPublicPluginById=async t=>{let{path:r,headers:a,query:n,body:o}=ym(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getPublicPlugin=async t=>{let{path:r,headers:a,query:n,body:o}=mm(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getPublicPluginCode=async t=>{let{path:r,headers:a,query:n,body:o}=hm(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};listPublicInterfaces=async t=>{let{path:r,headers:a,query:n,body:o}=fm(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getPublicInterfaceById=async t=>{let{path:r,headers:a,query:n,body:o}=Im(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getPublicInterface=async t=>{let{path:r,headers:a,query:n,body:o}=Pm(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};createBot=async t=>{let{path:r,headers:a,query:n,body:o}=Am(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};updateBot=async t=>{let{path:r,headers:a,query:n,body:o}=Bm(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"put",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};transferBot=async t=>{let{path:r,headers:a,query:n,body:o}=wm(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};listBots=async t=>{let{path:r,headers:a,query:n,body:o}=Gm(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getBot=async t=>{let{path:r,headers:a,query:n,body:o}=Lm(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};deleteBot=async t=>{let{path:r,headers:a,query:n,body:o}=Sm(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"delete",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getBotLogs=async t=>{let{path:r,headers:a,query:n,body:o}=Hm(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getBotWebchat=async t=>{let{path:r,headers:a,query:n,body:o}=_m(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getBotAnalytics=async t=>{let{path:r,headers:a,query:n,body:o}=Mm(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getBotIssue=async t=>{let{path:r,headers:a,query:n,body:o}=Vm(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};listBotIssues=async t=>{let{path:r,headers:a,query:n,body:o}=Km(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};deleteBotIssue=async t=>{let{path:r,headers:a,query:n,body:o}=jm(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"delete",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};listBotIssueEvents=async t=>{let{path:r,headers:a,query:n,body:o}=Jm(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};listBotVersions=async t=>{let{path:r,headers:a,query:n,body:o}=Zm(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getBotVersion=async t=>{let{path:r,headers:a,query:n,body:o}=eq(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};createBotVersion=async t=>{let{path:r,headers:a,query:n,body:o}=sq(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};deployBotVersion=async t=>{let{path:r,headers:a,query:n,body:o}=aq(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};createIntegrationShareableId=async t=>{let{path:r,headers:a,query:n,body:o}=oq(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};deleteIntegrationShareableId=async t=>{let{path:r,headers:a,query:n,body:o}=pq(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"delete",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getIntegrationShareableId=async t=>{let{path:r,headers:a,query:n,body:o}=cq(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};unlinkSandboxedConversations=async t=>{let{path:r,headers:a,query:n,body:o}=lq(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"delete",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};listBotApiKeys=async t=>{let{path:r,headers:a,query:n,body:o}=yq(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};createBotApiKey=async t=>{let{path:r,headers:a,query:n,body:o}=mq(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};deleteBotApiKey=async t=>{let{path:r,headers:a,query:n,body:o}=hq(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"delete",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};listWorkspaceInvoices=async t=>{let{path:r,headers:a,query:n,body:o}=fq(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getUpcomingInvoice=async t=>{let{path:r,headers:a,query:n,body:o}=Iq(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};chargeWorkspaceUnpaidInvoices=async t=>{let{path:r,headers:a,query:n,body:o}=Pq(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};createWorkspace=async t=>{let{path:r,headers:a,query:n,body:o}=Aq(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getPublicWorkspace=async t=>{let{path:r,headers:a,query:n,body:o}=Bq(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getWorkspace=async t=>{let{path:r,headers:a,query:n,body:o}=wq(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};listWorkspaceUsages=async t=>{let{path:r,headers:a,query:n,body:o}=Gq(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};breakDownWorkspaceUsageByBot=async t=>{let{path:r,headers:a,query:n,body:o}=Lq(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getAllWorkspaceQuotaCompletion=async t=>{let{path:r,headers:a,query:n,body:o}=Sq(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getWorkspaceQuota=async t=>{let{path:r,headers:a,query:n,body:o}=Hq(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};listWorkspaceQuotas=async t=>{let{path:r,headers:a,query:n,body:o}=_q(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};updateWorkspace=async t=>{let{path:r,headers:a,query:n,body:o}=Mq(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"put",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};checkHandleAvailability=async t=>{let{path:r,headers:a,query:n,body:o}=Vq(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"put",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};listWorkspaces=async t=>{let{path:r,headers:a,query:n,body:o}=Kq(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};listPublicWorkspaces=async t=>{let{path:r,headers:a,query:n,body:o}=jq(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};deleteWorkspace=async t=>{let{path:r,headers:a,query:n,body:o}=Jq(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"delete",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getAuditRecords=async t=>{let{path:r,headers:a,query:n,body:o}=Zq(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};listWorkspaceMembers=async t=>{let{path:r,headers:a,query:n,body:o}=eh(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getWorkspaceMember=async t=>{let{path:r,headers:a,query:n,body:o}=sh(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};deleteWorkspaceMember=async t=>{let{path:r,headers:a,query:n,body:o}=ah(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"delete",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};createWorkspaceMember=async t=>{let{path:r,headers:a,query:n,body:o}=oh(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};updateWorkspaceMember=async t=>{let{path:r,headers:a,query:n,body:o}=ph(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"put",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};listIntegrationApiKeys=async t=>{let{path:r,headers:a,query:n,body:o}=ch(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};createIntegrationApiKey=async t=>{let{path:r,headers:a,query:n,body:o}=lh(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};deleteIntegrationApiKey=async t=>{let{path:r,headers:a,query:n,body:o}=yh(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"delete",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};createIntegration=async t=>{let{path:r,headers:a,query:n,body:o}=mh(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};validateIntegrationCreation=async t=>{let{path:r,headers:a,query:n,body:o}=hh(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};updateIntegration=async t=>{let{path:r,headers:a,query:n,body:o}=fh(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"put",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};validateIntegrationUpdate=async t=>{let{path:r,headers:a,query:n,body:o}=Ih(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"put",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};listIntegrations=async t=>{let{path:r,headers:a,query:n,body:o}=Ph(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getIntegration=async t=>{let{path:r,headers:a,query:n,body:o}=Ah(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getIntegrationLogs=async t=>{let{path:r,headers:a,query:n,body:o}=Bh(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getIntegrationByName=async t=>{let{path:r,headers:a,query:n,body:o}=wh(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};deleteIntegration=async t=>{let{path:r,headers:a,query:n,body:o}=Gh(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"delete",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};requestIntegrationVerification=async t=>{let{path:r,headers:a,query:n,body:o}=Lh(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};createInterface=async t=>{let{path:r,headers:a,query:n,body:o}=Sh(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getInterface=async t=>{let{path:r,headers:a,query:n,body:o}=Hh(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getInterfaceByName=async t=>{let{path:r,headers:a,query:n,body:o}=_h(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};updateInterface=async t=>{let{path:r,headers:a,query:n,body:o}=Mh(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"put",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};deleteInterface=async t=>{let{path:r,headers:a,query:n,body:o}=Vh(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"delete",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};listInterfaces=async t=>{let{path:r,headers:a,query:n,body:o}=Kh(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};createPlugin=async t=>{let{path:r,headers:a,query:n,body:o}=jh(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getPlugin=async t=>{let{path:r,headers:a,query:n,body:o}=Jh(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getPluginByName=async t=>{let{path:r,headers:a,query:n,body:o}=Zh(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};updatePlugin=async t=>{let{path:r,headers:a,query:n,body:o}=eb(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"put",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};deletePlugin=async t=>{let{path:r,headers:a,query:n,body:o}=sb(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"delete",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};listPlugins=async t=>{let{path:r,headers:a,query:n,body:o}=ab(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getPluginCode=async t=>{let{path:r,headers:a,query:n,body:o}=ob(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getUsage=async t=>{let{path:r,headers:a,query:n,body:o}=pb(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getMultipleUsages=async t=>{let{path:r,headers:a,query:n,body:o}=cb(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};listUsageHistory=async t=>{let{path:r,headers:a,query:n,body:o}=lb(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};listUsageActivity=async t=>{let{path:r,headers:a,query:n,body:o}=yb(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};listUsageActivityDaily=async t=>{let{path:r,headers:a,query:n,body:o}=mb(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};changeAISpendQuota=async t=>{let{path:r,headers:a,query:n,body:o}=hb(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"put",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};listActivities=async t=>{let{path:r,headers:a,query:n,body:o}=fb(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};introspect=async t=>{let{path:r,headers:a,query:n,body:o}=Ib(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};upsertFile=async t=>{let{path:r,headers:a,query:n,body:o}=Pb(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"put",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};deleteFile=async t=>{let{path:r,headers:a,query:n,body:o}=Ab(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"delete",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};listFiles=async t=>{let{path:r,headers:a,query:n,body:o}=Bb(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getFile=async t=>{let{path:r,headers:a,query:n,body:o}=wb(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};updateFileMetadata=async t=>{let{path:r,headers:a,query:n,body:o}=Gb(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"put",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};copyFile=async t=>{let{path:r,headers:a,query:n,body:o}=Lb(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};searchFiles=async t=>{let{path:r,headers:a,query:n,body:o}=Sb(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};listFilePassages=async t=>{let{path:r,headers:a,query:n,body:o}=Hb(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};setFilePassages=async t=>{let{path:r,headers:a,query:n,body:o}=_b(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"put",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};listFileTags=async t=>{let{path:r,headers:a,query:n,body:o}=Mb(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};listFileTagValues=async t=>{let{path:r,headers:a,query:n,body:o}=Vb(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};createKnowledgeBase=async t=>{let{path:r,headers:a,query:n,body:o}=Kb(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};deleteKnowledgeBase=async t=>{let{path:r,headers:a,query:n,body:o}=jb(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"delete",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};updateKnowledgeBase=async t=>{let{path:r,headers:a,query:n,body:o}=Jb(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"put",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};listKnowledgeBases=async t=>{let{path:r,headers:a,query:n,body:o}=Zb(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};listTables=async t=>{let{path:r,headers:a,query:n,body:o}=ef(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getTable=async t=>{let{path:r,headers:a,query:n,body:o}=sf(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getOrCreateTable=async t=>{let{path:r,headers:a,query:n,body:o}=af(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};createTable=async t=>{let{path:r,headers:a,query:n,body:o}=of(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};duplicateTable=async t=>{let{path:r,headers:a,query:n,body:o}=uf(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};exportTable=async t=>{let{path:r,headers:a,query:n,body:o}=df(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getTableJobs=async t=>{let{path:r,headers:a,query:n,body:o}=gf(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};importTable=async t=>{let{path:r,headers:a,query:n,body:o}=Rf(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};updateTable=async t=>{let{path:r,headers:a,query:n,body:o}=qf(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"put",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};renameTableColumn=async t=>{let{path:r,headers:a,query:n,body:o}=bf(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"put",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};deleteTable=async t=>{let{path:r,headers:a,query:n,body:o}=xf(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"delete",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getTableRow=async t=>{let{path:r,headers:a,query:n,body:o}=kf(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};findTableRows=async t=>{let{path:r,headers:a,query:n,body:o}=vf(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};createTableRows=async t=>{let{path:r,headers:a,query:n,body:o}=Tf(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};deleteTableRows=async t=>{let{path:r,headers:a,query:n,body:o}=Cf(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};updateTableRows=async t=>{let{path:r,headers:a,query:n,body:o}=Uf(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"put",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};upsertTableRows=async t=>{let{path:r,headers:a,query:n,body:o}=Ef(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})}};function d(e){return Wf.default.isAxiosError(e)&&e.response?.data?ue(e.response.data):ue(e)}var ka=class extends Qt{config;constructor(t={}){let r=F.getClientConfig(t),a=M.createAxios(r),n=Sf.default.create(a);super(n,{toApiError:O.toApiError}),t.retry&&V(n,t.retry),this.config=r}get list(){return{conversations:t=>new h.AsyncCollection(({nextToken:r})=>this.listConversations({nextToken:r,...t}).then(a=>({...a,items:a.conversations}))),participants:t=>new h.AsyncCollection(({nextToken:r})=>this.listParticipants({nextToken:r,...t}).then(a=>({...a,items:a.participants}))),events:t=>new h.AsyncCollection(({nextToken:r})=>this.listEvents({nextToken:r,...t}).then(a=>({...a,items:a.events}))),messages:t=>new h.AsyncCollection(({nextToken:r})=>this.listMessages({nextToken:r,...t}).then(a=>({...a,items:a.messages}))),users:t=>new h.AsyncCollection(({nextToken:r})=>this.listUsers({nextToken:r,...t}).then(a=>({...a,items:a.users}))),tasks:t=>new h.AsyncCollection(({nextToken:r})=>this.listTasks({nextToken:r,...t}).then(a=>({...a,items:a.tasks}))),publicIntegrations:t=>new h.AsyncCollection(({nextToken:r})=>this.listPublicIntegrations({nextToken:r,...t}).then(a=>({...a,items:a.integrations}))),bots:t=>new h.AsyncCollection(({nextToken:r})=>this.listBots({nextToken:r,...t}).then(a=>({...a,items:a.bots}))),botIssues:t=>new h.AsyncCollection(({nextToken:r})=>this.listBotIssues({nextToken:r,...t}).then(a=>({...a,items:a.issues}))),workspaces:t=>new h.AsyncCollection(({nextToken:r})=>this.listWorkspaces({nextToken:r,...t}).then(a=>({...a,items:a.workspaces}))),publicWorkspaces:t=>new h.AsyncCollection(({nextToken:r})=>this.listPublicWorkspaces({nextToken:r,...t}).then(a=>({...a,items:a.workspaces}))),workspaceMembers:t=>new h.AsyncCollection(({nextToken:r})=>this.listWorkspaceMembers({nextToken:r,...t}).then(a=>({...a,items:a.members}))),integrations:t=>new h.AsyncCollection(({nextToken:r})=>this.listIntegrations({nextToken:r,...t}).then(a=>({...a,items:a.integrations}))),interfaces:t=>new h.AsyncCollection(({nextToken:r})=>this.listInterfaces({nextToken:r,...t}).then(a=>({...a,items:a.interfaces}))),activities:t=>new h.AsyncCollection(({nextToken:r})=>this.listActivities({nextToken:r,...t}).then(a=>({...a,items:a.activities}))),files:t=>new h.AsyncCollection(({nextToken:r})=>this.listFiles({nextToken:r,...t}).then(a=>({...a,items:a.files}))),filePassages:t=>new h.AsyncCollection(({nextToken:r})=>this.listFilePassages({nextToken:r,...t}).then(a=>({...a,items:a.passages}))),usageActivity:t=>new h.AsyncCollection(({nextToken:r})=>this.listUsageActivity({nextToken:r,...t}).then(a=>({...a,items:a.data}))),usageActivityDaily:t=>new h.AsyncCollection(({nextToken:r})=>this.listUsageActivityDaily({nextToken:r,...t}).then(a=>({...a,items:a.data})))}}uploadFile=async t=>await Wt(this,t)};0&&(module.exports={AlreadyExistsError,BreakingChangesError,Client,ForbiddenError,InternalError,InvalidDataFormatError,InvalidIdentifierError,InvalidJsonSchemaError,InvalidPayloadError,InvalidQueryError,LimitExceededError,MethodNotFoundError,PayloadTooLargeError,PaymentRequiredError,QuotaExceededError,RateLimitedError,ReferenceConstraintError,ReferenceNotFoundError,RelationConflictError,ResourceLockedConflictError,ResourceNotFoundError,RuntimeError,UnauthorizedError,UnknownError,UnsupportedMediaTypeError,UploadFileError,admin,axios,axiosRetry,errorFrom,files,isApiError,runtime,tables});
//# sourceMappingURL=index.cjs.map
