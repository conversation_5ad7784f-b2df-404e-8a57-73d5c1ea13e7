const db = require('../db');

class Reservation {
  static async createTable() {
    const sql = `
      CREATE TABLE IF NOT EXISTS reservations (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id VARCHAR(255) NOT NULL,
        customer_name VARCHAR(255) NOT NULL,
        phone VARCHAR(20),
        email VARCHAR(255),
        date DATE NOT NULL,
        time TIME NOT NULL,
        party_size INT NOT NULL,
        special_requests TEXT,
        status ENUM('pending', 'confirmed', 'cancelled', 'completed') DEFAULT 'pending',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      )
    `;
    
    try {
      await db.query(sql);
      console.log('Reservations table created or already exists');
    } catch (error) {
      console.error('Error creating reservations table:', error);
      throw error;
    }
  }

  static async create(reservationData) {
    const sql = `
      INSERT INTO reservations 
      (user_id, customer_name, phone, email, date, time, party_size, special_requests, status) 
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;
    
    try {
      const [result] = await db.query(sql, [
        reservationData.userId,
        reservationData.customerName,
        reservationData.phone || null,
        reservationData.email || null,
        reservationData.date,
        reservationData.time,
        reservationData.partySize,
        reservationData.special_requests || null,
        reservationData.status || 'pending'
      ]);
      return result.insertId;
    } catch (error) {
      console.error('Error creating reservation:', error);
      throw error;
    }
  }

  static async getByUserId(userId) {
    const sql = 'SELECT * FROM reservations WHERE user_id = ? ORDER BY date DESC, time DESC';
    try {
      const [rows] = await db.query(sql, [userId]);
      return rows;
    } catch (error) {
      console.error('Error fetching reservations:', error);
      throw error;
    }
  }

  static async updateStatus(reservationId, status) {
    const sql = 'UPDATE reservations SET status = ? WHERE id = ?';
    try {
      const [result] = await db.query(sql, [status, reservationId]);
      return result.affectedRows > 0;
    } catch (error) {
      console.error('Error updating reservation status:', error);
      throw error;
    }
  }

  static async update(reservationId, updateData) {
    const sql = `
      UPDATE reservations 
      SET date = ?, time = ?, party_size = ?, special_requests = ?
      WHERE id = ?
    `;
    try {
      const [result] = await db.query(sql, [
        updateData.date,
        updateData.time,
        updateData.partySize,
        updateData.specialRequests,
        reservationId
      ]);
      return result.affectedRows > 0;
    } catch (error) {
      console.error('Error updating reservation:', error);
      throw error;
    }
  }

  static async delete(reservationId) {
    const sql = 'DELETE FROM reservations WHERE id = ?';
    try {
      const [result] = await db.query(sql, [reservationId]);
      return result.affectedRows > 0;
    } catch (error) {
      console.error('Error deleting reservation:', error);
      throw error;
    }
  }

  static async findByEmail(email, date, time) {
    const sql = `
      SELECT * FROM reservations 
      WHERE email = ? AND date = ? AND time = ?
    `;
    
    try {
      const [rows] = await db.query(sql, [email, date, time]);
      return rows.length > 0 ? rows[0] : null;
    } catch (error) {
      console.error('Error finding reservation by email:', error);
      throw error;
    }
  }
}

module.exports = Reservation; 

