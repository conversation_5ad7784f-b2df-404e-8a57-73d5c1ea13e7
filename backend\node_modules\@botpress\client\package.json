{"name": "@botpress/client", "version": "1.15.1", "description": "Botpress Client", "main": "./dist/index.cjs", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "license": "MIT", "browser": {"crypto": false, "http": false, "https": false}, "scripts": {"check:type": "tsc --noEmit", "build:type": "tsup --tsconfig tsconfig.build.json ./src/index.ts --dts-resolve --dts-only", "build:browser": "ts-node -T ./build.ts --browser", "build:node": "ts-node -T ./build.ts --node", "build:bundle": "ts-node -T ./build.ts --bundle", "build": "pnpm build:type && pnpm build:node && pnpm build:browser && pnpm build:bundle", "generate": "ts-node ./openapi.ts", "test:e2e": "ts-node -T ./e2e/node.ts"}, "dependencies": {"axios": "^1.6.1", "axios-retry": "^4.5.0", "browser-or-node": "^2.1.1", "qs": "^6.11.0"}, "devDependencies": {"@types/qs": "^6.9.7", "esbuild": "^0.16.12", "lodash": "^4.17.21", "tsup": "^8.0.2"}, "engines": {"node": ">=18.0.0"}, "packageManager": "pnpm@8.6.2"}