const mysql = require('mysql2');
require('dotenv').config();

console.log('Testing MySQL connection...');
console.log('DB_HOST:', process.env.DB_HOST);
console.log('DB_USER:', process.env.DB_USER);
console.log('DB_NAME:', process.env.DB_NAME);

const connection = mysql.createConnection({
  host: process.env.DB_HOST || 'localhost',
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD,
  database: process.env.DB_NAME || 'abraham_restaurant'
});

connection.connect((err) => {
  if (err) {
    console.error('❌ MySQL connection failed:', err.message);
    console.error('Error code:', err.code);
    console.error('Error errno:', err.errno);
    process.exit(1);
  } else {
    console.log('✅ MySQL connection successful!');
    
    // Test a simple query
    connection.query('SELECT 1 as test', (err, results) => {
      if (err) {
        console.error('❌ Query failed:', err.message);
      } else {
        console.log('✅ Query successful:', results);
      }
      connection.end();
      process.exit(0);
    });
  }
});
