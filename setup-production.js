const fs = require('fs');
const path = require('path');

console.log('🚀 Setting up production configuration for Botpress integration...\n');

// Create production environment file
const productionEnv = `# Production Environment Variables for Abraham Restaurant
# Copy these to your production server

# Database Configuration
DB_HOST=your-production-db-host
DB_USER=your-production-db-user
DB_PASSWORD=your-production-db-password
DB_NAME=abraham_restaurant

# MongoDB (if using)
DB=mongodb://your-production-mongo-url/theabraham-restaurant

# Server Configuration
PORT=8080
JWT_SECRET_KEY=your-super-secure-jwt-secret-key-here

# Botpress Configuration
BOTPRESS_API_URL=https://your-production-domain.com
BOTPRESS_PAT=${process.env.BOTPRESS_PAT || 'your-botpress-personal-access-token'}
BOTPRESS_BOT_ID=${process.env.BOTPRESS_BOT_ID || 'your-bot-id'}
BOTPRESS_WORKSPACE_ID=${process.env.BOTPRESS_WORKSPACE_ID || 'your-workspace-id'}

# CORS Configuration
ALLOWED_ORIGINS=https://your-frontend-domain.com,https://cdn.botpress.cloud

# Security
NODE_ENV=production
`;

// Create deployment guide
const deploymentGuide = `# Deployment Guide for Abraham Restaurant Botpress Integration

## Current Status ✅
Your local integration is working perfectly! Here's what's set up:

### Backend Endpoints:
- ✅ http://localhost:8080/api/botpress-reservations
- ✅ http://localhost:8080/api/botpress/webhook  
- ✅ Database tables created and working
- ✅ Integration tested successfully

### Frontend:
- ✅ Chatbot embedded in React app
- ✅ Scripts loaded from Botpress Cloud

## For Production Deployment:

### 1. Deploy Backend to Cloud
Choose one of these platforms:

**Heroku:**
\`\`\`bash
# Install Heroku CLI, then:
heroku create abraham-restaurant-api
heroku config:set NODE_ENV=production
heroku config:set DB_HOST=your-db-host
heroku config:set DB_USER=your-db-user
heroku config:set DB_PASSWORD=your-db-password
heroku config:set DB_NAME=abraham_restaurant
heroku config:set JWT_SECRET_KEY=your-secret-key
git push heroku main
\`\`\`

**Railway:**
\`\`\`bash
# Install Railway CLI, then:
railway login
railway init
railway add mysql
railway deploy
\`\`\`

**DigitalOcean App Platform:**
- Connect your GitHub repo
- Set environment variables in dashboard
- Deploy automatically

### 2. Update Botpress Studio Action
In your Botpress Studio action, change:
\`\`\`javascript
// From:
const API_URL = 'http://localhost:8080/api/botpress-reservations';

// To:
const API_URL = 'https://your-production-domain.com/api/botpress-reservations';
\`\`\`

### 3. Database Setup
Make sure your production database has:
- ✅ reservations table
- ✅ botpress_reservations table  
- ✅ All required columns including review columns

### 4. Test Production
\`\`\`bash
# Test your production API
curl -X POST https://your-production-domain.com/api/botpress-reservations \\
  -H "Content-Type: application/json" \\
  -d '{
    "customerName": "Test User",
    "email": "<EMAIL>", 
    "phone": "555-0123",
    "date": "2024-12-25",
    "time": "19:30",
    "partySize": 4,
    "specialRequests": "Test reservation"
  }'
\`\`\`

## Security Checklist:
- [ ] Use HTTPS in production
- [ ] Set proper CORS origins
- [ ] Use environment variables for secrets
- [ ] Enable database SSL
- [ ] Set up monitoring/logging

## Monitoring:
Add these to track your reservations:
- Database monitoring
- API endpoint monitoring  
- Error logging (Sentry, LogRocket, etc.)
- Reservation analytics

## Support:
If you need help with deployment:
1. Check the logs for any errors
2. Verify all environment variables are set
3. Test database connectivity
4. Ensure Botpress can reach your API

Your integration is ready for production! 🎉
`;

// Write files
try {
  fs.writeFileSync('.env.production', productionEnv);
  fs.writeFileSync('DEPLOYMENT.md', deploymentGuide);
  
  console.log('✅ Created .env.production template');
  console.log('✅ Created DEPLOYMENT.md guide');
  console.log('\n📋 Summary of your Botpress integration:');
  console.log('');
  console.log('🔧 Backend Setup: COMPLETE ✅');
  console.log('   - API endpoints working');
  console.log('   - Database tables created');
  console.log('   - Webhook handlers ready');
  console.log('');
  console.log('🤖 Botpress Studio Setup: READY ✅');
  console.log('   - Action code provided in botpress-studio-action.js');
  console.log('   - Integration guide in botpress-integration-guide.md');
  console.log('   - Test data working perfectly');
  console.log('');
  console.log('🌐 Frontend Setup: COMPLETE ✅');
  console.log('   - Chatbot embedded in React app');
  console.log('   - Scripts loaded from Botpress Cloud');
  console.log('');
  console.log('📝 Next Steps:');
  console.log('1. Copy code from botpress-studio-action.js to Botpress Studio');
  console.log('2. Create conversation flow as described in guide');
  console.log('3. Test with embedded chatbot');
  console.log('4. Deploy to production when ready');
  console.log('');
  console.log('🎉 Your Botpress integration is ready to use!');
  
} catch (error) {
  console.error('❌ Error creating files:', error.message);
}

// Clean up test files
try {
  if (fs.existsSync('test-botpress-integration.js')) {
    fs.unlinkSync('test-botpress-integration.js');
    console.log('🧹 Cleaned up test files');
  }
} catch (error) {
  console.log('Note: Could not clean up test files');
}

console.log('\n🔗 Files created:');
console.log('- .env.production (production environment template)');
console.log('- DEPLOYMENT.md (deployment guide)');
console.log('- botpress-integration-guide.md (complete setup guide)');
console.log('- botpress-studio-action.js (copy this to Botpress Studio)');
