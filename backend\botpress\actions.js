const axios = require('axios');
require('dotenv').config();

/**
 * This file contains custom actions that can be called from Botpress flows
 */

const BOTPRESS_API_URL = process.env.BOTPRESS_API_URL || 'http://localhost:5000/api';

module.exports = {
  // Create a new reservation from Botpress conversation
  createReservation: async (data) => {
    try {
      const reservationData = {
        customerName: data.customer.name,
        phone: data.customer.phone,
        email: data.customer.email || '',
        date: data.reservation.date,
        time: data.reservation.time,
        partySize: parseInt(data.reservation.partySize),
        special_requests: data.reservation.special_requests || '',
        source: 'botpress'
      };

      const response = await axios.post(`${BOTPRESS_API_URL}/reservations/bot`, reservationData);
      return {
        success: true,
        reservationId: response.data.id
      };
    } catch (error) {
      console.error('Error creating reservation from Botpress:', error);
      return {
        success: false,
        error: error.message
      };
    }
  },

  // Check table availability
  checkAvailability: async (data) => {
    try {
      const params = {
        date: data.date,
        time: data.time,
        partySize: data.partySize
      };

      const response = await axios.get(`${BOTPRESS_API_URL}/reservations/availability`, { params });
      return {
        success: true,
        available: response.data.available,
        message: response.data.message
      };
    } catch (error) {
      console.error('Error checking availability:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }
}; 
