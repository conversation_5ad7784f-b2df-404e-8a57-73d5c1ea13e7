.app__navbar {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: var(--color-black);
    padding: 1rem 2rem;
    
    /* Add these properties to make the navbar fixed */
    position: fixed;
    top: 0;
    left: 0;
    z-index: 1000; /* Ensure navbar stays above other content */
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2); /* Optional: adds a subtle shadow */
}

.app__navbar-logo {
    display: flex;
    justify-content: flex-start;
    align-items: center;
}

.app__navbar-logo img {
    width: 150px;
}

.app__navbar-links {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;

    list-style: none;
}

.app__navbar-links li {
    margin: 0 1rem;
    cursor: pointer;
}

.app__navbar-links li:hover {
    color: var(--color-gray);
}

.app__navbar-login {
    display: flex;
    justify-content: flex-end;
    align-items: center;
}

.app__navbar-login a {
    margin: 0 1rem;
    text-decoration: none;
    transition: .5s ease;
}

.app__navbar-login a:hover {
    border-bottom: 1px solid var(--color-golden);
}

.app__navbar-login div {
    width: 1px;
    height: 30px;
    background: var(--color-gray);
}

.app__navbar-smallscreen {
    display: none;
}

.app__navbar-smallscreen_overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    background: var(--color-black);
    transition: .5s ease;

    flex-direction: column;
    z-index: 5;
}

.app__navbar-smallscreen_overlay .overlay__close {
    font-size: 27px;
    color: var(--color-golden);
    cursor: pointer;

    position: absolute;
    top: 20px;
    right: 20px;
}

.app__navbar-smallscreen_links {
    list-style: none;
}

.app__navbar-smallscreen_links li {
    margin: 2rem;
    cursor: pointer;
    color: var(--color-golden);
    font-size: 2rem;
    text-align: center;
    font-family: var(--font-base);
}

.app__navbar-smallscreen_links li:hover {
    color: var(--color-white);
}

.app__navbar-welcome {
    max-width: 150px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    display: inline-block;
    margin-right: 10px; /* Add right margin to move it left relative to the divider */
    text-align: right; /* Align text to the right */
}

/* For smaller screens, reduce the max-width */
@media screen and (max-width: 1150px) {
    .app__navbar-welcome {
        max-width: 120px;
    }
}

@media screen and (max-width: 650px) {
    .app__navbar-welcome {
        max-width: 100px;
    }
}

@media screen and (min-width: 2000px) {
    .app__navbar-logo img {
        width: 210px;
    }
}

@media screen and (max-width: 1150px) {
    .app__navbar-links {
        display: none;
    }

    .app__navbar-smallscreen {
        display: flex;
    }
}

@media screen and (max-width: 650px) {
    .app__navbar {
        padding: 1rem;
    }

    .app__navbar-login {
        display: none;
    }

    .app__navbar-logo img {
        width: 110px;
    }
}

.app__navbar-link-button {
    background: none;
    border: none;
    color: var(--color-white);
    margin: 0 1rem;
    text-decoration: none;
    cursor: pointer;
    font: inherit;
    transition: .5s ease;
    padding: 0.2em 0.5em;
}

.app__navbar-link-button:hover {
    border-bottom: 1px solid var(--color-golden);
    color: var(--color-golden);
}

/* Admin link styling */
.admin-link {
    color: var(--color-golden) !important;
    font-weight: 600;
    text-decoration: none;
    padding: 5px 10px;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.admin-link:hover {
    background-color: rgba(220, 202, 135, 0.1);
    transform: translateY(-1px);
}
