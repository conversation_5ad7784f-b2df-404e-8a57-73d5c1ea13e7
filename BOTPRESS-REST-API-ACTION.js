/**
 * <PERSON> Restaurant - REST API Botpress Action
 * This version uses the REST API backend bridge approach
 * 
 * SIMPLIFIED VERSION - Only uses the 4 required parameters from your Botpress workflow:
 * - confNumber
 * - dateTime  
 * - email
 * - partySize
 * 
 * COPY THIS CODE TO BOTPRESS STUDIO:
 * File: src/actions/saveReservationToMySQL.js
 */

const saveReservationToMySQL = async ({ confNumber, dateTime, email, partySize }) => {
  const axios = require('axios');
  
  try {
    console.log('🍽️ Abraham Restaurant - REST API Bridge');
    console.log('Received from Botpress:', { confNumber, dateTime, email, partySize });

    // Your backend API endpoint (running on port 8080)
    const API_URL = 'http://localhost:8080/api/botpress-reservations';
    
    // Validate required fields from your Botpress workflow
    if (!email || !dateTime || !partySize) {
      throw new Error('Missing required fields: email, dateTime, partySize');
    }

    // Prepare data for the REST API
    const reservationData = {
      email: email,
      datetime: dateTime, // Send as-is, API will handle parsing
      partySize: parseInt(partySize)
    };

    console.log('📤 Sending to REST API Backend');
    console.log('📝 API URL:', API_URL);
    console.log('📝 Reservation data:', reservationData);

    // Send to your REST API backend
    const response = await axios.post(API_URL, reservationData, {
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'User-Agent': 'Botpress-Abraham-Restaurant-REST-v1.0'
      },
      timeout: 15000 // 15 second timeout
    });

    console.log('✅ SUCCESS: Reservation saved via REST API!');
    console.log('📊 API response:', response.data);

    // Return success response to Botpress chatbot
    return {
      success: true,
      reservationId: response.data.reservationId,
      botpressId: response.data.id,
      confNumber: confNumber || `AR-${Date.now()}`,
      message: `🎉 Perfect! Your reservation has been confirmed!\n\n📅 Date & Time: ${dateTime}\n👥 Party Size: ${partySize} people\n📧 Email: ${email}\n🔢 Confirmation: ${confNumber || 'AR-' + Date.now()}\n\nWe look forward to serving you at Abraham Restaurant! 🍽️`,
      details: response.data
    };

  } catch (error) {
    console.error('❌ ERROR: Failed to save reservation via REST API');
    console.error('Error message:', error.message);
    console.error('Full error:', error);
    
    // Return error response to Botpress chatbot
    return {
      success: false,
      error: error.message,
      confNumber: confNumber || `AR-ERROR-${Date.now()}`,
      message: `I apologize, but there was a technical issue saving your reservation.\n\n📝 Your details:\n📧 Email: ${email}\n📅 Date & Time: ${dateTime}\n👥 Party Size: ${partySize}\n\n📞 Please call us at (555) 123-4567 to confirm manually.\n\n🔢 Reference: ${confNumber || 'AR-ERROR-' + Date.now()}`
    };
  }
};

module.exports = { saveReservationToMySQL };

/*
🚀 SETUP INSTRUCTIONS:

1. ✅ Make sure your backend server is running:
   - Open terminal in backend folder
   - Run: npm start
   - Server should start on port 8080

2. ✅ Copy this code to Botpress Studio:
   - Go to src/actions/saveReservationToMySQL.js
   - Replace existing code with this code

3. ✅ In your Botpress flow, add Execute Code node with:
   - confNumber: {{workflow.confNumber}}
   - dateTime: {{workflow.dateTime}}
   - email: {{workflow.email}}
   - partySize: {{workflow.partySize}}

4. ✅ Add conditions after Execute Code:
   - Success condition: {{workflow.success}} === true
   - Error condition: {{workflow.success}} === false

5. ✅ Add response messages:
   - Success: {{workflow.message}}
   - Error: {{workflow.message}}

6. ✅ Test your chatbot!

🔧 BENEFITS OF REST API APPROACH:
- ✅ Cleaner separation of concerns
- ✅ Better error handling
- ✅ Easier to debug and maintain
- ✅ More secure database access
- ✅ Scalable architecture
- ✅ Can be used by multiple clients
*/
