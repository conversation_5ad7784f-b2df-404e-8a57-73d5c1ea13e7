/**
 * <PERSON> Restaurant - REST API Botpress Action
 * FIXED VERSION for Botpress Studio
 *
 * COPY THIS EXACT CODE TO BOTPRESS STUDIO:
 * File: src/actions/saveReservationToMySQL.js
 */

// Botpress Action Function
async function saveReservationToMySQL({ confNumber, dateTime, email, partySize }) {
  try {
    console.log('🍽️ Abraham Restaurant - REST API Bridge');
    console.log('Received from Botpress:', { confNumber, dateTime, email, partySize });

    // Your backend API endpoint (running on port 8080)
    const API_URL = 'http://localhost:8080/api/botpress-reservations';

    // Validate required fields from your Botpress workflow
    if (!email || !dateTime || !partySize) {
      throw new Error('Missing required fields: email, dateTime, partySize');
    }

    // Prepare data for the REST API
    const reservationData = {
      email: email,
      datetime: dateTime, // Send as-is, API will handle parsing
      partySize: parseInt(partySize)
    };

    console.log('📤 Sending to REST API Backend');
    console.log('📝 API URL:', API_URL);
    console.log('📝 Reservation data:', reservationData);

    // Use fetch instead of axios (Botpress has fetch built-in)
    const response = await fetch(API_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'User-Agent': 'Botpress-Abraham-Restaurant-REST-v1.0'
      },
      body: JSON.stringify(reservationData)
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const responseData = await response.json();

    console.log('✅ SUCCESS: Reservation saved via REST API!');
    console.log('📊 API response:', responseData);

    // Return success response to Botpress workflow
    workflow.success = true;
    workflow.reservationId = responseData.reservationId || 'N/A';
    workflow.botpressId = responseData.id || 'N/A';
    workflow.confNumber = confNumber || `AR-${Date.now()}`;
    workflow.message = `🎉 Perfect! Your reservation has been confirmed!\n\n📅 Date & Time: ${dateTime}\n👥 Party Size: ${partySize} people\n📧 Email: ${email}\n🔢 Confirmation: ${confNumber || 'AR-' + Date.now()}\n\nWe look forward to serving you at Abraham Restaurant! 🍽️`;
    workflow.details = responseData;

  } catch (error) {
    console.error('❌ ERROR: Failed to save reservation via REST API');
    console.error('Error message:', error.message);
    console.error('Full error:', error);

    // Return error response to Botpress workflow
    workflow.success = false;
    workflow.error = error.message;
    workflow.confNumber = confNumber || `AR-ERROR-${Date.now()}`;
    workflow.message = `I apologize, but there was a technical issue saving your reservation.\n\n📝 Your details:\n📧 Email: ${email}\n📅 Date & Time: ${dateTime}\n👥 Party Size: ${partySize}\n\n📞 Please call us at (555) 123-4567 to confirm manually.\n\n🔢 Reference: ${confNumber || 'AR-ERROR-' + Date.now()}`;
  }
}

/*
🚀 SETUP INSTRUCTIONS:

1. ✅ Make sure your backend server is running:
   - Open terminal in backend folder
   - Run: npm start
   - Server should start on port 8080

2. ✅ Copy this code to Botpress Studio:
   - Go to src/actions/saveReservationToMySQL.js
   - Replace existing code with this code

3. ✅ In your Botpress flow, add Execute Code node with:
   - confNumber: {{workflow.confNumber}}
   - dateTime: {{workflow.dateTime}}
   - email: {{workflow.email}}
   - partySize: {{workflow.partySize}}

4. ✅ Add conditions after Execute Code:
   - Success condition: {{workflow.success}} === true
   - Error condition: {{workflow.success}} === false

5. ✅ Add response messages:
   - Success: {{workflow.message}}
   - Error: {{workflow.message}}

6. ✅ Test your chatbot!

🔧 BENEFITS OF REST API APPROACH:
- ✅ Cleaner separation of concerns
- ✅ Better error handling
- ✅ Easier to debug and maintain
- ✅ More secure database access
- ✅ Scalable architecture
- ✅ Can be used by multiple clients
*/
