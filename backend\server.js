const express = require("express");
const cors = require("cors");
const dotenv = require("dotenv");
const db = require("./db");
const connectMongoDB = require("./config/mongodb");
const reservationRoutes = require("./routes/reservations");
const authRoutes = require("./routes/auth");
const userRoutes = require("./routes/users");
const adminRoutes = require("./routes/admin");
const Reservation = require("./models/reservation");
const BotpressReservation = require('./models/botpressReservation');
const cron = require('node-cron');
const { syncUserData } = require('./utils/syncUsers');
const botpressUtils = require('./utils/botpressClient');
const botpressReservationsRoutes = require('./routes/botpressReservations');

// Load environment variables
dotenv.config();

const app = express();

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Connect to MongoDB
connectMongoDB();

// Test MySQL connection
const testConnection = async () => {
  try {
    const [result] = await db.query('SELECT 1');
    console.log('MySQL connection successful');
  } catch (error) {
    console.error('MySQL connection error:', error);
    process.exit(1);
  }
};

testConnection();

// Initialize database tables
const initDb = async () => {
  try {
    // Create tables if they don't exist
    await Reservation.createTable();
    await BotpressReservation.createTable();
    console.log('Database tables initialized');
  } catch (error) {
    console.error('Error initializing database tables:', error);
    process.exit(1);
  }
};

// Run database initialization
initDb();

// Routes
app.use("/api/auth", authRoutes);
app.use("/api/users", userRoutes);
app.use("/api/reservations", reservationRoutes);
app.use("/api/botpress-reservations", botpressReservationsRoutes);
app.use("/api/admin", adminRoutes);

// Schedule daily sync at midnight
cron.schedule('0 0 * * *', async () => {
  console.log('Running scheduled user data sync');
  await syncUserData();
});

// Botpress Studio webhook endpoint for live chatbot integration
app.post("/api/botpress/webhook", async (req, res) => {
  try {
    console.log('🤖 Received Botpress webhook:', JSON.stringify(req.body, null, 2));

    const { type, payload } = req.body;

    if (type === "create_reservation") {
      const reservationData = {
        userId: 'botpress',
        customerName: payload.customer.name || payload.customerName,
        phone: payload.customer.phone || payload.phone || '',
        email: payload.customer.email || payload.email || '',
        date: payload.reservation.date || payload.date,
        time: payload.reservation.time || payload.time,
        partySize: parseInt(payload.reservation.partySize || payload.partySize),
        special_requests: payload.reservation.special_requests || payload.specialRequests || '',
        status: 'pending'
      };

      console.log('📝 Creating reservation with data:', reservationData);

      // Create main reservation
      const reservationId = await Reservation.create(reservationData);

      // Create Botpress tracking entry
      const botpressReservationData = {
        email: reservationData.email,
        datetime: `${reservationData.date} ${reservationData.time}:00`,
        partySize: reservationData.partySize,
        reservationId: reservationId,
        status: 'pending'
      };

      const botpressReservationId = await BotpressReservation.create(botpressReservationData);

      console.log('✅ Reservation created successfully:', { reservationId, botpressReservationId });

      res.json({
        success: true,
        reservationId: reservationId,
        botpressReservationId: botpressReservationId,
        message: "Reservation created successfully",
        data: reservationData
      });
    } else {
      console.log('❌ Unknown webhook type:', type);
      res.status(400).json({
        success: false,
        message: "Unknown webhook type"
      });
    }
  } catch (error) {
    console.error("❌ Error processing Botpress webhook:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error",
      error: error.message
    });
  }
});

// Error handling middleware
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({
    success: false,
    message: "Something went wrong!"
  });
});

// Start server
const PORT = process.env.PORT || 5000;
app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
});




