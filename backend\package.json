{"name": "backend", "version": "1.0.0", "main": "server.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "nodemon server.js"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@botpress/client": "^1.15.1", "axios": "^1.9.0", "bcrypt": "^5.1.1", "body-parser": "^1.20.3", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "joi": "^17.13.3", "joi-password-complexity": "^5.2.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.15.1", "mysql2": "^3.14.1", "node-cron": "^3.0.2", "nodemon": "^3.1.10"}}