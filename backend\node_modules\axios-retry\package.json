{"name": "axios-retry", "version": "4.5.0", "author": "<PERSON><PERSON><PERSON> <<EMAIL>>", "description": "Axios plugin that intercepts failed requests and retries them whenever posible.", "license": "Apache-2.0", "homepage": "https://github.com/softonic/axios-retry", "files": ["dist"], "scripts": {"lint": "eslint .", "pretest": "npm run lint", "test": "ts-node --project tsconfig-cjs.json node_modules/jasmine/bin/jasmine", "prebuild": "npm run test", "build": "rm -fr dist/* && npm run build:esm && npm run build:cjs", "build:esm": "tsc -p tsconfig.json && echo '{\"type\":\"module\"}' > dist/esm/package.json", "build:cjs": "tsc -p tsconfig-cjs.json && echo '{\"type\":\"commonjs\"}' > dist/cjs/package.json", "prerelease": "npm run build", "release": "npm version -m \"New version: %s\"", "postrelease": "npm run push && npm publish", "push": "git push origin master && git push origin --tags", "prepare": "husky install"}, "lint-staged": {"*.ts": ["eslint --cache --fix", "prettier --write"]}, "dependencies": {"is-retry-allowed": "^2.2.0"}, "peerDependencies": {"axios": "0.x || 1.x"}, "devDependencies": {"@types/axios": "^0.14.0", "@types/node": "^20.9.1", "@types/jasmine": "^5.1.2", "@typescript-eslint/eslint-plugin": "^6.11.0", "@typescript-eslint/parser": "^6.11.0", "axios": "^1.6.2", "eslint": "^8.53.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-jasmine": "^4.1.3", "eslint-plugin-prettier": "^5.0.1", "husky": "^8.0.3", "jasmine": "^5.1.0", "lint-staged": "^15.1.0", "nock": "^13.3.8", "prettier": "^3.1.0", "ts-node": "^10.9.1", "typescript": "^5.2.2"}, "repository": {"type": "git", "url": "git+https://github.com/softonic/axios-retry.git"}, "bugs": {"url": "https://github.com/softonic/axios-retry/issues"}, "types": "dist/cjs/index.d.ts", "main": "dist/cjs/index.js", "module": "dist/esm/index.js", "exports": {".": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/cjs/index.d.ts", "default": "./dist/cjs/index.js"}}, "./package.json": "./package.json"}}