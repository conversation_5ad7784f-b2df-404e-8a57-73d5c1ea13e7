# 🍽️ Abraham Restaurant: Connect Botpress Chatbot to MySQL

## Current Status ✅
- ✅ Botpress chatbot with reservation workflow (confNumber, dateTime, email, partySize)
- ✅ MySQL database with `reservations` and `botpress_reservations` tables
- ✅ Backend API running on port 8080 with `/api/botpress-reservations` endpoint
- ✅ Database credentials: localhost, root, khoivinh2004, abraham_restaurant

## What We Need to Do
Connect your Botpress chatbot to save data directly to your MySQL database in real-time.

---

## STEP 1: Set Up ngrok Tunnel (Required for Testing)

### 1.1 Start Your Backend
```bash
cd backend
npm start
```
✅ Should show: "Server running on port 8080"

### 1.2 Create Public Tunnel
Open a new terminal:
```bash
ngrok http 8080
```

### 1.3 Copy the HTTPS URL
You'll see:
```
Forwarding    https://abc123.ngrok.io -> http://localhost:8080
```
📋 **Copy the https URL** (e.g., `https://abc123.ngrok.io`)

---

## STEP 2: Add Action to Botpress Studio

### 2.1 Open Botpress Studio
Go to: https://studio.botpress.cloud/

### 2.2 Navigate to Code Editor
- Click "Code Editor" in the left sidebar
- Create new file: `src/actions/saveReservationToMySQL.js`

### 2.3 Copy This Exact Code:

```javascript
const saveReservationToMySQL = async ({ confNumber, dateTime, email, partySize, customerName, phone, specialRequests }) => {
  const axios = require('axios');
  
  try {
    console.log('🍽️ Abraham Restaurant - Saving to MySQL');
    console.log('Input:', { confNumber, dateTime, email, partySize });

    // REPLACE THIS URL WITH YOUR NGROK URL FROM STEP 1.3
    const API_URL = 'https://YOUR_NGROK_URL_HERE.ngrok.io/api/botpress-reservations';
    
    if (!email || !dateTime || !partySize) {
      throw new Error('Missing required fields: email, dateTime, partySize');
    }

    // Parse dateTime from Botpress
    const dateTimeObj = new Date(dateTime);
    const parsedDate = dateTimeObj.toISOString().split('T')[0];
    const parsedTime = dateTimeObj.toTimeString().split(' ')[0].substring(0, 5);

    const reservationData = {
      customerName: customerName || email.split('@')[0],
      email: email,
      phone: phone || '',
      date: parsedDate,
      time: parsedTime,
      datetime: dateTime,
      partySize: parseInt(partySize),
      specialRequests: specialRequests || ''
    };

    console.log('📤 Sending to MySQL:', reservationData);

    const response = await axios.post(API_URL, reservationData, {
      headers: { 'Content-Type': 'application/json' },
      timeout: 15000
    });

    console.log('✅ MySQL Success:', response.data);

    return {
      success: true,
      reservationId: response.data.reservationId,
      confNumber: confNumber,
      message: `🎉 Reservation confirmed!\n📅 ${parsedDate} at ${parsedTime}\n👥 ${partySize} people\n📧 ${email}\n🔢 Confirmation: ${confNumber}`
    };

  } catch (error) {
    console.error('❌ MySQL Error:', error.message);
    return {
      success: false,
      message: `Sorry, technical issue saving reservation. Please call (*************.\nReference: ${confNumber}`
    };
  }
};

module.exports = { saveReservationToMySQL };
```

### 2.4 Update the API URL
Replace `https://YOUR_NGROK_URL_HERE.ngrok.io` with your actual ngrok URL from Step 1.3

### 2.5 Save the File

---

## STEP 3: Update Your Botpress Flow

### 3.1 Open Your Reservation Flow
- Go to "Flows" in Botpress Studio
- Open your existing reservation flow

### 3.2 Add Execute Code Node
After collecting all data (confNumber, dateTime, email, partySize), add:

**Node Type**: Execute Code
**Action**: `saveReservationToMySQL`
**Parameters**:
```
confNumber: {{event.state.confNumber}}
dateTime: {{event.state.dateTime}}
email: {{event.state.email}}
partySize: {{event.state.partySize}}
customerName: {{event.state.customerName}}
phone: {{event.state.phone}}
specialRequests: {{event.state.specialRequests}}
```

### 3.3 Add Router Node
After Execute Code:
- **Success condition**: `{{temp.actionResult.success}} === true`
- **Error condition**: `{{temp.actionResult.success}} === false`

### 3.4 Add Response Messages

**Success Response**:
```
{{temp.actionResult.message}}

Thank you for choosing Abraham Restaurant! 
We'll send a confirmation email shortly.
```

**Error Response**:
```
{{temp.actionResult.message}}
```

---

## STEP 4: Test the Integration

### 4.1 Publish Your Bot
Click "Publish" in Botpress Studio

### 4.2 Test on Your Website
1. Go to http://localhost:3001
2. Open the chatbot
3. Complete a reservation with:
   - Email address
   - Date and time
   - Party size

### 4.3 Verify in MySQL Workbench
Check for new entries in:
- `reservations` table
- `botpress_reservations` table

### 4.4 Check Backend Logs
You should see:
```
🤖 Received Botpress reservation request: {...}
✅ Created main reservation with ID: X
✅ Created Botpress reservation with ID: Y
```

---

## STEP 5: Troubleshooting

### ❌ "Network Error"
- Verify ngrok is running
- Check ngrok URL is correct in action code
- Ensure backend is on port 8080

### ❌ "Missing required fields"
- Check variable names in your flow match exactly:
  - `confNumber` (not `confirmationNumber`)
  - `dateTime` (not `date` and `time` separately)
  - `email`
  - `partySize`

### ❌ No data in MySQL
- Check backend terminal for API calls
- Verify database connection
- Check Botpress Studio logs for errors

---

## SUCCESS INDICATORS ✅

✅ **Botpress Studio Logs**: No errors, action executes successfully
✅ **Backend Logs**: Shows API calls from Botpress
✅ **MySQL Database**: New entries in both tables
✅ **Chatbot Response**: Shows confirmation message

---

## Next Steps (Production)

Once working with ngrok:
1. Deploy backend to cloud service (Heroku, Railway, etc.)
2. Update API URL in Botpress action to production URL
3. Remove ngrok dependency

Your chatbot will then save reservations directly to MySQL in real-time! 🚀
