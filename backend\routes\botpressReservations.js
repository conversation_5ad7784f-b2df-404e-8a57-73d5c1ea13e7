const express = require('express');
const router = express.Router();
const BotpressReservation = require('../models/botpressReservation');
const Reservation = require('../models/reservation');
const auth = require('../middleware/auth');
const admin = require('../middleware/admin');

// Get all Botpress reservations (admin only)
router.get('/', [auth, admin], async (req, res) => {
  try {
    const reservations = await BotpressReservation.getAll();
    res.json(reservations);
  } catch (error) {
    console.error('Error fetching Botpress reservations:', error);
    res.status(500).json({ message: 'Error fetching reservations' });
  }
});

// Create a new Botpress reservation
router.post('/', async (req, res) => {
  try {
    console.log('🤖 Received Botpress reservation request:', JSON.stringify(req.body, null, 2));
    console.log('🔍 Starting reservation creation process...');

    // Handle different payload formats from Botpress Studio
    const customerName = req.body.customerName || req.body.email?.split('@')[0] || 'Botpress User';
    const email = req.body.email || '';
    const phone = req.body.phone || '';
    const specialRequests = req.body.specialRequests || req.body.special_requests || '';

    // Handle datetime - could be separate date/time or combined datetime
    let date, time;
    if (req.body.datetime) {
      const dateTime = new Date(req.body.datetime);
      date = dateTime.toISOString().split('T')[0];
      time = dateTime.toTimeString().split(' ')[0].substring(0, 5);
    } else {
      date = req.body.date;
      time = req.body.time;
    }

    // Validate required fields
    if (!email || !date || !time || !req.body.partySize) {
      return res.status(400).json({
        success: false,
        message: 'Missing required fields: email, date, time, and partySize are required',
        received: req.body
      });
    }

    // First create the main reservation
    const reservationData = {
      userId: 'botpress',
      customerName: customerName,
      email: email,
      phone: phone,
      date: date,
      time: time,
      partySize: parseInt(req.body.partySize),
      special_requests: specialRequests,
      status: 'pending'
    };

    console.log('📝 Prepared reservation data:', reservationData);
    console.log('🔄 Creating main reservation...');

    const reservationId = await Reservation.create(reservationData);
    console.log('✅ Created main reservation with ID:', reservationId);

    // Then create the Botpress-specific reservation
    const botpressReservationData = {
      email: email,
      datetime: req.body.datetime || `${date}T${time}:00`,
      partySize: parseInt(req.body.partySize),
      reservationId: reservationId,
      status: 'pending'
    };

    console.log('📝 Prepared Botpress reservation data:', botpressReservationData);
    console.log('🔄 Creating Botpress reservation...');

    const botpressReservationId = await BotpressReservation.create(botpressReservationData);
    console.log('✅ Created Botpress reservation with ID:', botpressReservationId);

    res.status(201).json({
      success: true,
      id: botpressReservationId,
      reservationId: reservationId,
      message: 'Botpress reservation created successfully',
      data: {
        customerName,
        email,
        phone,
        date,
        time,
        partySize: parseInt(req.body.partySize),
        specialRequests
      }
    });
  } catch (error) {
    console.error('❌ Error creating Botpress reservation:', error);
    console.error('Error details:', error.message);
    console.error('Error stack:', error.stack);
    res.status(500).json({
      success: false,
      message: 'Error creating reservation',
      error: error.message
    });
  }
});

// Update a Botpress reservation status
router.patch('/:id/status', [auth, admin], async (req, res) => {
  try {
    const { status } = req.body;
    const id = req.params.id;
    
    const updated = await BotpressReservation.update(id, { status });
    
    if (updated) {
      res.json({ message: 'Reservation status updated successfully' });
    } else {
      res.status(404).json({ message: 'Reservation not found' });
    }
  } catch (error) {
    console.error('Error updating Botpress reservation status:', error);
    res.status(500).json({ message: 'Error updating reservation status' });
  }
});

// Delete a Botpress reservation
router.delete('/:id', [auth, admin], async (req, res) => {
  try {
    const deleted = await BotpressReservation.delete(req.params.id);
    
    if (deleted) {
      res.json({ message: 'Botpress reservation deleted successfully' });
    } else {
      res.status(404).json({ message: 'Reservation not found' });
    }
  } catch (error) {
    console.error('Error deleting Botpress reservation:', error);
    res.status(500).json({ message: 'Error deleting reservation' });
  }
});

module.exports = router;