const express = require('express');
const router = express.Router();
const BotpressReservation = require('../models/botpressReservation');
const Reservation = require('../models/reservation');
const auth = require('../middleware/auth');
const admin = require('../middleware/admin');

// Get all Botpress reservations (admin only)
router.get('/', [auth, admin], async (req, res) => {
  try {
    const reservations = await BotpressReservation.getAll();
    res.json(reservations);
  } catch (error) {
    console.error('Error fetching Botpress reservations:', error);
    res.status(500).json({ message: 'Error fetching reservations' });
  }
});

// Create a new Botpress reservation
router.post('/', async (req, res) => {
  try {
    // First create the main reservation
    const reservationData = {
      userId: 'botpress',
      customerName: req.body.email.split('@')[0], // Use email username as name
      email: req.body.email,
      date: new Date(req.body.datetime).toISOString().split('T')[0],
      time: new Date(req.body.datetime).toTimeString().split(' ')[0].substring(0, 5),
      partySize: req.body.partySize,
      special_requests: req.body.specialRequests || '',
      status: 'pending'
    };

    const reservationId = await Reservation.create(reservationData);
    
    // Then create the Botpress-specific reservation
    const botpressReservationData = {
      email: req.body.email,
      datetime: req.body.datetime,
      partySize: req.body.partySize,
      reservationId: reservationId,
      status: 'pending'
    };
    
    const botpressReservationId = await BotpressReservation.create(botpressReservationData);
    
    res.status(201).json({
      id: botpressReservationId,
      reservationId: reservationId,
      message: 'Botpress reservation created successfully'
    });
  } catch (error) {
    console.error('Error creating Botpress reservation:', error);
    res.status(500).json({ message: 'Error creating reservation' });
  }
});

// Update a Botpress reservation status
router.patch('/:id/status', [auth, admin], async (req, res) => {
  try {
    const { status } = req.body;
    const id = req.params.id;
    
    const updated = await BotpressReservation.update(id, { status });
    
    if (updated) {
      res.json({ message: 'Reservation status updated successfully' });
    } else {
      res.status(404).json({ message: 'Reservation not found' });
    }
  } catch (error) {
    console.error('Error updating Botpress reservation status:', error);
    res.status(500).json({ message: 'Error updating reservation status' });
  }
});

// Delete a Botpress reservation
router.delete('/:id', [auth, admin], async (req, res) => {
  try {
    const deleted = await BotpressReservation.delete(req.params.id);
    
    if (deleted) {
      res.json({ message: 'Botpress reservation deleted successfully' });
    } else {
      res.status(404).json({ message: 'Reservation not found' });
    }
  } catch (error) {
    console.error('Error deleting Botpress reservation:', error);
    res.status(500).json({ message: 'Error deleting reservation' });
  }
});

module.exports = router;