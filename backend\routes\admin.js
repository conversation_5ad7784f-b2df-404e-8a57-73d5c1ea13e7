const express = require('express');
const router = express.Router();
const auth = require('../middleware/auth');
const admin = require('../middleware/admin');
const { syncUserData } = require('../utils/syncUsers');
const botpressUtils = require('../utils/botpressClient');

// Admin-only endpoint to sync user data
router.post('/sync-users', [auth, admin], async (req, res) => {
  try {
    const result = await syncUserData();
    if (result.success) {
      res.status(200).json({ message: result.message });
    } else {
      res.status(500).json({ message: 'Sync failed', error: result.error });
    }
  } catch (error) {
    console.error('Error in sync endpoint:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
});

// Sync reservations from MySQL to Botpress
router.post('/sync-to-botpress', [auth, admin], async (req, res) => {
  try {
    // Get all reservations from MySQL
    const [reservations] = await db.query('SELECT * FROM reservations WHERE email IS NOT NULL');
    
    // Sync to Botpress
    const result = await botpressUtils.syncReservationsToBot(reservations);
    
    if (result.success) {
      res.status(200).json({ 
        message: `Successfully synced ${result.synced} reservations to Botpress` 
      });
    } else {
      res.status(500).json({ 
        message: 'Sync failed', 
        error: result.error 
      });
    }
  } catch (error) {
    console.error('Error in sync to Botpress endpoint:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
});

// Sync reservations from Botpress to MySQL
router.post('/sync-from-botpress', [auth, admin], async (req, res) => {
  try {
    const result = await botpressUtils.syncReservationsFromBot();
    
    if (result.success) {
      res.status(200).json({ 
        message: `Successfully synced ${result.synced} reservations from Botpress` 
      });
    } else {
      res.status(500).json({ 
        message: 'Sync failed', 
        error: result.error 
      });
    }
  } catch (error) {
    console.error('Error in sync from Botpress endpoint:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
});

module.exports = router;

