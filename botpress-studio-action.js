/**
 * Restaurant Reservation Action for Botpress Studio
 * 
 * Instructions:
 * 1. Copy this entire code
 * 2. In Botpress Studio, go to Code Editor
 * 3. Create new file: src/actions/createReservation.js
 * 4. Paste this code and save
 * 
 * @title Create Restaurant Reservation
 * @category Restaurant
 * <AUTHOR> Restaurant
 * @param {string} customerName - Customer's full name
 * @param {string} email - Customer's email address  
 * @param {string} phone - Customer's phone number
 * @param {string} date - Reservation date (YYYY-MM-DD)
 * @param {string} time - Reservation time (HH:MM)
 * @param {number} partySize - Number of people
 * @param {string} specialRequests - Special requests or notes
 */

const createReservation = async ({ customerName, email, phone, date, time, partySize, specialRequests }) => {
  const axios = require('axios');
  
  try {
    // Log the input data for debugging
    console.log('🍽️ Creating reservation for Abraham Restaurant');
    console.log('Input data:', { customerName, email, phone, date, time, partySize, specialRequests });

    // Your backend API endpoint - UPDATE THIS FOR PRODUCTION
    const API_URL = 'http://localhost:8080/api/botpress-reservations';
    
    // Prepare the reservation data
    const reservationData = {
      customerName: customerName || 'Guest',
      email: email,
      phone: phone || '',
      date: date,
      time: time,
      partySize: parseInt(partySize),
      specialRequests: specialRequests || ''
    };

    console.log('📤 Sending reservation data:', reservationData);

    // Make the API call to your backend
    const response = await axios.post(API_URL, reservationData, {
      headers: {
        'Content-Type': 'application/json'
      },
      timeout: 10000 // 10 second timeout
    });

    console.log('✅ Reservation created successfully!');
    console.log('Response:', response.data);

    // Return success response
    return {
      success: true,
      reservationId: response.data.reservationId,
      botpressReservationId: response.data.id,
      message: `Perfect! Your reservation has been confirmed for ${date} at ${time} for ${partySize} people.`,
      confirmationDetails: {
        customerName: customerName,
        email: email,
        phone: phone,
        date: date,
        time: time,
        partySize: partySize,
        specialRequests: specialRequests
      }
    };

  } catch (error) {
    console.error('❌ Error creating reservation:', error);
    
    // Return error response
    return {
      success: false,
      error: error.message,
      message: 'I apologize, but there was an issue creating your reservation. Please try again or contact us directly at (*************.'
    };
  }
};

// Export the function
module.exports = { createReservation };

/*
USAGE IN BOTPRESS STUDIO FLOW:

1. Create these variables in your flow:
   - customerName (text)
   - email (text) 
   - phone (text)
   - date (text)
   - time (text)
   - partySize (number)
   - specialRequests (text)

2. Add an "Execute Code" node with:
   - Action: createReservation
   - Parameters:
     * customerName: {{event.state.customerName}}
     * email: {{event.state.email}}
     * phone: {{event.state.phone}}
     * date: {{event.state.date}}
     * time: {{event.state.time}}
     * partySize: {{event.state.partySize}}
     * specialRequests: {{event.state.specialRequests}}

3. Add a Router node after the action with conditions:
   - Success: {{temp.actionResult.success}} === true
   - Error: {{temp.actionResult.success}} === false

4. Success message example:
   "{{temp.actionResult.message}}
   
   📧 Confirmation will be sent to: {{temp.actionResult.confirmationDetails.email}}
   📞 Contact number: {{temp.actionResult.confirmationDetails.phone}}
   
   We look forward to serving you at Abraham Restaurant!"

5. Error message example:
   "{{temp.actionResult.message}}"

SAMPLE CONVERSATION FLOW:
Bot: "Welcome to Abraham Restaurant! I'd be happy to help you make a reservation."
Bot: "What's your full name?"
User: "John Doe"
Bot: "What's your email address?"
User: "<EMAIL>"
Bot: "What's your phone number?"
User: "555-0123"
Bot: "What date would you like? (YYYY-MM-DD format)"
User: "2024-12-25"
Bot: "What time would you prefer? (HH:MM format)"
User: "19:30"
Bot: "How many people will be dining?"
User: "4"
Bot: "Any special requests?"
User: "Anniversary dinner"
Bot: [Execute createReservation action]
Bot: "Perfect! Your reservation has been confirmed for 2024-12-25 at 19:30 for 4 people..."
*/
