var xf=Object.create;var yt=Object.defineProperty;var If=Object.getOwnPropertyDescriptor;var kf=Object.getOwnPropertyNames;var Pf=Object.getPrototypeOf,vf=Object.prototype.hasOwnProperty;var R=(e,t)=>()=>(t||e((t={exports:{}}).exports,t),t.exports),z=(e,t)=>{for(var r in t)yt(e,r,{get:t[r],enumerable:!0})},Af=(e,t,r,a)=>{if(t&&typeof t=="object"||typeof t=="function")for(let n of kf(t))!vf.call(e,n)&&n!==r&&yt(e,n,{get:()=>t[n],enumerable:!(a=If(t,n))||a.enumerable});return e};var te=(e,t,r)=>(r=e!=null?xf(Pf(e)):{},Af(t||!e||!e.__esModule?yt(r,"default",{value:e,enumerable:!0}):r,e));var qa=R((cP,ma)=>{"use strict";var Tf=new Set(["ENOTFOUND","ENETUNREACH","UNABLE_TO_GET_ISSUER_CERT","UNABLE_TO_GET_CRL","UNABLE_TO_DECRYPT_CERT_SIGNATURE","UNABLE_TO_DECRYPT_CRL_SIGNATURE","UNABLE_TO_DECODE_ISSUER_PUBLIC_KEY","CERT_SIGNATURE_FAILURE","CRL_SIGNATURE_FAILURE","CERT_NOT_YET_VALID","CERT_HAS_EXPIRED","CRL_NOT_YET_VALID","CRL_HAS_EXPIRED","ERROR_IN_CERT_NOT_BEFORE_FIELD","ERROR_IN_CERT_NOT_AFTER_FIELD","ERROR_IN_CRL_LAST_UPDATE_FIELD","ERROR_IN_CRL_NEXT_UPDATE_FIELD","OUT_OF_MEM","DEPTH_ZERO_SELF_SIGNED_CERT","SELF_SIGNED_CERT_IN_CHAIN","UNABLE_TO_GET_ISSUER_CERT_LOCALLY","UNABLE_TO_VERIFY_LEAF_SIGNATURE","CERT_CHAIN_TOO_LONG","CERT_REVOKED","INVALID_CA","PATH_LENGTH_EXCEEDED","INVALID_PURPOSE","CERT_UNTRUSTED","CERT_REJECTED","HOSTNAME_MISMATCH"]);ma.exports=e=>!Tf.has(e&&e.code)});var Aa=R(()=>{});var Ta=R(()=>{});var ys=R((vP,Da)=>{"use strict";Da.exports=Object});var Fa=R((AP,_a)=>{"use strict";_a.exports=Error});var Oa=R((TP,Ma)=>{"use strict";Ma.exports=EvalError});var Na=R((BP,Va)=>{"use strict";Va.exports=RangeError});var $a=R((CP,Ka)=>{"use strict";Ka.exports=ReferenceError});var Rs=R((wP,ja)=>{"use strict";ja.exports=SyntaxError});var be=R((UP,za)=>{"use strict";za.exports=TypeError});var Ya=R((GP,Ja)=>{"use strict";Ja.exports=URIError});var Xa=R((EP,Za)=>{"use strict";Za.exports=Math.abs});var tn=R((LP,en)=>{"use strict";en.exports=Math.floor});var rn=R((WP,sn)=>{"use strict";sn.exports=Math.max});var nn=R((SP,an)=>{"use strict";an.exports=Math.min});var pn=R((QP,on)=>{"use strict";on.exports=Math.pow});var cn=R((HP,un)=>{"use strict";un.exports=Math.round});var ln=R((DP,dn)=>{"use strict";dn.exports=Number.isNaN||function(t){return t!==t}});var yn=R((_P,gn)=>{"use strict";var ix=ln();gn.exports=function(t){return ix(t)||t===0?t:t<0?-1:1}});var mn=R((FP,Rn)=>{"use strict";Rn.exports=Object.getOwnPropertyDescriptor});var We=R((MP,qn)=>{"use strict";var je=mn();if(je)try{je([],"length")}catch{je=null}qn.exports=je});var Se=R((OP,hn)=>{"use strict";var ze=Object.defineProperty||!1;if(ze)try{ze({},"a",{value:1})}catch{ze=!1}hn.exports=ze});var fn=R((VP,bn)=>{"use strict";bn.exports=function(){if(typeof Symbol!="function"||typeof Object.getOwnPropertySymbols!="function")return!1;if(typeof Symbol.iterator=="symbol")return!0;var t={},r=Symbol("test"),a=Object(r);if(typeof r=="string"||Object.prototype.toString.call(r)!=="[object Symbol]"||Object.prototype.toString.call(a)!=="[object Symbol]")return!1;var n=42;t[r]=n;for(var o in t)return!1;if(typeof Object.keys=="function"&&Object.keys(t).length!==0||typeof Object.getOwnPropertyNames=="function"&&Object.getOwnPropertyNames(t).length!==0)return!1;var i=Object.getOwnPropertySymbols(t);if(i.length!==1||i[0]!==r||!Object.prototype.propertyIsEnumerable.call(t,r))return!1;if(typeof Object.getOwnPropertyDescriptor=="function"){var u=Object.getOwnPropertyDescriptor(t,r);if(u.value!==n||u.enumerable!==!0)return!1}return!0}});var kn=R((NP,In)=>{"use strict";var xn=typeof Symbol<"u"&&Symbol,px=fn();In.exports=function(){return typeof xn!="function"||typeof Symbol!="function"||typeof xn("foo")!="symbol"||typeof Symbol("bar")!="symbol"?!1:px()}});var ms=R((KP,Pn)=>{"use strict";Pn.exports=typeof Reflect<"u"&&Reflect.getPrototypeOf||null});var qs=R(($P,vn)=>{"use strict";var ux=ys();vn.exports=ux.getPrototypeOf||null});var Bn=R((jP,Tn)=>{"use strict";var cx="Function.prototype.bind called on incompatible ",dx=Object.prototype.toString,lx=Math.max,gx="[object Function]",An=function(t,r){for(var a=[],n=0;n<t.length;n+=1)a[n]=t[n];for(var o=0;o<r.length;o+=1)a[o+t.length]=r[o];return a},yx=function(t,r){for(var a=[],n=r||0,o=0;n<t.length;n+=1,o+=1)a[o]=t[n];return a},Rx=function(e,t){for(var r="",a=0;a<e.length;a+=1)r+=e[a],a+1<e.length&&(r+=t);return r};Tn.exports=function(t){var r=this;if(typeof r!="function"||dx.apply(r)!==gx)throw new TypeError(cx+r);for(var a=yx(arguments,1),n,o=function(){if(this instanceof n){var y=r.apply(this,An(a,arguments));return Object(y)===y?y:this}return r.apply(t,An(a,arguments))},i=lx(0,r.length-a.length),u=[],p=0;p<i;p++)u[p]="$"+p;if(n=Function("binder","return function ("+Rx(u,",")+"){ return binder.apply(this,arguments); }")(o),r.prototype){var s=function(){};s.prototype=r.prototype,n.prototype=new s,s.prototype=null}return n}});var fe=R((zP,Cn)=>{"use strict";var mx=Bn();Cn.exports=Function.prototype.bind||mx});var Je=R((JP,wn)=>{"use strict";wn.exports=Function.prototype.call});var hs=R((YP,Un)=>{"use strict";Un.exports=Function.prototype.apply});var En=R((ZP,Gn)=>{"use strict";Gn.exports=typeof Reflect<"u"&&Reflect&&Reflect.apply});var Wn=R((XP,Ln)=>{"use strict";var qx=fe(),hx=hs(),bx=Je(),fx=En();Ln.exports=fx||qx.call(bx,hx)});var Qn=R((ev,Sn)=>{"use strict";var xx=fe(),Ix=be(),kx=Je(),Px=Wn();Sn.exports=function(t){if(t.length<1||typeof t[0]!="function")throw new Ix("a function is required");return Px(xx,kx,t)}});var On=R((tv,Mn)=>{"use strict";var vx=Qn(),Hn=We(),_n;try{_n=[].__proto__===Array.prototype}catch(e){if(!e||typeof e!="object"||!("code"in e)||e.code!=="ERR_PROTO_ACCESS")throw e}var bs=!!_n&&Hn&&Hn(Object.prototype,"__proto__"),Fn=Object,Dn=Fn.getPrototypeOf;Mn.exports=bs&&typeof bs.get=="function"?vx([bs.get]):typeof Dn=="function"?function(t){return Dn(t==null?t:Fn(t))}:!1});var jn=R((sv,$n)=>{"use strict";var Vn=ms(),Nn=qs(),Kn=On();$n.exports=Vn?function(t){return Vn(t)}:Nn?function(t){if(!t||typeof t!="object"&&typeof t!="function")throw new TypeError("getProto: not an object");return Nn(t)}:Kn?function(t){return Kn(t)}:null});var Jn=R((rv,zn)=>{"use strict";var Ax=Function.prototype.call,Tx=Object.prototype.hasOwnProperty,Bx=fe();zn.exports=Bx.call(Ax,Tx)});var _e=R((av,so)=>{"use strict";var f,Cx=ys(),wx=Fa(),Ux=Oa(),Gx=Na(),Ex=$a(),Pe=Rs(),ke=be(),Lx=Ya(),Wx=Xa(),Sx=tn(),Qx=rn(),Hx=nn(),Dx=pn(),_x=cn(),Fx=yn(),eo=Function,fs=function(e){try{return eo('"use strict"; return ('+e+").constructor;")()}catch{}},Qe=We(),Mx=Se(),xs=function(){throw new ke},Ox=Qe?function(){try{return arguments.callee,xs}catch{try{return Qe(arguments,"callee").get}catch{return xs}}}():xs,xe=kn()(),G=jn(),Vx=qs(),Nx=ms(),to=hs(),He=Je(),Ie={},Kx=typeof Uint8Array>"u"||!G?f:G(Uint8Array),ce={__proto__:null,"%AggregateError%":typeof AggregateError>"u"?f:AggregateError,"%Array%":Array,"%ArrayBuffer%":typeof ArrayBuffer>"u"?f:ArrayBuffer,"%ArrayIteratorPrototype%":xe&&G?G([][Symbol.iterator]()):f,"%AsyncFromSyncIteratorPrototype%":f,"%AsyncFunction%":Ie,"%AsyncGenerator%":Ie,"%AsyncGeneratorFunction%":Ie,"%AsyncIteratorPrototype%":Ie,"%Atomics%":typeof Atomics>"u"?f:Atomics,"%BigInt%":typeof BigInt>"u"?f:BigInt,"%BigInt64Array%":typeof BigInt64Array>"u"?f:BigInt64Array,"%BigUint64Array%":typeof BigUint64Array>"u"?f:BigUint64Array,"%Boolean%":Boolean,"%DataView%":typeof DataView>"u"?f:DataView,"%Date%":Date,"%decodeURI%":decodeURI,"%decodeURIComponent%":decodeURIComponent,"%encodeURI%":encodeURI,"%encodeURIComponent%":encodeURIComponent,"%Error%":wx,"%eval%":eval,"%EvalError%":Ux,"%Float32Array%":typeof Float32Array>"u"?f:Float32Array,"%Float64Array%":typeof Float64Array>"u"?f:Float64Array,"%FinalizationRegistry%":typeof FinalizationRegistry>"u"?f:FinalizationRegistry,"%Function%":eo,"%GeneratorFunction%":Ie,"%Int8Array%":typeof Int8Array>"u"?f:Int8Array,"%Int16Array%":typeof Int16Array>"u"?f:Int16Array,"%Int32Array%":typeof Int32Array>"u"?f:Int32Array,"%isFinite%":isFinite,"%isNaN%":isNaN,"%IteratorPrototype%":xe&&G?G(G([][Symbol.iterator]())):f,"%JSON%":typeof JSON=="object"?JSON:f,"%Map%":typeof Map>"u"?f:Map,"%MapIteratorPrototype%":typeof Map>"u"||!xe||!G?f:G(new Map()[Symbol.iterator]()),"%Math%":Math,"%Number%":Number,"%Object%":Cx,"%Object.getOwnPropertyDescriptor%":Qe,"%parseFloat%":parseFloat,"%parseInt%":parseInt,"%Promise%":typeof Promise>"u"?f:Promise,"%Proxy%":typeof Proxy>"u"?f:Proxy,"%RangeError%":Gx,"%ReferenceError%":Ex,"%Reflect%":typeof Reflect>"u"?f:Reflect,"%RegExp%":RegExp,"%Set%":typeof Set>"u"?f:Set,"%SetIteratorPrototype%":typeof Set>"u"||!xe||!G?f:G(new Set()[Symbol.iterator]()),"%SharedArrayBuffer%":typeof SharedArrayBuffer>"u"?f:SharedArrayBuffer,"%String%":String,"%StringIteratorPrototype%":xe&&G?G(""[Symbol.iterator]()):f,"%Symbol%":xe?Symbol:f,"%SyntaxError%":Pe,"%ThrowTypeError%":Ox,"%TypedArray%":Kx,"%TypeError%":ke,"%Uint8Array%":typeof Uint8Array>"u"?f:Uint8Array,"%Uint8ClampedArray%":typeof Uint8ClampedArray>"u"?f:Uint8ClampedArray,"%Uint16Array%":typeof Uint16Array>"u"?f:Uint16Array,"%Uint32Array%":typeof Uint32Array>"u"?f:Uint32Array,"%URIError%":Lx,"%WeakMap%":typeof WeakMap>"u"?f:WeakMap,"%WeakRef%":typeof WeakRef>"u"?f:WeakRef,"%WeakSet%":typeof WeakSet>"u"?f:WeakSet,"%Function.prototype.call%":He,"%Function.prototype.apply%":to,"%Object.defineProperty%":Mx,"%Object.getPrototypeOf%":Vx,"%Math.abs%":Wx,"%Math.floor%":Sx,"%Math.max%":Qx,"%Math.min%":Hx,"%Math.pow%":Dx,"%Math.round%":_x,"%Math.sign%":Fx,"%Reflect.getPrototypeOf%":Nx};if(G)try{null.error}catch(e){Yn=G(G(e)),ce["%Error.prototype%"]=Yn}var Yn,$x=function e(t){var r;if(t==="%AsyncFunction%")r=fs("async function () {}");else if(t==="%GeneratorFunction%")r=fs("function* () {}");else if(t==="%AsyncGeneratorFunction%")r=fs("async function* () {}");else if(t==="%AsyncGenerator%"){var a=e("%AsyncGeneratorFunction%");a&&(r=a.prototype)}else if(t==="%AsyncIteratorPrototype%"){var n=e("%AsyncGenerator%");n&&G&&(r=G(n.prototype))}return ce[t]=r,r},Zn={__proto__:null,"%ArrayBufferPrototype%":["ArrayBuffer","prototype"],"%ArrayPrototype%":["Array","prototype"],"%ArrayProto_entries%":["Array","prototype","entries"],"%ArrayProto_forEach%":["Array","prototype","forEach"],"%ArrayProto_keys%":["Array","prototype","keys"],"%ArrayProto_values%":["Array","prototype","values"],"%AsyncFunctionPrototype%":["AsyncFunction","prototype"],"%AsyncGenerator%":["AsyncGeneratorFunction","prototype"],"%AsyncGeneratorPrototype%":["AsyncGeneratorFunction","prototype","prototype"],"%BooleanPrototype%":["Boolean","prototype"],"%DataViewPrototype%":["DataView","prototype"],"%DatePrototype%":["Date","prototype"],"%ErrorPrototype%":["Error","prototype"],"%EvalErrorPrototype%":["EvalError","prototype"],"%Float32ArrayPrototype%":["Float32Array","prototype"],"%Float64ArrayPrototype%":["Float64Array","prototype"],"%FunctionPrototype%":["Function","prototype"],"%Generator%":["GeneratorFunction","prototype"],"%GeneratorPrototype%":["GeneratorFunction","prototype","prototype"],"%Int8ArrayPrototype%":["Int8Array","prototype"],"%Int16ArrayPrototype%":["Int16Array","prototype"],"%Int32ArrayPrototype%":["Int32Array","prototype"],"%JSONParse%":["JSON","parse"],"%JSONStringify%":["JSON","stringify"],"%MapPrototype%":["Map","prototype"],"%NumberPrototype%":["Number","prototype"],"%ObjectPrototype%":["Object","prototype"],"%ObjProto_toString%":["Object","prototype","toString"],"%ObjProto_valueOf%":["Object","prototype","valueOf"],"%PromisePrototype%":["Promise","prototype"],"%PromiseProto_then%":["Promise","prototype","then"],"%Promise_all%":["Promise","all"],"%Promise_reject%":["Promise","reject"],"%Promise_resolve%":["Promise","resolve"],"%RangeErrorPrototype%":["RangeError","prototype"],"%ReferenceErrorPrototype%":["ReferenceError","prototype"],"%RegExpPrototype%":["RegExp","prototype"],"%SetPrototype%":["Set","prototype"],"%SharedArrayBufferPrototype%":["SharedArrayBuffer","prototype"],"%StringPrototype%":["String","prototype"],"%SymbolPrototype%":["Symbol","prototype"],"%SyntaxErrorPrototype%":["SyntaxError","prototype"],"%TypedArrayPrototype%":["TypedArray","prototype"],"%TypeErrorPrototype%":["TypeError","prototype"],"%Uint8ArrayPrototype%":["Uint8Array","prototype"],"%Uint8ClampedArrayPrototype%":["Uint8ClampedArray","prototype"],"%Uint16ArrayPrototype%":["Uint16Array","prototype"],"%Uint32ArrayPrototype%":["Uint32Array","prototype"],"%URIErrorPrototype%":["URIError","prototype"],"%WeakMapPrototype%":["WeakMap","prototype"],"%WeakSetPrototype%":["WeakSet","prototype"]},De=fe(),Ye=Jn(),jx=De.call(He,Array.prototype.concat),zx=De.call(to,Array.prototype.splice),Xn=De.call(He,String.prototype.replace),Ze=De.call(He,String.prototype.slice),Jx=De.call(He,RegExp.prototype.exec),Yx=/[^%.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|%$))/g,Zx=/\\(\\)?/g,Xx=function(t){var r=Ze(t,0,1),a=Ze(t,-1);if(r==="%"&&a!=="%")throw new Pe("invalid intrinsic syntax, expected closing `%`");if(a==="%"&&r!=="%")throw new Pe("invalid intrinsic syntax, expected opening `%`");var n=[];return Xn(t,Yx,function(o,i,u,p){n[n.length]=u?Xn(p,Zx,"$1"):i||o}),n},eI=function(t,r){var a=t,n;if(Ye(Zn,a)&&(n=Zn[a],a="%"+n[0]+"%"),Ye(ce,a)){var o=ce[a];if(o===Ie&&(o=$x(a)),typeof o>"u"&&!r)throw new ke("intrinsic "+t+" exists, but is not available. Please file an issue!");return{alias:n,name:a,value:o}}throw new Pe("intrinsic "+t+" does not exist!")};so.exports=function(t,r){if(typeof t!="string"||t.length===0)throw new ke("intrinsic name must be a non-empty string");if(arguments.length>1&&typeof r!="boolean")throw new ke('"allowMissing" argument must be a boolean');if(Jx(/^%?[^%]*%?$/,t)===null)throw new Pe("`%` may not be present anywhere but at the beginning and end of the intrinsic name");var a=Xx(t),n=a.length>0?a[0]:"",o=eI("%"+n+"%",r),i=o.name,u=o.value,p=!1,s=o.alias;s&&(n=s[0],zx(a,jx([0,1],s)));for(var y=1,P=!0;y<a.length;y+=1){var b=a[y],k=Ze(b,0,1),I=Ze(b,-1);if((k==='"'||k==="'"||k==="`"||I==='"'||I==="'"||I==="`")&&k!==I)throw new Pe("property names with quotes must have matching quotes");if((b==="constructor"||!P)&&(p=!0),n+="."+b,i="%"+n+"%",Ye(ce,i))u=ce[i];else if(u!=null){if(!(b in u)){if(!r)throw new ke("base intrinsic for "+t+" exists, but the property is not available.");return}if(Qe&&y+1>=a.length){var U=Qe(u,b);P=!!U,P&&"get"in U&&!("originalValue"in U.get)?u=U.get:u=u[b]}else P=Ye(u,b),u=u[b];P&&!p&&(ce[i]=u)}}return u}});var oo=R((nv,no)=>{"use strict";var ro=Se(),tI=Rs(),ve=be(),ao=We();no.exports=function(t,r,a){if(!t||typeof t!="object"&&typeof t!="function")throw new ve("`obj` must be an object or a function`");if(typeof r!="string"&&typeof r!="symbol")throw new ve("`property` must be a string or a symbol`");if(arguments.length>3&&typeof arguments[3]!="boolean"&&arguments[3]!==null)throw new ve("`nonEnumerable`, if provided, must be a boolean or null");if(arguments.length>4&&typeof arguments[4]!="boolean"&&arguments[4]!==null)throw new ve("`nonWritable`, if provided, must be a boolean or null");if(arguments.length>5&&typeof arguments[5]!="boolean"&&arguments[5]!==null)throw new ve("`nonConfigurable`, if provided, must be a boolean or null");if(arguments.length>6&&typeof arguments[6]!="boolean")throw new ve("`loose`, if provided, must be a boolean");var n=arguments.length>3?arguments[3]:null,o=arguments.length>4?arguments[4]:null,i=arguments.length>5?arguments[5]:null,u=arguments.length>6?arguments[6]:!1,p=!!ao&&ao(t,r);if(ro)ro(t,r,{configurable:i===null&&p?p.configurable:!i,enumerable:n===null&&p?p.enumerable:!n,value:a,writable:o===null&&p?p.writable:!o});else if(u||!n&&!o&&!i)t[r]=a;else throw new tI("This environment does not support defining a property as non-configurable, non-writable, or non-enumerable.")}});var uo=R((ov,po)=>{"use strict";var Is=Se(),io=function(){return!!Is};io.hasArrayLengthDefineBug=function(){if(!Is)return null;try{return Is([],"length",{value:1}).length!==1}catch{return!0}};po.exports=io});var Ro=R((iv,yo)=>{"use strict";var sI=_e(),co=oo(),rI=uo()(),lo=We(),go=be(),aI=sI("%Math.floor%");yo.exports=function(t,r){if(typeof t!="function")throw new go("`fn` is not a function");if(typeof r!="number"||r<0||r>4294967295||aI(r)!==r)throw new go("`length` must be a positive 32-bit integer");var a=arguments.length>2&&!!arguments[2],n=!0,o=!0;if("length"in t&&lo){var i=lo(t,"length");i&&!i.configurable&&(n=!1),i&&!i.writable&&(o=!1)}return(n||o||!a)&&(rI?co(t,"length",r,!0,!0):co(t,"length",r)),t}});var xo=R((pv,Xe)=>{"use strict";var ks=fe(),et=_e(),nI=Ro(),oI=be(),ho=et("%Function.prototype.apply%"),bo=et("%Function.prototype.call%"),fo=et("%Reflect.apply%",!0)||ks.call(bo,ho),mo=Se(),iI=et("%Math.max%");Xe.exports=function(t){if(typeof t!="function")throw new oI("a function is required");var r=fo(ks,bo,arguments);return nI(r,1+iI(0,t.length-(arguments.length-1)),!0)};var qo=function(){return fo(ks,ho,arguments)};mo?mo(Xe.exports,"apply",{value:qo}):Xe.exports.apply=qo});var vo=R((uv,Po)=>{"use strict";var Io=_e(),ko=xo(),pI=ko(Io("String.prototype.indexOf"));Po.exports=function(t,r){var a=Io(t,!!r);return typeof a=="function"&&pI(t,".prototype.")>-1?ko(a):a}});var Ao=R(()=>{});var $o=R((lv,Ko)=>{var Es=typeof Map=="function"&&Map.prototype,Ps=Object.getOwnPropertyDescriptor&&Es?Object.getOwnPropertyDescriptor(Map.prototype,"size"):null,st=Es&&Ps&&typeof Ps.get=="function"?Ps.get:null,To=Es&&Map.prototype.forEach,Ls=typeof Set=="function"&&Set.prototype,vs=Object.getOwnPropertyDescriptor&&Ls?Object.getOwnPropertyDescriptor(Set.prototype,"size"):null,rt=Ls&&vs&&typeof vs.get=="function"?vs.get:null,Bo=Ls&&Set.prototype.forEach,uI=typeof WeakMap=="function"&&WeakMap.prototype,Me=uI?WeakMap.prototype.has:null,cI=typeof WeakSet=="function"&&WeakSet.prototype,Oe=cI?WeakSet.prototype.has:null,dI=typeof WeakRef=="function"&&WeakRef.prototype,Co=dI?WeakRef.prototype.deref:null,lI=Boolean.prototype.valueOf,gI=Object.prototype.toString,yI=Function.prototype.toString,RI=String.prototype.match,Ws=String.prototype.slice,ae=String.prototype.replace,mI=String.prototype.toUpperCase,wo=String.prototype.toLowerCase,Do=RegExp.prototype.test,Uo=Array.prototype.concat,N=Array.prototype.join,qI=Array.prototype.slice,Go=Math.floor,Bs=typeof BigInt=="function"?BigInt.prototype.valueOf:null,As=Object.getOwnPropertySymbols,Cs=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?Symbol.prototype.toString:null,Ae=typeof Symbol=="function"&&typeof Symbol.iterator=="object",Q=typeof Symbol=="function"&&Symbol.toStringTag&&(typeof Symbol.toStringTag===Ae||"symbol")?Symbol.toStringTag:null,_o=Object.prototype.propertyIsEnumerable,Eo=(typeof Reflect=="function"?Reflect.getPrototypeOf:Object.getPrototypeOf)||([].__proto__===Array.prototype?function(e){return e.__proto__}:null);function Lo(e,t){if(e===1/0||e===-1/0||e!==e||e&&e>-1e3&&e<1e3||Do.call(/e/,t))return t;var r=/[0-9](?=(?:[0-9]{3})+(?![0-9]))/g;if(typeof e=="number"){var a=e<0?-Go(-e):Go(e);if(a!==e){var n=String(a),o=Ws.call(t,n.length+1);return ae.call(n,r,"$&_")+"."+ae.call(ae.call(o,/([0-9]{3})/g,"$&_"),/_$/,"")}}return ae.call(t,r,"$&_")}var ws=Ao(),Wo=ws.custom,So=Oo(Wo)?Wo:null,Fo={__proto__:null,double:'"',single:"'"},hI={__proto__:null,double:/(["\\])/g,single:/(['\\])/g};Ko.exports=function e(t,r,a,n){var o=r||{};if(J(o,"quoteStyle")&&!J(Fo,o.quoteStyle))throw new TypeError('option "quoteStyle" must be "single" or "double"');if(J(o,"maxStringLength")&&(typeof o.maxStringLength=="number"?o.maxStringLength<0&&o.maxStringLength!==1/0:o.maxStringLength!==null))throw new TypeError('option "maxStringLength", if provided, must be a positive integer, Infinity, or `null`');var i=J(o,"customInspect")?o.customInspect:!0;if(typeof i!="boolean"&&i!=="symbol")throw new TypeError("option \"customInspect\", if provided, must be `true`, `false`, or `'symbol'`");if(J(o,"indent")&&o.indent!==null&&o.indent!=="	"&&!(parseInt(o.indent,10)===o.indent&&o.indent>0))throw new TypeError('option "indent" must be "\\t", an integer > 0, or `null`');if(J(o,"numericSeparator")&&typeof o.numericSeparator!="boolean")throw new TypeError('option "numericSeparator", if provided, must be `true` or `false`');var u=o.numericSeparator;if(typeof t>"u")return"undefined";if(t===null)return"null";if(typeof t=="boolean")return t?"true":"false";if(typeof t=="string")return No(t,o);if(typeof t=="number"){if(t===0)return 1/0/t>0?"0":"-0";var p=String(t);return u?Lo(t,p):p}if(typeof t=="bigint"){var s=String(t)+"n";return u?Lo(t,s):s}var y=typeof o.depth>"u"?5:o.depth;if(typeof a>"u"&&(a=0),a>=y&&y>0&&typeof t=="object")return Us(t)?"[Array]":"[Object]";var P=SI(o,a);if(typeof n>"u")n=[];else if(Vo(n,t)>=0)return"[Circular]";function b(V,ee,j){if(ee&&(n=qI.call(n),n.push(ee)),j){var Ee={depth:o.depth};return J(o,"quoteStyle")&&(Ee.quoteStyle=o.quoteStyle),e(V,Ee,a+1,n)}return e(V,o,a+1,n)}if(typeof t=="function"&&!Qo(t)){var k=TI(t),I=tt(t,b);return"[Function"+(k?": "+k:" (anonymous)")+"]"+(I.length>0?" { "+N.call(I,", ")+" }":"")}if(Oo(t)){var U=Ae?ae.call(String(t),/^(Symbol\(.*\))_[^)]*$/,"$1"):Cs.call(t);return typeof t=="object"&&!Ae?Fe(U):U}if(EI(t)){for(var w="<"+wo.call(String(t.nodeName)),x=t.attributes||[],$=0;$<x.length;$++)w+=" "+x[$].name+"="+Mo(bI(x[$].value),"double",o);return w+=">",t.childNodes&&t.childNodes.length&&(w+="..."),w+="</"+wo.call(String(t.nodeName))+">",w}if(Us(t)){if(t.length===0)return"[]";var Z=tt(t,b);return P&&!WI(Z)?"["+Gs(Z,P)+"]":"[ "+N.call(Z,", ")+" ]"}if(xI(t)){var Re=tt(t,b);return!("cause"in Error.prototype)&&"cause"in t&&!_o.call(t,"cause")?"{ ["+String(t)+"] "+N.call(Uo.call("[cause]: "+b(t.cause),Re),", ")+" }":Re.length===0?"["+String(t)+"]":"{ ["+String(t)+"] "+N.call(Re,", ")+" }"}if(typeof t=="object"&&i){if(So&&typeof t[So]=="function"&&ws)return ws(t,{depth:y-a});if(i!=="symbol"&&typeof t.inspect=="function")return t.inspect()}if(BI(t)){var we=[];return To&&To.call(t,function(V,ee){we.push(b(ee,t,!0)+" => "+b(V,t))}),Ho("Map",st.call(t),we,P)}if(UI(t)){var Ue=[];return Bo&&Bo.call(t,function(V){Ue.push(b(V,t))}),Ho("Set",rt.call(t),Ue,P)}if(CI(t))return Ts("WeakMap");if(GI(t))return Ts("WeakSet");if(wI(t))return Ts("WeakRef");if(kI(t))return Fe(b(Number(t)));if(vI(t))return Fe(b(Bs.call(t)));if(PI(t))return Fe(lI.call(t));if(II(t))return Fe(b(String(t)));if(typeof window<"u"&&t===window)return"{ [object Window] }";if(typeof globalThis<"u"&&t===globalThis||typeof global<"u"&&t===global)return"{ [object globalThis] }";if(!fI(t)&&!Qo(t)){var oe=tt(t,b),Ge=Eo?Eo(t)===Object.prototype:t instanceof Object||t.constructor===Object,X=t instanceof Object?"":"null prototype",me=!Ge&&Q&&Object(t)===t&&Q in t?Ws.call(ne(t),8,-1):X?"Object":"",ie=Ge||typeof t.constructor!="function"?"":t.constructor.name?t.constructor.name+" ":"",qe=ie+(me||X?"["+N.call(Uo.call([],me||[],X||[]),": ")+"] ":"");return oe.length===0?qe+"{}":P?qe+"{"+Gs(oe,P)+"}":qe+"{ "+N.call(oe,", ")+" }"}return String(t)};function Mo(e,t,r){var a=r.quoteStyle||t,n=Fo[a];return n+e+n}function bI(e){return ae.call(String(e),/"/g,"&quot;")}function Us(e){return ne(e)==="[object Array]"&&(!Q||!(typeof e=="object"&&Q in e))}function fI(e){return ne(e)==="[object Date]"&&(!Q||!(typeof e=="object"&&Q in e))}function Qo(e){return ne(e)==="[object RegExp]"&&(!Q||!(typeof e=="object"&&Q in e))}function xI(e){return ne(e)==="[object Error]"&&(!Q||!(typeof e=="object"&&Q in e))}function II(e){return ne(e)==="[object String]"&&(!Q||!(typeof e=="object"&&Q in e))}function kI(e){return ne(e)==="[object Number]"&&(!Q||!(typeof e=="object"&&Q in e))}function PI(e){return ne(e)==="[object Boolean]"&&(!Q||!(typeof e=="object"&&Q in e))}function Oo(e){if(Ae)return e&&typeof e=="object"&&e instanceof Symbol;if(typeof e=="symbol")return!0;if(!e||typeof e!="object"||!Cs)return!1;try{return Cs.call(e),!0}catch{}return!1}function vI(e){if(!e||typeof e!="object"||!Bs)return!1;try{return Bs.call(e),!0}catch{}return!1}var AI=Object.prototype.hasOwnProperty||function(e){return e in this};function J(e,t){return AI.call(e,t)}function ne(e){return gI.call(e)}function TI(e){if(e.name)return e.name;var t=RI.call(yI.call(e),/^function\s*([\w$]+)/);return t?t[1]:null}function Vo(e,t){if(e.indexOf)return e.indexOf(t);for(var r=0,a=e.length;r<a;r++)if(e[r]===t)return r;return-1}function BI(e){if(!st||!e||typeof e!="object")return!1;try{st.call(e);try{rt.call(e)}catch{return!0}return e instanceof Map}catch{}return!1}function CI(e){if(!Me||!e||typeof e!="object")return!1;try{Me.call(e,Me);try{Oe.call(e,Oe)}catch{return!0}return e instanceof WeakMap}catch{}return!1}function wI(e){if(!Co||!e||typeof e!="object")return!1;try{return Co.call(e),!0}catch{}return!1}function UI(e){if(!rt||!e||typeof e!="object")return!1;try{rt.call(e);try{st.call(e)}catch{return!0}return e instanceof Set}catch{}return!1}function GI(e){if(!Oe||!e||typeof e!="object")return!1;try{Oe.call(e,Oe);try{Me.call(e,Me)}catch{return!0}return e instanceof WeakSet}catch{}return!1}function EI(e){return!e||typeof e!="object"?!1:typeof HTMLElement<"u"&&e instanceof HTMLElement?!0:typeof e.nodeName=="string"&&typeof e.getAttribute=="function"}function No(e,t){if(e.length>t.maxStringLength){var r=e.length-t.maxStringLength,a="... "+r+" more character"+(r>1?"s":"");return No(Ws.call(e,0,t.maxStringLength),t)+a}var n=hI[t.quoteStyle||"single"];n.lastIndex=0;var o=ae.call(ae.call(e,n,"\\$1"),/[\x00-\x1f]/g,LI);return Mo(o,"single",t)}function LI(e){var t=e.charCodeAt(0),r={8:"b",9:"t",10:"n",12:"f",13:"r"}[t];return r?"\\"+r:"\\x"+(t<16?"0":"")+mI.call(t.toString(16))}function Fe(e){return"Object("+e+")"}function Ts(e){return e+" { ? }"}function Ho(e,t,r,a){var n=a?Gs(r,a):N.call(r,", ");return e+" ("+t+") {"+n+"}"}function WI(e){for(var t=0;t<e.length;t++)if(Vo(e[t],`
`)>=0)return!1;return!0}function SI(e,t){var r;if(e.indent==="	")r="	";else if(typeof e.indent=="number"&&e.indent>0)r=N.call(Array(e.indent+1)," ");else return null;return{base:r,prev:N.call(Array(t+1),r)}}function Gs(e,t){if(e.length===0)return"";var r=`
`+t.prev+t.base;return r+N.call(e,","+r)+`
`+t.prev}function tt(e,t){var r=Us(e),a=[];if(r){a.length=e.length;for(var n=0;n<e.length;n++)a[n]=J(e,n)?t(e[n],e):""}var o=typeof As=="function"?As(e):[],i;if(Ae){i={};for(var u=0;u<o.length;u++)i["$"+o[u]]=o[u]}for(var p in e)J(e,p)&&(r&&String(Number(p))===p&&p<e.length||Ae&&i["$"+p]instanceof Symbol||(Do.call(/[^\w$]/,p)?a.push(t(p,e)+": "+t(e[p],e)):a.push(p+": "+t(e[p],e))));if(typeof As=="function")for(var s=0;s<o.length;s++)_o.call(e,o[s])&&a.push("["+t(o[s])+"]: "+t(e[o[s]],e));return a}});var zo=R((gv,jo)=>{"use strict";var Ss=_e(),Te=vo(),QI=$o(),HI=Ss("%TypeError%"),at=Ss("%WeakMap%",!0),nt=Ss("%Map%",!0),DI=Te("WeakMap.prototype.get",!0),_I=Te("WeakMap.prototype.set",!0),FI=Te("WeakMap.prototype.has",!0),MI=Te("Map.prototype.get",!0),OI=Te("Map.prototype.set",!0),VI=Te("Map.prototype.has",!0),Qs=function(e,t){for(var r=e,a;(a=r.next)!==null;r=a)if(a.key===t)return r.next=a.next,a.next=e.next,e.next=a,a},NI=function(e,t){var r=Qs(e,t);return r&&r.value},KI=function(e,t,r){var a=Qs(e,t);a?a.value=r:e.next={key:t,next:e.next,value:r}},$I=function(e,t){return!!Qs(e,t)};jo.exports=function(){var t,r,a,n={assert:function(o){if(!n.has(o))throw new HI("Side channel does not contain "+QI(o))},get:function(o){if(at&&o&&(typeof o=="object"||typeof o=="function")){if(t)return DI(t,o)}else if(nt){if(r)return MI(r,o)}else if(a)return NI(a,o)},has:function(o){if(at&&o&&(typeof o=="object"||typeof o=="function")){if(t)return FI(t,o)}else if(nt){if(r)return VI(r,o)}else if(a)return $I(a,o);return!1},set:function(o,i){at&&o&&(typeof o=="object"||typeof o=="function")?(t||(t=new at),_I(t,o,i)):nt?(r||(r=new nt),OI(r,o,i)):(a||(a={key:{},next:null}),KI(a,o,i))}};return n}});var ot=R((yv,Jo)=>{"use strict";var jI=String.prototype.replace,zI=/%20/g,Hs={RFC1738:"RFC1738",RFC3986:"RFC3986"};Jo.exports={default:Hs.RFC3986,formatters:{RFC1738:function(e){return jI.call(e,zI,"+")},RFC3986:function(e){return String(e)}},RFC1738:Hs.RFC1738,RFC3986:Hs.RFC3986}});var _s=R((Rv,Zo)=>{"use strict";var JI=ot(),Ds=Object.prototype.hasOwnProperty,de=Array.isArray,K=function(){for(var e=[],t=0;t<256;++t)e.push("%"+((t<16?"0":"")+t.toString(16)).toUpperCase());return e}(),YI=function(t){for(;t.length>1;){var r=t.pop(),a=r.obj[r.prop];if(de(a)){for(var n=[],o=0;o<a.length;++o)typeof a[o]<"u"&&n.push(a[o]);r.obj[r.prop]=n}}},Yo=function(t,r){for(var a=r&&r.plainObjects?Object.create(null):{},n=0;n<t.length;++n)typeof t[n]<"u"&&(a[n]=t[n]);return a},ZI=function e(t,r,a){if(!r)return t;if(typeof r!="object"){if(de(t))t.push(r);else if(t&&typeof t=="object")(a&&(a.plainObjects||a.allowPrototypes)||!Ds.call(Object.prototype,r))&&(t[r]=!0);else return[t,r];return t}if(!t||typeof t!="object")return[t].concat(r);var n=t;return de(t)&&!de(r)&&(n=Yo(t,a)),de(t)&&de(r)?(r.forEach(function(o,i){if(Ds.call(t,i)){var u=t[i];u&&typeof u=="object"&&o&&typeof o=="object"?t[i]=e(u,o,a):t.push(o)}else t[i]=o}),t):Object.keys(r).reduce(function(o,i){var u=r[i];return Ds.call(o,i)?o[i]=e(o[i],u,a):o[i]=u,o},n)},XI=function(t,r){return Object.keys(r).reduce(function(a,n){return a[n]=r[n],a},t)},ek=function(e,t,r){var a=e.replace(/\+/g," ");if(r==="iso-8859-1")return a.replace(/%[0-9a-f]{2}/gi,unescape);try{return decodeURIComponent(a)}catch{return a}},tk=function(t,r,a,n,o){if(t.length===0)return t;var i=t;if(typeof t=="symbol"?i=Symbol.prototype.toString.call(t):typeof t!="string"&&(i=String(t)),a==="iso-8859-1")return escape(i).replace(/%u[0-9a-f]{4}/gi,function(y){return"%26%23"+parseInt(y.slice(2),16)+"%3B"});for(var u="",p=0;p<i.length;++p){var s=i.charCodeAt(p);if(s===45||s===46||s===95||s===126||s>=48&&s<=57||s>=65&&s<=90||s>=97&&s<=122||o===JI.RFC1738&&(s===40||s===41)){u+=i.charAt(p);continue}if(s<128){u=u+K[s];continue}if(s<2048){u=u+(K[192|s>>6]+K[128|s&63]);continue}if(s<55296||s>=57344){u=u+(K[224|s>>12]+K[128|s>>6&63]+K[128|s&63]);continue}p+=1,s=65536+((s&1023)<<10|i.charCodeAt(p)&1023),u+=K[240|s>>18]+K[128|s>>12&63]+K[128|s>>6&63]+K[128|s&63]}return u},sk=function(t){for(var r=[{obj:{o:t},prop:"o"}],a=[],n=0;n<r.length;++n)for(var o=r[n],i=o.obj[o.prop],u=Object.keys(i),p=0;p<u.length;++p){var s=u[p],y=i[s];typeof y=="object"&&y!==null&&a.indexOf(y)===-1&&(r.push({obj:i,prop:s}),a.push(y))}return YI(r),t},rk=function(t){return Object.prototype.toString.call(t)==="[object RegExp]"},ak=function(t){return!t||typeof t!="object"?!1:!!(t.constructor&&t.constructor.isBuffer&&t.constructor.isBuffer(t))},nk=function(t,r){return[].concat(t,r)},ok=function(t,r){if(de(t)){for(var a=[],n=0;n<t.length;n+=1)a.push(r(t[n]));return a}return r(t)};Zo.exports={arrayToObject:Yo,assign:XI,combine:nk,compact:sk,decode:ek,encode:tk,isBuffer:ak,isRegExp:rk,maybeMap:ok,merge:ZI}});var ai=R((mv,ri)=>{"use strict";var ti=zo(),Ms=_s(),Ve=ot(),ik=Object.prototype.hasOwnProperty,Xo={brackets:function(t){return t+"[]"},comma:"comma",indices:function(t,r){return t+"["+r+"]"},repeat:function(t){return t}},Y=Array.isArray,pk=String.prototype.split,uk=Array.prototype.push,si=function(e,t){uk.apply(e,Y(t)?t:[t])},ck=Date.prototype.toISOString,ei=Ve.default,W={addQueryPrefix:!1,allowDots:!1,charset:"utf-8",charsetSentinel:!1,delimiter:"&",encode:!0,encoder:Ms.encode,encodeValuesOnly:!1,format:ei,formatter:Ve.formatters[ei],indices:!1,serializeDate:function(t){return ck.call(t)},skipNulls:!1,strictNullHandling:!1},dk=function(t){return typeof t=="string"||typeof t=="number"||typeof t=="boolean"||typeof t=="symbol"||typeof t=="bigint"},Fs={},lk=function e(t,r,a,n,o,i,u,p,s,y,P,b,k,I,U,w){for(var x=t,$=w,Z=0,Re=!1;($=$.get(Fs))!==void 0&&!Re;){var we=$.get(t);if(Z+=1,typeof we<"u"){if(we===Z)throw new RangeError("Cyclic object value");Re=!0}typeof $.get(Fs)>"u"&&(Z=0)}if(typeof p=="function"?x=p(r,x):x instanceof Date?x=P(x):a==="comma"&&Y(x)&&(x=Ms.maybeMap(x,function(gt){return gt instanceof Date?P(gt):gt})),x===null){if(o)return u&&!I?u(r,W.encoder,U,"key",b):r;x=""}if(dk(x)||Ms.isBuffer(x)){if(u){var Ue=I?r:u(r,W.encoder,U,"key",b);if(a==="comma"&&I){for(var oe=pk.call(String(x),","),Ge="",X=0;X<oe.length;++X)Ge+=(X===0?"":",")+k(u(oe[X],W.encoder,U,"value",b));return[k(Ue)+(n&&Y(x)&&oe.length===1?"[]":"")+"="+Ge]}return[k(Ue)+"="+k(u(x,W.encoder,U,"value",b))]}return[k(r)+"="+k(String(x))]}var me=[];if(typeof x>"u")return me;var ie;if(a==="comma"&&Y(x))ie=[{value:x.length>0?x.join(",")||null:void 0}];else if(Y(p))ie=p;else{var qe=Object.keys(x);ie=s?qe.sort(s):qe}for(var V=n&&Y(x)&&x.length===1?r+"[]":r,ee=0;ee<ie.length;++ee){var j=ie[ee],Ee=typeof j=="object"&&typeof j.value<"u"?j.value:x[j];if(!(i&&Ee===null)){var ff=Y(x)?typeof a=="function"?a(V,j):V:V+(y?"."+j:"["+j+"]");w.set(t,Z);var Ra=ti();Ra.set(Fs,w),si(me,e(Ee,ff,a,n,o,i,u,p,s,y,P,b,k,I,U,Ra))}}return me},gk=function(t){if(!t)return W;if(t.encoder!==null&&typeof t.encoder<"u"&&typeof t.encoder!="function")throw new TypeError("Encoder has to be a function.");var r=t.charset||W.charset;if(typeof t.charset<"u"&&t.charset!=="utf-8"&&t.charset!=="iso-8859-1")throw new TypeError("The charset option must be either utf-8, iso-8859-1, or undefined");var a=Ve.default;if(typeof t.format<"u"){if(!ik.call(Ve.formatters,t.format))throw new TypeError("Unknown format option provided.");a=t.format}var n=Ve.formatters[a],o=W.filter;return(typeof t.filter=="function"||Y(t.filter))&&(o=t.filter),{addQueryPrefix:typeof t.addQueryPrefix=="boolean"?t.addQueryPrefix:W.addQueryPrefix,allowDots:typeof t.allowDots>"u"?W.allowDots:!!t.allowDots,charset:r,charsetSentinel:typeof t.charsetSentinel=="boolean"?t.charsetSentinel:W.charsetSentinel,delimiter:typeof t.delimiter>"u"?W.delimiter:t.delimiter,encode:typeof t.encode=="boolean"?t.encode:W.encode,encoder:typeof t.encoder=="function"?t.encoder:W.encoder,encodeValuesOnly:typeof t.encodeValuesOnly=="boolean"?t.encodeValuesOnly:W.encodeValuesOnly,filter:o,format:a,formatter:n,serializeDate:typeof t.serializeDate=="function"?t.serializeDate:W.serializeDate,skipNulls:typeof t.skipNulls=="boolean"?t.skipNulls:W.skipNulls,sort:typeof t.sort=="function"?t.sort:null,strictNullHandling:typeof t.strictNullHandling=="boolean"?t.strictNullHandling:W.strictNullHandling}};ri.exports=function(e,t){var r=e,a=gk(t),n,o;typeof a.filter=="function"?(o=a.filter,r=o("",r)):Y(a.filter)&&(o=a.filter,n=o);var i=[];if(typeof r!="object"||r===null)return"";var u;t&&t.arrayFormat in Xo?u=t.arrayFormat:t&&"indices"in t?u=t.indices?"indices":"repeat":u="indices";var p=Xo[u];if(t&&"commaRoundTrip"in t&&typeof t.commaRoundTrip!="boolean")throw new TypeError("`commaRoundTrip` must be a boolean, or absent");var s=p==="comma"&&t&&t.commaRoundTrip;n||(n=Object.keys(r)),a.sort&&n.sort(a.sort);for(var y=ti(),P=0;P<n.length;++P){var b=n[P];a.skipNulls&&r[b]===null||si(i,lk(r[b],b,p,s,a.strictNullHandling,a.skipNulls,a.encode?a.encoder:null,a.filter,a.sort,a.allowDots,a.serializeDate,a.format,a.formatter,a.encodeValuesOnly,a.charset,y))}var k=i.join(a.delimiter),I=a.addQueryPrefix===!0?"?":"";return a.charsetSentinel&&(a.charset==="iso-8859-1"?I+="utf8=%26%2310003%3B&":I+="utf8=%E2%9C%93&"),k.length>0?I+k:""}});var ii=R((qv,oi)=>{"use strict";var Be=_s(),Os=Object.prototype.hasOwnProperty,yk=Array.isArray,E={allowDots:!1,allowPrototypes:!1,allowSparse:!1,arrayLimit:20,charset:"utf-8",charsetSentinel:!1,comma:!1,decoder:Be.decode,delimiter:"&",depth:5,ignoreQueryPrefix:!1,interpretNumericEntities:!1,parameterLimit:1e3,parseArrays:!0,plainObjects:!1,strictNullHandling:!1},Rk=function(e){return e.replace(/&#(\d+);/g,function(t,r){return String.fromCharCode(parseInt(r,10))})},ni=function(e,t){return e&&typeof e=="string"&&t.comma&&e.indexOf(",")>-1?e.split(","):e},mk="utf8=%26%2310003%3B",qk="utf8=%E2%9C%93",hk=function(t,r){var a={},n=r.ignoreQueryPrefix?t.replace(/^\?/,""):t,o=r.parameterLimit===1/0?void 0:r.parameterLimit,i=n.split(r.delimiter,o),u=-1,p,s=r.charset;if(r.charsetSentinel)for(p=0;p<i.length;++p)i[p].indexOf("utf8=")===0&&(i[p]===qk?s="utf-8":i[p]===mk&&(s="iso-8859-1"),u=p,p=i.length);for(p=0;p<i.length;++p)if(p!==u){var y=i[p],P=y.indexOf("]="),b=P===-1?y.indexOf("="):P+1,k,I;b===-1?(k=r.decoder(y,E.decoder,s,"key"),I=r.strictNullHandling?null:""):(k=r.decoder(y.slice(0,b),E.decoder,s,"key"),I=Be.maybeMap(ni(y.slice(b+1),r),function(U){return r.decoder(U,E.decoder,s,"value")})),I&&r.interpretNumericEntities&&s==="iso-8859-1"&&(I=Rk(I)),y.indexOf("[]=")>-1&&(I=yk(I)?[I]:I),Os.call(a,k)?a[k]=Be.combine(a[k],I):a[k]=I}return a},bk=function(e,t,r,a){for(var n=a?t:ni(t,r),o=e.length-1;o>=0;--o){var i,u=e[o];if(u==="[]"&&r.parseArrays)i=[].concat(n);else{i=r.plainObjects?Object.create(null):{};var p=u.charAt(0)==="["&&u.charAt(u.length-1)==="]"?u.slice(1,-1):u,s=parseInt(p,10);!r.parseArrays&&p===""?i={0:n}:!isNaN(s)&&u!==p&&String(s)===p&&s>=0&&r.parseArrays&&s<=r.arrayLimit?(i=[],i[s]=n):p!=="__proto__"&&(i[p]=n)}n=i}return n},fk=function(t,r,a,n){if(t){var o=a.allowDots?t.replace(/\.([^.[]+)/g,"[$1]"):t,i=/(\[[^[\]]*])/,u=/(\[[^[\]]*])/g,p=a.depth>0&&i.exec(o),s=p?o.slice(0,p.index):o,y=[];if(s){if(!a.plainObjects&&Os.call(Object.prototype,s)&&!a.allowPrototypes)return;y.push(s)}for(var P=0;a.depth>0&&(p=u.exec(o))!==null&&P<a.depth;){if(P+=1,!a.plainObjects&&Os.call(Object.prototype,p[1].slice(1,-1))&&!a.allowPrototypes)return;y.push(p[1])}return p&&y.push("["+o.slice(p.index)+"]"),bk(y,r,a,n)}},xk=function(t){if(!t)return E;if(t.decoder!==null&&t.decoder!==void 0&&typeof t.decoder!="function")throw new TypeError("Decoder has to be a function.");if(typeof t.charset<"u"&&t.charset!=="utf-8"&&t.charset!=="iso-8859-1")throw new TypeError("The charset option must be either utf-8, iso-8859-1, or undefined");var r=typeof t.charset>"u"?E.charset:t.charset;return{allowDots:typeof t.allowDots>"u"?E.allowDots:!!t.allowDots,allowPrototypes:typeof t.allowPrototypes=="boolean"?t.allowPrototypes:E.allowPrototypes,allowSparse:typeof t.allowSparse=="boolean"?t.allowSparse:E.allowSparse,arrayLimit:typeof t.arrayLimit=="number"?t.arrayLimit:E.arrayLimit,charset:r,charsetSentinel:typeof t.charsetSentinel=="boolean"?t.charsetSentinel:E.charsetSentinel,comma:typeof t.comma=="boolean"?t.comma:E.comma,decoder:typeof t.decoder=="function"?t.decoder:E.decoder,delimiter:typeof t.delimiter=="string"||Be.isRegExp(t.delimiter)?t.delimiter:E.delimiter,depth:typeof t.depth=="number"||t.depth===!1?+t.depth:E.depth,ignoreQueryPrefix:t.ignoreQueryPrefix===!0,interpretNumericEntities:typeof t.interpretNumericEntities=="boolean"?t.interpretNumericEntities:E.interpretNumericEntities,parameterLimit:typeof t.parameterLimit=="number"?t.parameterLimit:E.parameterLimit,parseArrays:t.parseArrays!==!1,plainObjects:typeof t.plainObjects=="boolean"?t.plainObjects:E.plainObjects,strictNullHandling:typeof t.strictNullHandling=="boolean"?t.strictNullHandling:E.strictNullHandling}};oi.exports=function(e,t){var r=xk(t);if(e===""||e===null||typeof e>"u")return r.plainObjects?Object.create(null):{};for(var a=typeof e=="string"?hk(e,r):e,n=r.plainObjects?Object.create(null):{},o=Object.keys(a),i=0;i<o.length;++i){var u=o[i],p=fk(u,a[u],r,typeof e=="string");n=Be.merge(n,p,r)}return r.allowSparse===!0?n:Be.compact(n)}});var Ce=R((hv,pi)=>{"use strict";var Ik=ai(),kk=ii(),Pk=ot();pi.exports={formats:Pk,parse:kk,stringify:Ik}});import*as RA from"axios";var va={};z(va,{DEFAULT_OPTIONS:()=>Pa,default:()=>O,exponentialDelay:()=>Ia,isIdempotentRequestError:()=>mt,isNetworkError:()=>Rt,isNetworkOrIdempotentRequestError:()=>qt,isRetryableError:()=>Ke,isSafeRequestError:()=>xa,linearDelay:()=>ka,namespace:()=>Ne,retryAfter:()=>$e});var ba=te(qa(),1),Ne="axios-retry";function Rt(e){let t=["ERR_CANCELED","ECONNABORTED"];return e.response||!e.code||t.includes(e.code)?!1:(0,ba.default)(e)}var fa=["get","head","options"],Bf=fa.concat(["put","delete"]);function Ke(e){return e.code!=="ECONNABORTED"&&(!e.response||e.response.status===429||e.response.status>=500&&e.response.status<=599)}function xa(e){return e.config?.method?Ke(e)&&fa.indexOf(e.config.method)!==-1:!1}function mt(e){return e.config?.method?Ke(e)&&Bf.indexOf(e.config.method)!==-1:!1}function qt(e){return Rt(e)||mt(e)}function $e(e=void 0){let t=e?.response?.headers["retry-after"];if(!t)return 0;let r=(Number(t)||0)*1e3;return r===0&&(r=(new Date(t).valueOf()||0)-Date.now()),Math.max(0,r)}function Cf(e=0,t=void 0){return Math.max(0,$e(t))}function Ia(e=0,t=void 0,r=100){let a=2**e*r,n=Math.max(a,$e(t)),o=n*.2*Math.random();return n+o}function ka(e=100){return(t=0,r=void 0)=>{let a=t*e;return Math.max(a,$e(r))}}var Pa={retries:3,retryCondition:qt,retryDelay:Cf,shouldResetTimeout:!1,onRetry:()=>{},onMaxRetryTimesExceeded:()=>{},validateResponse:null};function wf(e,t){return{...Pa,...t,...e[Ne]}}function ha(e,t,r=!1){let a=wf(e,t||{});return a.retryCount=a.retryCount||0,(!a.lastRequestTime||r)&&(a.lastRequestTime=Date.now()),e[Ne]=a,a}function Uf(e,t){e.defaults.agent===t.agent&&delete t.agent,e.defaults.httpAgent===t.httpAgent&&delete t.httpAgent,e.defaults.httpsAgent===t.httpsAgent&&delete t.httpsAgent}async function Gf(e,t){let{retries:r,retryCondition:a}=e,n=(e.retryCount||0)<r&&a(t);if(typeof n=="object")try{return await n!==!1}catch{return!1}return n}async function Ef(e,t,r,a){t.retryCount+=1;let{retryDelay:n,shouldResetTimeout:o,onRetry:i}=t,u=n(t.retryCount,r);if(Uf(e,a),!o&&a.timeout&&t.lastRequestTime){let p=Date.now()-t.lastRequestTime,s=a.timeout-p-u;if(s<=0)return Promise.reject(r);a.timeout=s}return a.transformRequest=[p=>p],await i(t.retryCount,r,a),a.signal?.aborted?Promise.resolve(e(a)):new Promise(p=>{let s=()=>{clearTimeout(y),p(e(a))},y=setTimeout(()=>{p(e(a)),a.signal?.removeEventListener&&a.signal.removeEventListener("abort",s)},u);a.signal?.addEventListener&&a.signal.addEventListener("abort",s,{once:!0})})}async function Lf(e,t){e.retryCount>=e.retries&&await e.onMaxRetryTimesExceeded(t,e.retryCount)}var se=(e,t)=>{let r=e.interceptors.request.use(n=>(ha(n,t,!0),n[Ne]?.validateResponse&&(n.validateStatus=()=>!1),n)),a=e.interceptors.response.use(null,async n=>{let{config:o}=n;if(!o)return Promise.reject(n);let i=ha(o,t);return n.response&&i.validateResponse?.(n.response)?n.response:await Gf(i,n)?Ef(e,i,n,o):(await Lf(i,n),Promise.reject(n))});return{requestInterceptorId:r,responseInterceptorId:a}};se.isNetworkError=Rt;se.isSafeRequestError=xa;se.isIdempotentRequestError=mt;se.isNetworkOrIdempotentRequestError=qt;se.exponentialDelay=Ia;se.linearDelay=ka;se.isRetryableError=Ke;var O=se;var Kp={};z(Kp,{Client:()=>Vs});import Bk from"axios";var Ca=te(Aa()),wa=te(Ta());import{isNode as Ba}from"browser-or-node";var Ua=100*1024*1024,Ga=Ua,Ea=Ua,La=Ba?new Ca.default.Agent({keepAlive:!0}):void 0,Wa=Ba?new wa.default.Agent({keepAlive:!0}):void 0;var _={};z(_,{getClientConfig:()=>Of});import{isBrowser as Qa,isNode as Wf}from"browser-or-node";var Sf="https://api.botpress.cloud",Qf=6e4,Hf="BP_API_URL",Df="BP_BOT_ID",_f="BP_INTEGRATION_ID",Ff="BP_WORKSPACE_ID",Mf="BP_TOKEN";function Of(e){let t=Vf(e),r={};t.workspaceId&&(r["x-workspace-id"]=t.workspaceId),t.botId&&(r["x-bot-id"]=t.botId),t.integrationId&&(r["x-integration-id"]=t.integrationId),t.token&&(r.Authorization=`Bearer ${t.token}`),r={...r,...t.headers};let a=t.apiUrl??Sf,n=t.timeout??Qf;return{apiUrl:a,timeout:n,withCredentials:Qa,headers:r}}function Vf(e){return Qa?e:Wf?Nf(e):e}function Nf(e){let t={...e,apiUrl:e.apiUrl??process.env[Hf],botId:e.botId??process.env[Df],integrationId:e.integrationId??process.env[_f],workspaceId:e.workspaceId??process.env[Ff]},r=t.token??process.env[Mf];return r&&(t.token=r),t}var h={};z(h,{AsyncCollection:()=>ht});var ht=class{constructor(t){this._list=t}async*[Symbol.asyncIterator](){let t;do{let{items:r,meta:a}=await this._list({nextToken:t});t=a.nextToken;for(let n of r)yield n}while(t)}async collect(t={}){let r=t.limit??Number.POSITIVE_INFINITY,a=[],n=0;for await(let o of this)if(a.push(o),n++,n>=r)break;return a}};var F={};z(F,{createAxios:()=>Kf});var Kf=e=>({baseURL:e.apiUrl,headers:e.headers,withCredentials:e.withCredentials,timeout:e.timeout,maxBodyLength:Ga,maxContentLength:Ea,httpAgent:La,httpsAgent:Wa});var M={};z(M,{toApiError:()=>ex});import Xf from"axios";import $f from"crypto";var jf={getRandomValues:e=>new Uint8Array(e.map(()=>Math.floor(Math.random()*256)))},bt=typeof window<"u"&&typeof window.document<"u"?window.crypto:$f;bt.getRandomValues||(bt=jf);var v=class extends Error{constructor(r,a,n,o,i,u,p){super(o);this.code=r;this.description=a;this.type=n;this.message=o;this.error=i;this.id=u;this.metadata=p;this.id||(this.id=v.generateId())}isApiError=!0;format(){return`[${this.type}] ${this.message} (Error ID: ${this.id})`}toJSON(){return{id:this.id,code:this.code,type:this.type,message:this.message,metadata:this.metadata}}static generateId(){let r=this.getPrefix(),a=new Date().toISOString().replace(/[\-:TZ]/g,"").split(".")[0],n=4,o=Array.from(bt.getRandomValues(new Uint8Array(n))).map(i=>i.toString(16).padStart(2,"0")).join("").toUpperCase();return`${r}_${a}x${o}`}static getPrefix(){return typeof window<"u"&&typeof window.document<"u"?"err_bwsr":"err"}},zf=e=>typeof e=="object"&&!Array.isArray(e)&&e!==null,Jf=e=>e instanceof v||zf(e)&&e.isApiError===!0,pe=class extends v{constructor(t,r,a,n){super(500,"An unknown error occurred","Unknown",t,r,a,n)}},ft=class extends v{constructor(t,r,a,n){super(500,"An internal error occurred","Internal",t,r,a,n)}},xt=class extends v{constructor(t,r,a,n){super(401,"The request requires to be authenticated.","Unauthorized",t,r,a,n)}},It=class extends v{constructor(t,r,a,n){super(403,"The requested action can't be peform by this resource.","Forbidden",t,r,a,n)}},kt=class extends v{constructor(t,r,a,n){super(413,"The request payload is too large.","PayloadTooLarge",t,r,a,n)}},Pt=class extends v{constructor(t,r,a,n){super(400,"The request payload is invalid.","InvalidPayload",t,r,a,n)}},vt=class extends v{constructor(t,r,a,n){super(415,"The request is invalid because the content-type is not supported.","UnsupportedMediaType",t,r,a,n)}},At=class extends v{constructor(t,r,a,n){super(405,"The requested method does not exist.","MethodNotFound",t,r,a,n)}},Tt=class extends v{constructor(t,r,a,n){super(404,"The requested resource does not exist.","ResourceNotFound",t,r,a,n)}},Bt=class extends v{constructor(t,r,a,n){super(400,"The provided JSON schema is invalid.","InvalidJsonSchema",t,r,a,n)}},Ct=class extends v{constructor(t,r,a,n){super(400,"The provided data doesn't respect the provided JSON schema.","InvalidDataFormat",t,r,a,n)}},wt=class extends v{constructor(t,r,a,n){super(400,"The provided identifier is not valid. An identifier must start with a lowercase letter, be between 2 and 100 characters long and use only alphanumeric characters.","InvalidIdentifier",t,r,a,n)}},Ut=class extends v{constructor(t,r,a,n){super(409,"The resource is related with a different resource that the one referenced in the request. This is usually caused when providing two resource identifiers that aren't linked together.","RelationConflict",t,r,a,n)}},Gt=class extends v{constructor(t,r,a,n){super(409,"The resource cannot be deleted because it's referenced by another resource","ReferenceConstraint",t,r,a,n)}},Et=class extends v{constructor(t,r,a,n){super(409,"The resource is current locked and cannot be operated on until the lock is released.","ResourceLockedConflict",t,r,a,n)}},Lt=class extends v{constructor(t,r,a,n){super(400,"The provided resource reference is missing. This is usually caused when providing an invalid id inside the payload of a request.","ReferenceNotFound",t,r,a,n)}},Wt=class extends v{constructor(t,r,a,n){super(400,"The provided query is invalid. This is usually caused when providing an invalid parameter for querying a resource.","InvalidQuery",t,r,a,n)}},St=class extends v{constructor(t,r,a,n){super(400,"An error happened during the execution of a runtime (bot or integration).","Runtime",t,r,a,n)}},Qt=class extends v{constructor(t,r,a,n){super(409,"The record attempted to be created already exists.","AlreadyExists",t,r,a,n)}},Ht=class extends v{constructor(t,r,a,n){super(429,"The request has been rate limited.","RateLimited",t,r,a,n)}},Dt=class extends v{constructor(t,r,a,n){super(402,"A payment is required to perform this request.","PaymentRequired",t,r,a,n)}},_t=class extends v{constructor(t,r,a,n){super(403,"The request exceeds the allowed quota. Quotas are a soft limit that can be increased.","QuotaExceeded",t,r,a,n)}},Ft=class extends v{constructor(t,r,a,n){super(413,"The request exceeds the allowed limit. Limits are a hard limit that cannot be increased.","LimitExceeded",t,r,a,n)}},Mt=class extends v{constructor(t,r,a,n){super(400,"Request payload contains breaking changes which is not allowed for this resource without a version increment.","BreakingChanges",t,r,a,n)}},Yf={Unknown:pe,Internal:ft,Unauthorized:xt,Forbidden:It,PayloadTooLarge:kt,InvalidPayload:Pt,UnsupportedMediaType:vt,MethodNotFound:At,ResourceNotFound:Tt,InvalidJsonSchema:Bt,InvalidDataFormat:Ct,InvalidIdentifier:wt,RelationConflict:Ut,ReferenceConstraint:Gt,ResourceLockedConflict:Et,ReferenceNotFound:Lt,InvalidQuery:Wt,Runtime:St,AlreadyExists:Qt,RateLimited:Ht,PaymentRequired:Dt,QuotaExceeded:_t,LimitExceeded:Ft,BreakingChanges:Mt},he=e=>Jf(e)?e:e instanceof Error?new pe(e.message,e):typeof e=="string"?new pe(e):Zf(e);function Zf(e){if(typeof e=="object"&&"code"in e&&"type"in e&&"id"in e&&"message"in e&&typeof e.type=="string"&&typeof e.message=="string"){let t=Yf[e.type];return t?new t(e.message,void 0,e.id||"UNKNOWN",e.metadata):new pe(`An unclassified API error occurred: ${e.message} (Type: ${e.type}, Code: ${e.code})`)}return new pe("An invalid error occurred: "+JSON.stringify(e))}var re=class extends Error{constructor(r,a,n){super(r);this.innerError=a;this.file=n;this.name="FileUploadError"}};var ex=e=>Xf.isAxiosError(e)&&e.response?.data?he(e.response.data):he(e);import Ak from"axios";import tx from"crypto";var sx={getRandomValues:e=>new Uint8Array(e.map(()=>Math.floor(Math.random()*256)))},Ot=typeof window<"u"&&typeof window.document<"u"?window.crypto:tx;Ot.getRandomValues||(Ot=sx);var A=class extends Error{constructor(r,a,n,o,i,u,p){super(o);this.code=r;this.description=a;this.type=n;this.message=o;this.error=i;this.id=u;this.metadata=p;this.id||(this.id=A.generateId())}isApiError=!0;format(){return`[${this.type}] ${this.message} (Error ID: ${this.id})`}toJSON(){return{id:this.id,code:this.code,type:this.type,message:this.message,metadata:this.metadata}}static generateId(){let r=this.getPrefix(),a=new Date().toISOString().replace(/[\-:TZ]/g,"").split(".")[0],n=4,o=Array.from(Ot.getRandomValues(new Uint8Array(n))).map(i=>i.toString(16).padStart(2,"0")).join("").toUpperCase();return`${r}_${a}x${o}`}static getPrefix(){return typeof window<"u"&&typeof window.document<"u"?"err_bwsr":"err"}},rx=e=>typeof e=="object"&&!Array.isArray(e)&&e!==null,ax=e=>e instanceof A||rx(e)&&e.isApiError===!0,ue=class extends A{constructor(t,r,a,n){super(500,"An unknown error occurred","Unknown",t,r,a,n)}},Vt=class extends A{constructor(t,r,a,n){super(500,"An internal error occurred","Internal",t,r,a,n)}},Nt=class extends A{constructor(t,r,a,n){super(401,"The request requires to be authenticated.","Unauthorized",t,r,a,n)}},Kt=class extends A{constructor(t,r,a,n){super(403,"The requested action can't be peform by this resource.","Forbidden",t,r,a,n)}},$t=class extends A{constructor(t,r,a,n){super(413,"The request payload is too large.","PayloadTooLarge",t,r,a,n)}},jt=class extends A{constructor(t,r,a,n){super(400,"The request payload is invalid.","InvalidPayload",t,r,a,n)}},zt=class extends A{constructor(t,r,a,n){super(415,"The request is invalid because the content-type is not supported.","UnsupportedMediaType",t,r,a,n)}},Jt=class extends A{constructor(t,r,a,n){super(405,"The requested method does not exist.","MethodNotFound",t,r,a,n)}},Yt=class extends A{constructor(t,r,a,n){super(404,"The requested resource does not exist.","ResourceNotFound",t,r,a,n)}},Zt=class extends A{constructor(t,r,a,n){super(400,"The provided JSON schema is invalid.","InvalidJsonSchema",t,r,a,n)}},Xt=class extends A{constructor(t,r,a,n){super(400,"The provided data doesn't respect the provided JSON schema.","InvalidDataFormat",t,r,a,n)}},es=class extends A{constructor(t,r,a,n){super(400,"The provided identifier is not valid. An identifier must start with a lowercase letter, be between 2 and 100 characters long and use only alphanumeric characters.","InvalidIdentifier",t,r,a,n)}},ts=class extends A{constructor(t,r,a,n){super(409,"The resource is related with a different resource that the one referenced in the request. This is usually caused when providing two resource identifiers that aren't linked together.","RelationConflict",t,r,a,n)}},ss=class extends A{constructor(t,r,a,n){super(409,"The resource cannot be deleted because it's referenced by another resource","ReferenceConstraint",t,r,a,n)}},rs=class extends A{constructor(t,r,a,n){super(409,"The resource is current locked and cannot be operated on until the lock is released.","ResourceLockedConflict",t,r,a,n)}},as=class extends A{constructor(t,r,a,n){super(400,"The provided resource reference is missing. This is usually caused when providing an invalid id inside the payload of a request.","ReferenceNotFound",t,r,a,n)}},ns=class extends A{constructor(t,r,a,n){super(400,"The provided query is invalid. This is usually caused when providing an invalid parameter for querying a resource.","InvalidQuery",t,r,a,n)}},os=class extends A{constructor(t,r,a,n){super(400,"An error happened during the execution of a runtime (bot or integration).","Runtime",t,r,a,n)}},is=class extends A{constructor(t,r,a,n){super(409,"The record attempted to be created already exists.","AlreadyExists",t,r,a,n)}},ps=class extends A{constructor(t,r,a,n){super(429,"The request has been rate limited.","RateLimited",t,r,a,n)}},us=class extends A{constructor(t,r,a,n){super(402,"A payment is required to perform this request.","PaymentRequired",t,r,a,n)}},cs=class extends A{constructor(t,r,a,n){super(403,"The request exceeds the allowed quota. Quotas are a soft limit that can be increased.","QuotaExceeded",t,r,a,n)}},ds=class extends A{constructor(t,r,a,n){super(413,"The request exceeds the allowed limit. Limits are a hard limit that cannot be increased.","LimitExceeded",t,r,a,n)}},ls=class extends A{constructor(t,r,a,n){super(400,"Request payload contains breaking changes which is not allowed for this resource without a version increment.","BreakingChanges",t,r,a,n)}},nx={Unknown:ue,Internal:Vt,Unauthorized:Nt,Forbidden:Kt,PayloadTooLarge:$t,InvalidPayload:jt,UnsupportedMediaType:zt,MethodNotFound:Jt,ResourceNotFound:Yt,InvalidJsonSchema:Zt,InvalidDataFormat:Xt,InvalidIdentifier:es,RelationConflict:ts,ReferenceConstraint:ss,ResourceLockedConflict:rs,ReferenceNotFound:as,InvalidQuery:ns,Runtime:os,AlreadyExists:is,RateLimited:ps,PaymentRequired:us,QuotaExceeded:cs,LimitExceeded:ds,BreakingChanges:ls},gs=e=>ax(e)?e:e instanceof Error?new ue(e.message,e):typeof e=="string"?new ue(e):ox(e);function ox(e){if(typeof e=="object"&&"code"in e&&"type"in e&&"id"in e&&"message"in e&&typeof e.type=="string"&&typeof e.message=="string"){let t=nx[e.type];return t?new t(e.message,void 0,e.id||"UNKNOWN",e.metadata):new ue(`An unclassified API error occurred: ${e.message} (Type: ${e.type}, Code: ${e.code})`)}return new ue("An invalid error occurred: "+JSON.stringify(e))}var ui=te(Ce()),vk=e=>e[1]!==void 0,m=e=>{let{method:t,path:r,query:a,headers:n,body:o}=e,i=Object.entries(n).filter(vk),u=Object.fromEntries(i),p=ui.default.stringify(a,{encode:!0,arrayFormat:"repeat",allowDots:!0}),s=p?[r,p].join("?"):r,y=["put","post","delete","patch"].includes(t.toLowerCase())?o:void 0;return{method:t,url:s,headers:u,data:y}};var ci=e=>({path:"/v1/chat/conversations",headers:{},query:{},params:{},body:{channel:e.channel,tags:e.tags,integrationName:e.integrationName}});var li=e=>({path:`/v1/chat/conversations/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var yi=e=>({path:"/v1/chat/conversations",headers:{},query:{nextToken:e.nextToken,sortField:e.sortField,sortDirection:e.sortDirection,tags:e.tags,participantIds:e.participantIds,integrationName:e.integrationName,channel:e.channel},params:{},body:{}});var mi=e=>({path:"/v1/chat/conversations/get-or-create",headers:{},query:{},params:{},body:{channel:e.channel,tags:e.tags,integrationName:e.integrationName,discriminateByTags:e.discriminateByTags}});var hi=e=>({path:`/v1/chat/conversations/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{currentTaskId:e.currentTaskId,tags:e.tags}});var fi=e=>({path:`/v1/chat/conversations/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var Ii=e=>({path:`/v1/chat/conversations/${encodeURIComponent(e.id)}/participants`,headers:{},query:{nextToken:e.nextToken},params:{id:e.id},body:{}});var Pi=e=>({path:`/v1/chat/conversations/${encodeURIComponent(e.id)}/participants`,headers:{},query:{},params:{id:e.id},body:{userId:e.userId}});var Ai=e=>({path:`/v1/chat/conversations/${encodeURIComponent(e.id)}/participants/${encodeURIComponent(e.userId)}`,headers:{},query:{},params:{id:e.id,userId:e.userId},body:{}});var Bi=e=>({path:`/v1/chat/conversations/${encodeURIComponent(e.id)}/participants/${encodeURIComponent(e.userId)}`,headers:{},query:{},params:{id:e.id,userId:e.userId},body:{}});var wi=e=>({path:"/v1/chat/events",headers:{},query:{},params:{},body:{type:e.type,payload:e.payload,schedule:e.schedule,conversationId:e.conversationId,userId:e.userId,messageId:e.messageId}});var Gi=e=>({path:`/v1/chat/events/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var Li=e=>({path:"/v1/chat/events",headers:{},query:{nextToken:e.nextToken,type:e.type,conversationId:e.conversationId,userId:e.userId,messageId:e.messageId,status:e.status},params:{},body:{}});var Si=e=>({path:"/v1/chat/messages",headers:{},query:{},params:{},body:{payload:e.payload,userId:e.userId,conversationId:e.conversationId,type:e.type,tags:e.tags,schedule:e.schedule}});var Hi=e=>({path:"/v1/chat/messages/get-or-create",headers:{},query:{},params:{},body:{payload:e.payload,userId:e.userId,conversationId:e.conversationId,type:e.type,tags:e.tags,schedule:e.schedule,discriminateByTags:e.discriminateByTags}});var _i=e=>({path:`/v1/chat/messages/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var Mi=e=>({path:`/v1/chat/messages/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{tags:e.tags,payload:e.payload}});var Vi=e=>({path:"/v1/chat/messages",headers:{},query:{nextToken:e.nextToken,conversationId:e.conversationId,tags:e.tags},params:{},body:{}});var Ki=e=>({path:`/v1/chat/messages/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var ji=e=>({path:"/v1/chat/users",headers:{},query:{},params:{},body:{tags:e.tags,integrationName:e.integrationName,name:e.name,pictureUrl:e.pictureUrl}});var Ji=e=>({path:`/v1/chat/users/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var Zi=e=>({path:"/v1/chat/users",headers:{},query:{nextToken:e.nextToken,conversationId:e.conversationId,tags:e.tags},params:{},body:{}});var ep=e=>({path:"/v1/chat/users/get-or-create",headers:{},query:{},params:{},body:{tags:e.tags,integrationName:e.integrationName,name:e.name,pictureUrl:e.pictureUrl,discriminateByTags:e.discriminateByTags}});var sp=e=>({path:`/v1/chat/users/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{tags:e.tags,name:e.name,pictureUrl:e.pictureUrl}});var ap=e=>({path:`/v1/chat/users/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var op=e=>({path:`/v1/chat/states/${encodeURIComponent(e.type)}/${encodeURIComponent(e.id)}/${encodeURIComponent(e.name)}/expiry`,headers:{},query:{},params:{type:e.type,id:e.id,name:e.name},body:{expiry:e.expiry}});var pp=e=>({path:`/v1/chat/states/${encodeURIComponent(e.type)}/${encodeURIComponent(e.id)}/${encodeURIComponent(e.name)}`,headers:{},query:{},params:{type:e.type,id:e.id,name:e.name},body:{}});var cp=e=>({path:`/v1/chat/states/${encodeURIComponent(e.type)}/${encodeURIComponent(e.id)}/${encodeURIComponent(e.name)}`,headers:{},query:{},params:{type:e.type,id:e.id,name:e.name},body:{payload:e.payload,expiry:e.expiry}});var lp=e=>({path:`/v1/chat/states/${encodeURIComponent(e.type)}/${encodeURIComponent(e.id)}/${encodeURIComponent(e.name)}/get-or-set`,headers:{},query:{},params:{type:e.type,id:e.id,name:e.name},body:{payload:e.payload,expiry:e.expiry}});var yp=e=>({path:`/v1/chat/states/${encodeURIComponent(e.type)}/${encodeURIComponent(e.id)}/${encodeURIComponent(e.name)}`,headers:{},query:{},params:{type:e.type,id:e.id,name:e.name},body:{payload:e.payload}});var mp=e=>({path:"/v1/chat/actions",headers:{},query:{},params:{},body:{type:e.type,input:e.input}});var hp=e=>({path:"/v1/chat/integrations/configure",headers:{},query:{},params:{},body:{identifier:e.identifier,scheduleRegisterCall:e.scheduleRegisterCall,sandboxIdentifiers:e.sandboxIdentifiers}});var fp=e=>({path:`/v1/chat/tasks/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var Ip=e=>({path:"/v1/chat/tasks",headers:{},query:{},params:{},body:{title:e.title,description:e.description,type:e.type,data:e.data,parentTaskId:e.parentTaskId,conversationId:e.conversationId,userId:e.userId,timeoutAt:e.timeoutAt,tags:e.tags}});var Pp=e=>({path:`/v1/chat/tasks/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{title:e.title,description:e.description,data:e.data,timeoutAt:e.timeoutAt,status:e.status,tags:e.tags}});var Ap=e=>({path:`/v1/chat/tasks/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var Bp=e=>({path:"/v1/chat/tasks",headers:{},query:{nextToken:e.nextToken,tags:e.tags,conversationId:e.conversationId,userId:e.userId,parentTaskId:e.parentTaskId,status:e.status,type:e.type},params:{},body:{}});var wp=e=>({path:"/v1/chat/workflows",headers:{},query:{},params:{},body:{name:e.name,input:e.input,parentWorkflowId:e.parentWorkflowId,conversationId:e.conversationId,userId:e.userId,timeoutAt:e.timeoutAt,tags:e.tags,status:e.status,eventId:e.eventId}});var Gp=e=>({path:`/v1/chat/workflows/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var Lp=e=>({path:`/v1/chat/workflows/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{output:e.output,timeoutAt:e.timeoutAt,status:e.status,failureReason:e.failureReason,tags:e.tags,userId:e.userId,eventId:e.eventId}});var Sp=e=>({path:`/v1/chat/workflows/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var Hp=e=>({path:"/v1/chat/workflows",headers:{},query:{nextToken:e.nextToken,tags:e.tags,conversationId:e.conversationId,userId:e.userId,parentWorkflowId:e.parentWorkflowId,statuses:e.statuses,name:e.name},params:{},body:{}});var _p=e=>({path:"/v1/chat/workflows/get-or-create",headers:{},query:{},params:{},body:{name:e.name,input:e.input,parentWorkflowId:e.parentWorkflowId,conversationId:e.conversationId,userId:e.userId,timeoutAt:e.timeoutAt,tags:e.tags,status:e.status,eventId:e.eventId}});var Mp=e=>({path:`/v1/chat/tags/${encodeURIComponent(e.key)}/values`,headers:{},query:{nextToken:e.nextToken,type:e.type},params:{key:e.key},body:{}});var Vp=e=>({path:"/v1/chat/analytics",headers:{},query:{},params:{},body:{name:e.name,count:e.count}});var it=class{constructor(t,r={}){this.axiosInstance=t;this.props=r}createConversation=async t=>{let{path:r,headers:a,query:n,body:o}=ci(t),i=this.props.toAxiosRequest??m,u=this.props.toApiError??q,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getConversation=async t=>{let{path:r,headers:a,query:n,body:o}=li(t),i=this.props.toAxiosRequest??m,u=this.props.toApiError??q,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};listConversations=async t=>{let{path:r,headers:a,query:n,body:o}=yi(t),i=this.props.toAxiosRequest??m,u=this.props.toApiError??q,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getOrCreateConversation=async t=>{let{path:r,headers:a,query:n,body:o}=mi(t),i=this.props.toAxiosRequest??m,u=this.props.toApiError??q,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};updateConversation=async t=>{let{path:r,headers:a,query:n,body:o}=hi(t),i=this.props.toAxiosRequest??m,u=this.props.toApiError??q,p=i({method:"put",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};deleteConversation=async t=>{let{path:r,headers:a,query:n,body:o}=fi(t),i=this.props.toAxiosRequest??m,u=this.props.toApiError??q,p=i({method:"delete",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};listParticipants=async t=>{let{path:r,headers:a,query:n,body:o}=Ii(t),i=this.props.toAxiosRequest??m,u=this.props.toApiError??q,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};addParticipant=async t=>{let{path:r,headers:a,query:n,body:o}=Pi(t),i=this.props.toAxiosRequest??m,u=this.props.toApiError??q,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getParticipant=async t=>{let{path:r,headers:a,query:n,body:o}=Ai(t),i=this.props.toAxiosRequest??m,u=this.props.toApiError??q,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};removeParticipant=async t=>{let{path:r,headers:a,query:n,body:o}=Bi(t),i=this.props.toAxiosRequest??m,u=this.props.toApiError??q,p=i({method:"delete",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};createEvent=async t=>{let{path:r,headers:a,query:n,body:o}=wi(t),i=this.props.toAxiosRequest??m,u=this.props.toApiError??q,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getEvent=async t=>{let{path:r,headers:a,query:n,body:o}=Gi(t),i=this.props.toAxiosRequest??m,u=this.props.toApiError??q,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};listEvents=async t=>{let{path:r,headers:a,query:n,body:o}=Li(t),i=this.props.toAxiosRequest??m,u=this.props.toApiError??q,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};createMessage=async t=>{let{path:r,headers:a,query:n,body:o}=Si(t),i=this.props.toAxiosRequest??m,u=this.props.toApiError??q,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getOrCreateMessage=async t=>{let{path:r,headers:a,query:n,body:o}=Hi(t),i=this.props.toAxiosRequest??m,u=this.props.toApiError??q,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getMessage=async t=>{let{path:r,headers:a,query:n,body:o}=_i(t),i=this.props.toAxiosRequest??m,u=this.props.toApiError??q,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};updateMessage=async t=>{let{path:r,headers:a,query:n,body:o}=Mi(t),i=this.props.toAxiosRequest??m,u=this.props.toApiError??q,p=i({method:"put",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};listMessages=async t=>{let{path:r,headers:a,query:n,body:o}=Vi(t),i=this.props.toAxiosRequest??m,u=this.props.toApiError??q,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};deleteMessage=async t=>{let{path:r,headers:a,query:n,body:o}=Ki(t),i=this.props.toAxiosRequest??m,u=this.props.toApiError??q,p=i({method:"delete",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};createUser=async t=>{let{path:r,headers:a,query:n,body:o}=ji(t),i=this.props.toAxiosRequest??m,u=this.props.toApiError??q,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getUser=async t=>{let{path:r,headers:a,query:n,body:o}=Ji(t),i=this.props.toAxiosRequest??m,u=this.props.toApiError??q,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};listUsers=async t=>{let{path:r,headers:a,query:n,body:o}=Zi(t),i=this.props.toAxiosRequest??m,u=this.props.toApiError??q,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getOrCreateUser=async t=>{let{path:r,headers:a,query:n,body:o}=ep(t),i=this.props.toAxiosRequest??m,u=this.props.toApiError??q,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};updateUser=async t=>{let{path:r,headers:a,query:n,body:o}=sp(t),i=this.props.toAxiosRequest??m,u=this.props.toApiError??q,p=i({method:"put",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};deleteUser=async t=>{let{path:r,headers:a,query:n,body:o}=ap(t),i=this.props.toAxiosRequest??m,u=this.props.toApiError??q,p=i({method:"delete",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};setStateExpiry=async t=>{let{path:r,headers:a,query:n,body:o}=op(t),i=this.props.toAxiosRequest??m,u=this.props.toApiError??q,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getState=async t=>{let{path:r,headers:a,query:n,body:o}=pp(t),i=this.props.toAxiosRequest??m,u=this.props.toApiError??q,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};setState=async t=>{let{path:r,headers:a,query:n,body:o}=cp(t),i=this.props.toAxiosRequest??m,u=this.props.toApiError??q,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getOrSetState=async t=>{let{path:r,headers:a,query:n,body:o}=lp(t),i=this.props.toAxiosRequest??m,u=this.props.toApiError??q,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};patchState=async t=>{let{path:r,headers:a,query:n,body:o}=yp(t),i=this.props.toAxiosRequest??m,u=this.props.toApiError??q,p=i({method:"patch",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};callAction=async t=>{let{path:r,headers:a,query:n,body:o}=mp(t),i=this.props.toAxiosRequest??m,u=this.props.toApiError??q,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};configureIntegration=async t=>{let{path:r,headers:a,query:n,body:o}=hp(t),i=this.props.toAxiosRequest??m,u=this.props.toApiError??q,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getTask=async t=>{let{path:r,headers:a,query:n,body:o}=fp(t),i=this.props.toAxiosRequest??m,u=this.props.toApiError??q,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};createTask=async t=>{let{path:r,headers:a,query:n,body:o}=Ip(t),i=this.props.toAxiosRequest??m,u=this.props.toApiError??q,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};updateTask=async t=>{let{path:r,headers:a,query:n,body:o}=Pp(t),i=this.props.toAxiosRequest??m,u=this.props.toApiError??q,p=i({method:"put",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};deleteTask=async t=>{let{path:r,headers:a,query:n,body:o}=Ap(t),i=this.props.toAxiosRequest??m,u=this.props.toApiError??q,p=i({method:"delete",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};listTasks=async t=>{let{path:r,headers:a,query:n,body:o}=Bp(t),i=this.props.toAxiosRequest??m,u=this.props.toApiError??q,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};createWorkflow=async t=>{let{path:r,headers:a,query:n,body:o}=wp(t),i=this.props.toAxiosRequest??m,u=this.props.toApiError??q,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getWorkflow=async t=>{let{path:r,headers:a,query:n,body:o}=Gp(t),i=this.props.toAxiosRequest??m,u=this.props.toApiError??q,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};updateWorkflow=async t=>{let{path:r,headers:a,query:n,body:o}=Lp(t),i=this.props.toAxiosRequest??m,u=this.props.toApiError??q,p=i({method:"put",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};deleteWorkflow=async t=>{let{path:r,headers:a,query:n,body:o}=Sp(t),i=this.props.toAxiosRequest??m,u=this.props.toApiError??q,p=i({method:"delete",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};listWorkflows=async t=>{let{path:r,headers:a,query:n,body:o}=Hp(t),i=this.props.toAxiosRequest??m,u=this.props.toApiError??q,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getOrCreateWorkflow=async t=>{let{path:r,headers:a,query:n,body:o}=_p(t),i=this.props.toAxiosRequest??m,u=this.props.toApiError??q,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};listTagValues=async t=>{let{path:r,headers:a,query:n,body:o}=Mp(t),i=this.props.toAxiosRequest??m,u=this.props.toApiError??q,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};trackAnalytics=async t=>{let{path:r,headers:a,query:n,body:o}=Vp(t),i=this.props.toAxiosRequest??m,u=this.props.toApiError??q,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})}};function q(e){return Ak.isAxiosError(e)&&e.response?.data?gs(e.response.data):gs(e)}var Vs=class extends it{config;constructor(t){let r=_.getClientConfig(t),a=F.createAxios(r),n=Bk.create(a);super(n,{toApiError:M.toApiError}),t.retry&&O(n,t.retry),this.config=r}get list(){return{conversations:t=>new h.AsyncCollection(({nextToken:r})=>this.listConversations({nextToken:r,...t}).then(a=>({...a,items:a.conversations}))),participants:t=>new h.AsyncCollection(({nextToken:r})=>this.listParticipants({nextToken:r,...t}).then(a=>({...a,items:a.participants}))),events:t=>new h.AsyncCollection(({nextToken:r})=>this.listEvents({nextToken:r,...t}).then(a=>({...a,items:a.events}))),messages:t=>new h.AsyncCollection(({nextToken:r})=>this.listMessages({nextToken:r,...t}).then(a=>({...a,items:a.messages}))),users:t=>new h.AsyncCollection(({nextToken:r})=>this.listUsers({nextToken:r,...t}).then(a=>({...a,items:a.users}))),tasks:t=>new h.AsyncCollection(({nextToken:r})=>this.listTasks({nextToken:r,...t}).then(a=>({...a,items:a.tasks})))}}};var Bl={};z(Bl,{Client:()=>mr});import Hk from"axios";import Sk from"axios";import Ck from"crypto";var wk={getRandomValues:e=>new Uint8Array(e.map(()=>Math.floor(Math.random()*256)))},Ns=typeof window<"u"&&typeof window.document<"u"?window.crypto:Ck;Ns.getRandomValues||(Ns=wk);var T=class extends Error{constructor(r,a,n,o,i,u,p){super(o);this.code=r;this.description=a;this.type=n;this.message=o;this.error=i;this.id=u;this.metadata=p;this.id||(this.id=T.generateId())}isApiError=!0;format(){return`[${this.type}] ${this.message} (Error ID: ${this.id})`}toJSON(){return{id:this.id,code:this.code,type:this.type,message:this.message,metadata:this.metadata}}static generateId(){let r=this.getPrefix(),a=new Date().toISOString().replace(/[\-:TZ]/g,"").split(".")[0],n=4,o=Array.from(Ns.getRandomValues(new Uint8Array(n))).map(i=>i.toString(16).padStart(2,"0")).join("").toUpperCase();return`${r}_${a}x${o}`}static getPrefix(){return typeof window<"u"&&typeof window.document<"u"?"err_bwsr":"err"}},Uk=e=>typeof e=="object"&&!Array.isArray(e)&&e!==null,Gk=e=>e instanceof T||Uk(e)&&e.isApiError===!0,le=class extends T{constructor(t,r,a,n){super(500,"An unknown error occurred","Unknown",t,r,a,n)}},Ks=class extends T{constructor(t,r,a,n){super(500,"An internal error occurred","Internal",t,r,a,n)}},$s=class extends T{constructor(t,r,a,n){super(401,"The request requires to be authenticated.","Unauthorized",t,r,a,n)}},js=class extends T{constructor(t,r,a,n){super(403,"The requested action can't be peform by this resource.","Forbidden",t,r,a,n)}},zs=class extends T{constructor(t,r,a,n){super(413,"The request payload is too large.","PayloadTooLarge",t,r,a,n)}},Js=class extends T{constructor(t,r,a,n){super(400,"The request payload is invalid.","InvalidPayload",t,r,a,n)}},Ys=class extends T{constructor(t,r,a,n){super(415,"The request is invalid because the content-type is not supported.","UnsupportedMediaType",t,r,a,n)}},Zs=class extends T{constructor(t,r,a,n){super(405,"The requested method does not exist.","MethodNotFound",t,r,a,n)}},Xs=class extends T{constructor(t,r,a,n){super(404,"The requested resource does not exist.","ResourceNotFound",t,r,a,n)}},er=class extends T{constructor(t,r,a,n){super(400,"The provided JSON schema is invalid.","InvalidJsonSchema",t,r,a,n)}},tr=class extends T{constructor(t,r,a,n){super(400,"The provided data doesn't respect the provided JSON schema.","InvalidDataFormat",t,r,a,n)}},sr=class extends T{constructor(t,r,a,n){super(400,"The provided identifier is not valid. An identifier must start with a lowercase letter, be between 2 and 100 characters long and use only alphanumeric characters.","InvalidIdentifier",t,r,a,n)}},rr=class extends T{constructor(t,r,a,n){super(409,"The resource is related with a different resource that the one referenced in the request. This is usually caused when providing two resource identifiers that aren't linked together.","RelationConflict",t,r,a,n)}},ar=class extends T{constructor(t,r,a,n){super(409,"The resource cannot be deleted because it's referenced by another resource","ReferenceConstraint",t,r,a,n)}},nr=class extends T{constructor(t,r,a,n){super(409,"The resource is current locked and cannot be operated on until the lock is released.","ResourceLockedConflict",t,r,a,n)}},or=class extends T{constructor(t,r,a,n){super(400,"The provided resource reference is missing. This is usually caused when providing an invalid id inside the payload of a request.","ReferenceNotFound",t,r,a,n)}},ir=class extends T{constructor(t,r,a,n){super(400,"The provided query is invalid. This is usually caused when providing an invalid parameter for querying a resource.","InvalidQuery",t,r,a,n)}},pr=class extends T{constructor(t,r,a,n){super(400,"An error happened during the execution of a runtime (bot or integration).","Runtime",t,r,a,n)}},ur=class extends T{constructor(t,r,a,n){super(409,"The record attempted to be created already exists.","AlreadyExists",t,r,a,n)}},cr=class extends T{constructor(t,r,a,n){super(429,"The request has been rate limited.","RateLimited",t,r,a,n)}},dr=class extends T{constructor(t,r,a,n){super(402,"A payment is required to perform this request.","PaymentRequired",t,r,a,n)}},lr=class extends T{constructor(t,r,a,n){super(403,"The request exceeds the allowed quota. Quotas are a soft limit that can be increased.","QuotaExceeded",t,r,a,n)}},gr=class extends T{constructor(t,r,a,n){super(413,"The request exceeds the allowed limit. Limits are a hard limit that cannot be increased.","LimitExceeded",t,r,a,n)}},yr=class extends T{constructor(t,r,a,n){super(400,"Request payload contains breaking changes which is not allowed for this resource without a version increment.","BreakingChanges",t,r,a,n)}},Ek={Unknown:le,Internal:Ks,Unauthorized:$s,Forbidden:js,PayloadTooLarge:zs,InvalidPayload:Js,UnsupportedMediaType:Ys,MethodNotFound:Zs,ResourceNotFound:Xs,InvalidJsonSchema:er,InvalidDataFormat:tr,InvalidIdentifier:sr,RelationConflict:rr,ReferenceConstraint:ar,ResourceLockedConflict:nr,ReferenceNotFound:or,InvalidQuery:ir,Runtime:pr,AlreadyExists:ur,RateLimited:cr,PaymentRequired:dr,QuotaExceeded:lr,LimitExceeded:gr,BreakingChanges:yr},Rr=e=>Gk(e)?e:e instanceof Error?new le(e.message,e):typeof e=="string"?new le(e):Lk(e);function Lk(e){if(typeof e=="object"&&"code"in e&&"type"in e&&"id"in e&&"message"in e&&typeof e.type=="string"&&typeof e.message=="string"){let t=Ek[e.type];return t?new t(e.message,void 0,e.id||"UNKNOWN",e.metadata):new le(`An unclassified API error occurred: ${e.message} (Type: ${e.type}, Code: ${e.code})`)}return new le("An invalid error occurred: "+JSON.stringify(e))}var $p=te(Ce()),Wk=e=>e[1]!==void 0,l=e=>{let{method:t,path:r,query:a,headers:n,body:o}=e,i=Object.entries(n).filter(Wk),u=Object.fromEntries(i),p=$p.default.stringify(a,{encode:!0,arrayFormat:"repeat",allowDots:!0}),s=p?[r,p].join("?"):r,y=["put","post","delete","patch"].includes(t.toLowerCase())?o:void 0;return{method:t,url:s,headers:u,data:y}};var jp=e=>({path:"/v1/admin/helper/vrl",headers:{},query:{},params:{},body:{data:e.data,script:e.script}});var Jp=e=>({path:"/v1/admin/account/me",headers:{},query:{},params:{},body:{}});var Zp=e=>({path:"/v1/admin/account/me",headers:{},query:{},params:{},body:{displayName:e.displayName,profilePicture:e.profilePicture,refresh:e.refresh}});var eu=e=>({path:"/v1/admin/account/pats",headers:{},query:{},params:{},body:{}});var su=e=>({path:"/v1/admin/account/pats",headers:{},query:{},params:{},body:{note:e.note}});var au=e=>({path:`/v1/admin/account/pats/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var ou=e=>({path:`/v1/admin/account/preferences/${encodeURIComponent(e.key)}`,headers:{},query:{},params:{key:e.key},body:{value:e.value}});var pu=e=>({path:`/v1/admin/account/preferences/${encodeURIComponent(e.key)}`,headers:{},query:{},params:{key:e.key},body:{}});var cu=e=>({path:"/v1/admin/hub/integrations",headers:{},query:{nextToken:e.nextToken,limit:e.limit,name:e.name,version:e.version,interfaceId:e.interfaceId,interfaceName:e.interfaceName,installedByBotId:e.installedByBotId,verificationStatus:e.verificationStatus,search:e.search,sortBy:e.sortBy,direction:e.direction},params:{},body:{}});var lu=e=>({path:`/v1/admin/hub/integrations/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var yu=e=>({path:`/v1/admin/hub/integrations/${encodeURIComponent(e.name)}/${encodeURIComponent(e.version)}`,headers:{},query:{},params:{name:e.name,version:e.version},body:{}});var mu=e=>({path:"/v1/admin/hub/plugins",headers:{},query:{nextToken:e.nextToken,name:e.name,version:e.version},params:{},body:{}});var hu=e=>({path:`/v1/admin/hub/plugins/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var fu=e=>({path:`/v1/admin/hub/plugins/${encodeURIComponent(e.name)}/${encodeURIComponent(e.version)}`,headers:{},query:{},params:{name:e.name,version:e.version},body:{}});var Iu=e=>({path:`/v1/admin/hub/plugins/${encodeURIComponent(e.id)}/code/${encodeURIComponent(e.platform)}`,headers:{},query:{},params:{id:e.id,platform:e.platform},body:{}});var Pu=e=>({path:"/v1/admin/hub/interfaces",headers:{},query:{nextToken:e.nextToken,name:e.name,version:e.version},params:{},body:{}});var Au=e=>({path:`/v1/admin/hub/interfaces/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var Bu=e=>({path:`/v1/admin/hub/interfaces/${encodeURIComponent(e.name)}/${encodeURIComponent(e.version)}`,headers:{},query:{},params:{name:e.name,version:e.version},body:{}});var wu=e=>({path:"/v1/admin/bots",headers:{},query:{},params:{},body:{states:e.states,events:e.events,recurringEvents:e.recurringEvents,subscriptions:e.subscriptions,actions:e.actions,configuration:e.configuration,user:e.user,conversation:e.conversation,message:e.message,tags:e.tags,code:e.code,name:e.name,medias:e.medias,url:e.url,dev:e.dev}});var Gu=e=>({path:`/v1/admin/bots/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{url:e.url,authentication:e.authentication,configuration:e.configuration,tags:e.tags,blocked:e.blocked,alwaysAlive:e.alwaysAlive,user:e.user,message:e.message,conversation:e.conversation,events:e.events,actions:e.actions,states:e.states,recurringEvents:e.recurringEvents,integrations:e.integrations,plugins:e.plugins,subscriptions:e.subscriptions,code:e.code,name:e.name,medias:e.medias,layers:e.layers}});var Lu=e=>({path:`/v1/admin/bots/${encodeURIComponent(e.id)}/transfer`,headers:{},query:{},params:{id:e.id},body:{targetWorkspaceId:e.targetWorkspaceId}});var Su=e=>({path:"/v1/admin/bots",headers:{},query:{dev:e.dev,tags:e.tags,nextToken:e.nextToken,sortField:e.sortField,sortDirection:e.sortDirection},params:{},body:{}});var Hu=e=>({path:`/v1/admin/bots/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var _u=e=>({path:`/v1/admin/bots/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var Mu=e=>({path:`/v1/admin/bots/${encodeURIComponent(e.id)}/logs`,headers:{},query:{timeStart:e.timeStart,timeEnd:e.timeEnd,level:e.level,userId:e.userId,workflowId:e.workflowId,conversationId:e.conversationId,messageContains:e.messageContains,nextToken:e.nextToken},params:{id:e.id},body:{}});var Vu=e=>({path:`/v1/admin/bots/${encodeURIComponent(e.id)}/webchat`,headers:{},query:{type:e.type},params:{id:e.id},body:{}});var Ku=e=>({path:`/v1/admin/bots/${encodeURIComponent(e.id)}/analytics`,headers:{},query:{startDate:e.startDate,endDate:e.endDate},params:{id:e.id},body:{}});var ju=e=>({path:`/v1/admin/bots/${encodeURIComponent(e.id)}/issues/${encodeURIComponent(e.issueId)}`,headers:{},query:{},params:{id:e.id,issueId:e.issueId},body:{}});var Ju=e=>({path:`/v1/admin/bots/${encodeURIComponent(e.id)}/issues`,headers:{},query:{nextToken:e.nextToken},params:{id:e.id},body:{}});var Zu=e=>({path:`/v1/admin/bots/${encodeURIComponent(e.id)}/issues/${encodeURIComponent(e.issueId)}`,headers:{},query:{},params:{id:e.id,issueId:e.issueId},body:{}});var ec=e=>({path:`/v1/admin/bots/${encodeURIComponent(e.id)}/issues/${encodeURIComponent(e.issueId)}/events`,headers:{},query:{},params:{id:e.id,issueId:e.issueId},body:{}});var sc=e=>({path:`/v1/admin/bots/${encodeURIComponent(e.id)}/versions`,headers:{},query:{},params:{id:e.id},body:{}});var ac=e=>({path:`/v1/admin/bots/${encodeURIComponent(e.id)}/versions/${encodeURIComponent(e.versionId)}`,headers:{},query:{},params:{id:e.id,versionId:e.versionId},body:{}});var oc=e=>({path:`/v1/admin/bots/${encodeURIComponent(e.id)}/versions`,headers:{},query:{},params:{id:e.id},body:{name:e.name,description:e.description}});var pc=e=>({path:`/v1/admin/bots/${encodeURIComponent(e.id)}/versions/deploy`,headers:{},query:{},params:{id:e.id},body:{versionId:e.versionId}});var cc=e=>({path:`/v1/admin/bots/${encodeURIComponent(e.botId)}/integrations/${encodeURIComponent(e.integrationId)}/shareable-id`,headers:{},query:{},params:{botId:e.botId,integrationId:e.integrationId},body:{}});var lc=e=>({path:`/v1/admin/bots/${encodeURIComponent(e.botId)}/integrations/${encodeURIComponent(e.integrationId)}/shareable-id`,headers:{},query:{},params:{botId:e.botId,integrationId:e.integrationId},body:{}});var yc=e=>({path:`/v1/admin/bots/${encodeURIComponent(e.botId)}/integrations/${encodeURIComponent(e.integrationId)}/shareable-id`,headers:{},query:{},params:{botId:e.botId,integrationId:e.integrationId},body:{}});var mc=e=>({path:`/v1/admin/bots/${encodeURIComponent(e.botId)}/integrations/${encodeURIComponent(e.integrationId)}/sandboxed-conversations`,headers:{},query:{},params:{botId:e.botId,integrationId:e.integrationId},body:{}});var hc=e=>({path:"/v1/admin/bots/baks",headers:{},query:{botId:e.botId},params:{},body:{}});var fc=e=>({path:"/v1/admin/bots/baks",headers:{},query:{},params:{},body:{botId:e.botId,note:e.note}});var Ic=e=>({path:`/v1/admin/bots/baks/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var Pc=e=>({path:`/v1/admin/workspaces/${encodeURIComponent(e.id)}/billing/invoices`,headers:{},query:{},params:{id:e.id},body:{}});var Ac=e=>({path:`/v1/admin/workspaces/${encodeURIComponent(e.id)}/billing/upcoming-invoice`,headers:{},query:{},params:{id:e.id},body:{}});var Bc=e=>({path:`/v1/admin/workspaces/${encodeURIComponent(e.id)}/billing/invoices/charge-unpaid`,headers:{},query:{},params:{id:e.id},body:{invoiceIds:e.invoiceIds}});var wc=e=>({path:"/v1/admin/workspaces",headers:{},query:{},params:{},body:{name:e.name}});var Gc=e=>({path:`/v1/admin/workspaces/${encodeURIComponent(e.id)}/public`,headers:{},query:{},params:{id:e.id},body:{}});var Lc=e=>({path:`/v1/admin/workspaces/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var Sc=e=>({path:`/v1/admin/workspaces/${encodeURIComponent(e.id)}/usages`,headers:{},query:{type:e.type,period:e.period},params:{id:e.id},body:{}});var Hc=e=>({path:`/v1/admin/workspaces/${encodeURIComponent(e.id)}/usages/by-bot`,headers:{},query:{type:e.type,period:e.period},params:{id:e.id},body:{}});var _c=e=>({path:"/v1/admin/workspaces/usages/quota-completion",headers:{},query:{},params:{},body:{}});var Mc=e=>({path:`/v1/admin/workspaces/${encodeURIComponent(e.id)}/quota`,headers:{},query:{type:e.type,period:e.period},params:{id:e.id},body:{}});var Vc=e=>({path:`/v1/admin/workspaces/${encodeURIComponent(e.id)}/quotas`,headers:{},query:{period:e.period},params:{id:e.id},body:{}});var Kc=e=>({path:`/v1/admin/workspaces/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{name:e.name,spendingLimit:e.spendingLimit,about:e.about,profilePicture:e.profilePicture,contactEmail:e.contactEmail,website:e.website,socialAccounts:e.socialAccounts,isPublic:e.isPublic,handle:e.handle}});var jc=e=>({path:"/v1/admin/workspaces/handle-availability",headers:{},query:{},params:{},body:{handle:e.handle}});var Jc=e=>({path:"/v1/admin/workspaces",headers:{},query:{nextToken:e.nextToken,handle:e.handle},params:{},body:{}});var Zc=e=>({path:"/v1/admin/workspaces/public",headers:{},query:{nextToken:e.nextToken,workspaceIds:e.workspaceIds,search:e.search},params:{},body:{}});var ed=e=>({path:`/v1/admin/workspaces/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var sd=e=>({path:`/v1/admin/workspaces/${encodeURIComponent(e.id)}/audit-records`,headers:{},query:{nextToken:e.nextToken},params:{id:e.id},body:{}});var ad=e=>({path:"/v1/admin/workspace-members",headers:{},query:{nextToken:e.nextToken},params:{},body:{}});var od=e=>({path:"/v1/admin/workspace-members/me",headers:{},query:{},params:{},body:{}});var pd=e=>({path:`/v1/admin/workspace-members/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var cd=e=>({path:"/v1/admin/workspace-members",headers:{},query:{},params:{},body:{email:e.email,role:e.role}});var ld=e=>({path:`/v1/admin/workspace-members/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{role:e.role}});var yd=e=>({path:"/v1/admin/integrations/iaks",headers:{},query:{integrationId:e.integrationId},params:{},body:{}});var md=e=>({path:"/v1/admin/integrations/iaks",headers:{},query:{},params:{},body:{integrationId:e.integrationId,note:e.note}});var hd=e=>({path:`/v1/admin/integrations/iaks/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var fd=e=>({path:"/v1/admin/integrations",headers:{},query:{},params:{},body:{name:e.name,version:e.version,configuration:e.configuration,configurations:e.configurations,states:e.states,events:e.events,actions:e.actions,entities:e.entities,attributes:e.attributes,identifier:e.identifier,channels:e.channels,user:e.user,interfaces:e.interfaces,secrets:e.secrets,code:e.code,url:e.url,dev:e.dev,icon:e.icon,readme:e.readme,title:e.title,description:e.description,public:e.public,layers:e.layers}});var Id=e=>({path:"/v1/admin/integrations/validate",headers:{},query:{},params:{},body:{name:e.name,version:e.version,configuration:e.configuration,configurations:e.configurations,states:e.states,events:e.events,actions:e.actions,entities:e.entities,attributes:e.attributes,identifier:e.identifier,channels:e.channels,user:e.user,interfaces:e.interfaces,secrets:e.secrets,code:e.code,url:e.url,dev:e.dev,icon:e.icon,readme:e.readme,title:e.title,description:e.description,public:e.public,layers:e.layers}});var Pd=e=>({path:`/v1/admin/integrations/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{configuration:e.configuration,configurations:e.configurations,channels:e.channels,identifier:e.identifier,actions:e.actions,events:e.events,states:e.states,user:e.user,entities:e.entities,interfaces:e.interfaces,sandbox:e.sandbox,attributes:e.attributes,secrets:e.secrets,code:e.code,icon:e.icon,readme:e.readme,title:e.title,description:e.description,url:e.url,public:e.public,layers:e.layers}});var Ad=e=>({path:`/v1/admin/integrations/${encodeURIComponent(e.id)}/validate`,headers:{},query:{},params:{id:e.id},body:{configuration:e.configuration,configurations:e.configurations,channels:e.channels,identifier:e.identifier,actions:e.actions,events:e.events,states:e.states,user:e.user,entities:e.entities,interfaces:e.interfaces,sandbox:e.sandbox,attributes:e.attributes,secrets:e.secrets,code:e.code,icon:e.icon,readme:e.readme,title:e.title,description:e.description,url:e.url,public:e.public,layers:e.layers}});var Bd=e=>({path:"/v1/admin/integrations",headers:{},query:{nextToken:e.nextToken,limit:e.limit,name:e.name,version:e.version,interfaceId:e.interfaceId,interfaceName:e.interfaceName,installedByBotId:e.installedByBotId,verificationStatus:e.verificationStatus,search:e.search,sortBy:e.sortBy,direction:e.direction,visibility:e.visibility,dev:e.dev},params:{},body:{}});var wd=e=>({path:`/v1/admin/integrations/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var Gd=e=>({path:`/v1/admin/integrations/${encodeURIComponent(e.id)}/logs`,headers:{},query:{timeStart:e.timeStart,timeEnd:e.timeEnd,level:e.level,userId:e.userId,conversationId:e.conversationId,messageContains:e.messageContains,nextToken:e.nextToken},params:{id:e.id},body:{}});var Ld=e=>({path:`/v1/admin/integrations/${encodeURIComponent(e.name)}/${encodeURIComponent(e.version)}`,headers:{},query:{},params:{name:e.name,version:e.version},body:{}});var Sd=e=>({path:`/v1/admin/integrations/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var Hd=e=>({path:"/v1/admin/integrations/request-verification",headers:{},query:{},params:{},body:{integrationId:e.integrationId}});var _d=e=>({path:"/v1/admin/interfaces",headers:{},query:{},params:{},body:{name:e.name,version:e.version,entities:e.entities,events:e.events,actions:e.actions,channels:e.channels,nameTemplate:e.nameTemplate,attributes:e.attributes,icon:e.icon,readme:e.readme,title:e.title,description:e.description,public:e.public}});var Md=e=>({path:`/v1/admin/interfaces/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var Vd=e=>({path:`/v1/admin/interfaces/${encodeURIComponent(e.name)}/${encodeURIComponent(e.version)}`,headers:{},query:{},params:{name:e.name,version:e.version},body:{}});var Kd=e=>({path:`/v1/admin/interfaces/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{entities:e.entities,events:e.events,actions:e.actions,channels:e.channels,nameTemplate:e.nameTemplate,attributes:e.attributes,icon:e.icon,readme:e.readme,title:e.title,description:e.description,public:e.public}});var jd=e=>({path:`/v1/admin/interfaces/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var Jd=e=>({path:"/v1/admin/interfaces",headers:{},query:{nextToken:e.nextToken,name:e.name,version:e.version},params:{},body:{}});var Zd=e=>({path:"/v1/admin/plugins",headers:{},query:{},params:{},body:{name:e.name,version:e.version,configuration:e.configuration,states:e.states,events:e.events,actions:e.actions,user:e.user,conversation:e.conversation,dependencies:e.dependencies,attributes:e.attributes,code:e.code,icon:e.icon,readme:e.readme,title:e.title,description:e.description,public:e.public}});var el=e=>({path:`/v1/admin/plugins/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var sl=e=>({path:`/v1/admin/plugins/${encodeURIComponent(e.name)}/${encodeURIComponent(e.version)}`,headers:{},query:{},params:{name:e.name,version:e.version},body:{}});var al=e=>({path:`/v1/admin/plugins/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{configuration:e.configuration,states:e.states,events:e.events,actions:e.actions,user:e.user,conversation:e.conversation,dependencies:e.dependencies,attributes:e.attributes,code:e.code,icon:e.icon,readme:e.readme,title:e.title,description:e.description,public:e.public}});var ol=e=>({path:`/v1/admin/plugins/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var pl=e=>({path:"/v1/admin/plugins",headers:{},query:{nextToken:e.nextToken,name:e.name,version:e.version},params:{},body:{}});var cl=e=>({path:`/v1/admin/plugins/${encodeURIComponent(e.id)}/code/${encodeURIComponent(e.platform)}`,headers:{},query:{},params:{id:e.id,platform:e.platform},body:{}});var ll=e=>({path:`/v1/admin/usages/${encodeURIComponent(e.id)}`,headers:{},query:{type:e.type,period:e.period},params:{id:e.id},body:{}});var yl=e=>({path:"/v1/admin/usages/multiple",headers:{},query:{types:e.types,ids:e.ids,period:e.period},params:{},body:{}});var ml=e=>({path:`/v1/admin/usages/${encodeURIComponent(e.id)}/history`,headers:{},query:{type:e.type},params:{id:e.id},body:{}});var hl=e=>({path:`/v1/admin/usages/${encodeURIComponent(e.id)}/activity`,headers:{},query:{type:e.type,timestampFrom:e.timestampFrom,timestampUntil:e.timestampUntil,nextToken:e.nextToken},params:{id:e.id},body:{}});var fl=e=>({path:`/v1/admin/usages/${encodeURIComponent(e.id)}/daily-activity`,headers:{},query:{type:e.type,dateFrom:e.dateFrom,dateUntil:e.dateUntil,nextToken:e.nextToken},params:{id:e.id},body:{}});var Il=e=>({path:"/v1/admin/quotas/ai-spend",headers:{},query:{},params:{},body:{monthlySpendingLimit:e.monthlySpendingLimit}});var Pl=e=>({path:"/v1/admin/activities",headers:{},query:{nextToken:e.nextToken,taskId:e.taskId,botId:e.botId},params:{},body:{}});var Al=e=>({path:"/v1/admin/introspect",headers:{},query:{},params:{},body:{botId:e.botId}});var pt=class{constructor(t,r={}){this.axiosInstance=t;this.props=r}runVrl=async t=>{let{path:r,headers:a,query:n,body:o}=jp(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getAccount=async t=>{let{path:r,headers:a,query:n,body:o}=Jp(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};updateAccount=async t=>{let{path:r,headers:a,query:n,body:o}=Zp(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"put",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};listPersonalAccessTokens=async t=>{let{path:r,headers:a,query:n,body:o}=eu(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};createPersonalAccessToken=async t=>{let{path:r,headers:a,query:n,body:o}=su(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};deletePersonalAccessToken=async t=>{let{path:r,headers:a,query:n,body:o}=au(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"delete",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};setAccountPreference=async t=>{let{path:r,headers:a,query:n,body:o}=ou(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getAccountPreference=async t=>{let{path:r,headers:a,query:n,body:o}=pu(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};listPublicIntegrations=async t=>{let{path:r,headers:a,query:n,body:o}=cu(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getPublicIntegrationById=async t=>{let{path:r,headers:a,query:n,body:o}=lu(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getPublicIntegration=async t=>{let{path:r,headers:a,query:n,body:o}=yu(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};listPublicPlugins=async t=>{let{path:r,headers:a,query:n,body:o}=mu(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getPublicPluginById=async t=>{let{path:r,headers:a,query:n,body:o}=hu(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getPublicPlugin=async t=>{let{path:r,headers:a,query:n,body:o}=fu(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getPublicPluginCode=async t=>{let{path:r,headers:a,query:n,body:o}=Iu(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};listPublicInterfaces=async t=>{let{path:r,headers:a,query:n,body:o}=Pu(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getPublicInterfaceById=async t=>{let{path:r,headers:a,query:n,body:o}=Au(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getPublicInterface=async t=>{let{path:r,headers:a,query:n,body:o}=Bu(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};createBot=async t=>{let{path:r,headers:a,query:n,body:o}=wu(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};updateBot=async t=>{let{path:r,headers:a,query:n,body:o}=Gu(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"put",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};transferBot=async t=>{let{path:r,headers:a,query:n,body:o}=Lu(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};listBots=async t=>{let{path:r,headers:a,query:n,body:o}=Su(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getBot=async t=>{let{path:r,headers:a,query:n,body:o}=Hu(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};deleteBot=async t=>{let{path:r,headers:a,query:n,body:o}=_u(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"delete",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getBotLogs=async t=>{let{path:r,headers:a,query:n,body:o}=Mu(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getBotWebchat=async t=>{let{path:r,headers:a,query:n,body:o}=Vu(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getBotAnalytics=async t=>{let{path:r,headers:a,query:n,body:o}=Ku(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getBotIssue=async t=>{let{path:r,headers:a,query:n,body:o}=ju(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};listBotIssues=async t=>{let{path:r,headers:a,query:n,body:o}=Ju(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};deleteBotIssue=async t=>{let{path:r,headers:a,query:n,body:o}=Zu(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"delete",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};listBotIssueEvents=async t=>{let{path:r,headers:a,query:n,body:o}=ec(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};listBotVersions=async t=>{let{path:r,headers:a,query:n,body:o}=sc(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getBotVersion=async t=>{let{path:r,headers:a,query:n,body:o}=ac(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};createBotVersion=async t=>{let{path:r,headers:a,query:n,body:o}=oc(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};deployBotVersion=async t=>{let{path:r,headers:a,query:n,body:o}=pc(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};createIntegrationShareableId=async t=>{let{path:r,headers:a,query:n,body:o}=cc(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};deleteIntegrationShareableId=async t=>{let{path:r,headers:a,query:n,body:o}=lc(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"delete",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getIntegrationShareableId=async t=>{let{path:r,headers:a,query:n,body:o}=yc(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};unlinkSandboxedConversations=async t=>{let{path:r,headers:a,query:n,body:o}=mc(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"delete",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};listBotApiKeys=async t=>{let{path:r,headers:a,query:n,body:o}=hc(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};createBotApiKey=async t=>{let{path:r,headers:a,query:n,body:o}=fc(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};deleteBotApiKey=async t=>{let{path:r,headers:a,query:n,body:o}=Ic(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"delete",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};listWorkspaceInvoices=async t=>{let{path:r,headers:a,query:n,body:o}=Pc(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getUpcomingInvoice=async t=>{let{path:r,headers:a,query:n,body:o}=Ac(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};chargeWorkspaceUnpaidInvoices=async t=>{let{path:r,headers:a,query:n,body:o}=Bc(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};createWorkspace=async t=>{let{path:r,headers:a,query:n,body:o}=wc(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getPublicWorkspace=async t=>{let{path:r,headers:a,query:n,body:o}=Gc(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getWorkspace=async t=>{let{path:r,headers:a,query:n,body:o}=Lc(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};listWorkspaceUsages=async t=>{let{path:r,headers:a,query:n,body:o}=Sc(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};breakDownWorkspaceUsageByBot=async t=>{let{path:r,headers:a,query:n,body:o}=Hc(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getAllWorkspaceQuotaCompletion=async t=>{let{path:r,headers:a,query:n,body:o}=_c(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getWorkspaceQuota=async t=>{let{path:r,headers:a,query:n,body:o}=Mc(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};listWorkspaceQuotas=async t=>{let{path:r,headers:a,query:n,body:o}=Vc(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};updateWorkspace=async t=>{let{path:r,headers:a,query:n,body:o}=Kc(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"put",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};checkHandleAvailability=async t=>{let{path:r,headers:a,query:n,body:o}=jc(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"put",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};listWorkspaces=async t=>{let{path:r,headers:a,query:n,body:o}=Jc(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};listPublicWorkspaces=async t=>{let{path:r,headers:a,query:n,body:o}=Zc(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};deleteWorkspace=async t=>{let{path:r,headers:a,query:n,body:o}=ed(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"delete",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getAuditRecords=async t=>{let{path:r,headers:a,query:n,body:o}=sd(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};listWorkspaceMembers=async t=>{let{path:r,headers:a,query:n,body:o}=ad(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getWorkspaceMember=async t=>{let{path:r,headers:a,query:n,body:o}=od(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};deleteWorkspaceMember=async t=>{let{path:r,headers:a,query:n,body:o}=pd(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"delete",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};createWorkspaceMember=async t=>{let{path:r,headers:a,query:n,body:o}=cd(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};updateWorkspaceMember=async t=>{let{path:r,headers:a,query:n,body:o}=ld(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"put",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};listIntegrationApiKeys=async t=>{let{path:r,headers:a,query:n,body:o}=yd(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};createIntegrationApiKey=async t=>{let{path:r,headers:a,query:n,body:o}=md(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};deleteIntegrationApiKey=async t=>{let{path:r,headers:a,query:n,body:o}=hd(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"delete",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};createIntegration=async t=>{let{path:r,headers:a,query:n,body:o}=fd(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};validateIntegrationCreation=async t=>{let{path:r,headers:a,query:n,body:o}=Id(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};updateIntegration=async t=>{let{path:r,headers:a,query:n,body:o}=Pd(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"put",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};validateIntegrationUpdate=async t=>{let{path:r,headers:a,query:n,body:o}=Ad(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"put",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};listIntegrations=async t=>{let{path:r,headers:a,query:n,body:o}=Bd(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getIntegration=async t=>{let{path:r,headers:a,query:n,body:o}=wd(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getIntegrationLogs=async t=>{let{path:r,headers:a,query:n,body:o}=Gd(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getIntegrationByName=async t=>{let{path:r,headers:a,query:n,body:o}=Ld(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};deleteIntegration=async t=>{let{path:r,headers:a,query:n,body:o}=Sd(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"delete",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};requestIntegrationVerification=async t=>{let{path:r,headers:a,query:n,body:o}=Hd(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};createInterface=async t=>{let{path:r,headers:a,query:n,body:o}=_d(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getInterface=async t=>{let{path:r,headers:a,query:n,body:o}=Md(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getInterfaceByName=async t=>{let{path:r,headers:a,query:n,body:o}=Vd(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};updateInterface=async t=>{let{path:r,headers:a,query:n,body:o}=Kd(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"put",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};deleteInterface=async t=>{let{path:r,headers:a,query:n,body:o}=jd(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"delete",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};listInterfaces=async t=>{let{path:r,headers:a,query:n,body:o}=Jd(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};createPlugin=async t=>{let{path:r,headers:a,query:n,body:o}=Zd(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getPlugin=async t=>{let{path:r,headers:a,query:n,body:o}=el(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getPluginByName=async t=>{let{path:r,headers:a,query:n,body:o}=sl(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};updatePlugin=async t=>{let{path:r,headers:a,query:n,body:o}=al(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"put",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};deletePlugin=async t=>{let{path:r,headers:a,query:n,body:o}=ol(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"delete",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};listPlugins=async t=>{let{path:r,headers:a,query:n,body:o}=pl(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getPluginCode=async t=>{let{path:r,headers:a,query:n,body:o}=cl(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getUsage=async t=>{let{path:r,headers:a,query:n,body:o}=ll(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getMultipleUsages=async t=>{let{path:r,headers:a,query:n,body:o}=yl(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};listUsageHistory=async t=>{let{path:r,headers:a,query:n,body:o}=ml(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};listUsageActivity=async t=>{let{path:r,headers:a,query:n,body:o}=hl(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};listUsageActivityDaily=async t=>{let{path:r,headers:a,query:n,body:o}=fl(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};changeAISpendQuota=async t=>{let{path:r,headers:a,query:n,body:o}=Il(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"put",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};listActivities=async t=>{let{path:r,headers:a,query:n,body:o}=Pl(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};introspect=async t=>{let{path:r,headers:a,query:n,body:o}=Al(t),i=this.props.toAxiosRequest??l,u=this.props.toApiError??g,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})}};function g(e){return Sk.isAxiosError(e)&&e.response?.data?Rr(e.response.data):Rr(e)}var mr=class extends pt{config;constructor(t){let r=_.getClientConfig(t),a=F.createAxios(r),n=Hk.create(a);super(n,{toApiError:M.toApiError}),t.retry&&O(n,t.retry),this.config=r}get list(){return{publicIntegrations:t=>new h.AsyncCollection(({nextToken:r})=>this.listPublicIntegrations({nextToken:r,...t}).then(a=>({...a,items:a.integrations}))),bots:t=>new h.AsyncCollection(({nextToken:r})=>this.listBots({nextToken:r,...t}).then(a=>({...a,items:a.bots}))),botIssues:t=>new h.AsyncCollection(({nextToken:r})=>this.listBotIssues({nextToken:r,...t}).then(a=>({...a,items:a.issues}))),workspaces:t=>new h.AsyncCollection(({nextToken:r})=>this.listWorkspaces({nextToken:r,...t}).then(a=>({...a,items:a.workspaces}))),publicWorkspaces:t=>new h.AsyncCollection(({nextToken:r})=>this.listPublicWorkspaces({nextToken:r,...t}).then(a=>({...a,items:a.workspaces}))),workspaceMembers:t=>new h.AsyncCollection(({nextToken:r})=>this.listWorkspaceMembers({nextToken:r,...t}).then(a=>({...a,items:a.members}))),integrations:t=>new h.AsyncCollection(({nextToken:r})=>this.listIntegrations({nextToken:r,...t}).then(a=>({...a,items:a.integrations}))),interfaces:t=>new h.AsyncCollection(({nextToken:r})=>this.listInterfaces({nextToken:r,...t}).then(a=>({...a,items:a.interfaces}))),activities:t=>new h.AsyncCollection(({nextToken:r})=>this.listActivities({nextToken:r,...t}).then(a=>({...a,items:a.activities}))),usageActivity:t=>new h.AsyncCollection(({nextToken:r})=>this.listUsageActivity({nextToken:r,...t}).then(a=>({...a,items:a.data}))),usageActivityDaily:t=>new h.AsyncCollection(({nextToken:r})=>this.listUsageActivityDaily({nextToken:r,...t}).then(a=>({...a,items:a.data})))}}};var pg={};z(pg,{Client:()=>Mr});import jk from"axios";import Kk from"axios";import Dk from"crypto";var _k={getRandomValues:e=>new Uint8Array(e.map(()=>Math.floor(Math.random()*256)))},qr=typeof window<"u"&&typeof window.document<"u"?window.crypto:Dk;qr.getRandomValues||(qr=_k);var B=class extends Error{constructor(r,a,n,o,i,u,p){super(o);this.code=r;this.description=a;this.type=n;this.message=o;this.error=i;this.id=u;this.metadata=p;this.id||(this.id=B.generateId())}isApiError=!0;format(){return`[${this.type}] ${this.message} (Error ID: ${this.id})`}toJSON(){return{id:this.id,code:this.code,type:this.type,message:this.message,metadata:this.metadata}}static generateId(){let r=this.getPrefix(),a=new Date().toISOString().replace(/[\-:TZ]/g,"").split(".")[0],n=4,o=Array.from(qr.getRandomValues(new Uint8Array(n))).map(i=>i.toString(16).padStart(2,"0")).join("").toUpperCase();return`${r}_${a}x${o}`}static getPrefix(){return typeof window<"u"&&typeof window.document<"u"?"err_bwsr":"err"}},Fk=e=>typeof e=="object"&&!Array.isArray(e)&&e!==null,Mk=e=>e instanceof B||Fk(e)&&e.isApiError===!0,ge=class extends B{constructor(t,r,a,n){super(500,"An unknown error occurred","Unknown",t,r,a,n)}},hr=class extends B{constructor(t,r,a,n){super(500,"An internal error occurred","Internal",t,r,a,n)}},br=class extends B{constructor(t,r,a,n){super(401,"The request requires to be authenticated.","Unauthorized",t,r,a,n)}},fr=class extends B{constructor(t,r,a,n){super(403,"The requested action can't be peform by this resource.","Forbidden",t,r,a,n)}},xr=class extends B{constructor(t,r,a,n){super(413,"The request payload is too large.","PayloadTooLarge",t,r,a,n)}},Ir=class extends B{constructor(t,r,a,n){super(400,"The request payload is invalid.","InvalidPayload",t,r,a,n)}},kr=class extends B{constructor(t,r,a,n){super(415,"The request is invalid because the content-type is not supported.","UnsupportedMediaType",t,r,a,n)}},Pr=class extends B{constructor(t,r,a,n){super(405,"The requested method does not exist.","MethodNotFound",t,r,a,n)}},vr=class extends B{constructor(t,r,a,n){super(404,"The requested resource does not exist.","ResourceNotFound",t,r,a,n)}},Ar=class extends B{constructor(t,r,a,n){super(400,"The provided JSON schema is invalid.","InvalidJsonSchema",t,r,a,n)}},Tr=class extends B{constructor(t,r,a,n){super(400,"The provided data doesn't respect the provided JSON schema.","InvalidDataFormat",t,r,a,n)}},Br=class extends B{constructor(t,r,a,n){super(400,"The provided identifier is not valid. An identifier must start with a lowercase letter, be between 2 and 100 characters long and use only alphanumeric characters.","InvalidIdentifier",t,r,a,n)}},Cr=class extends B{constructor(t,r,a,n){super(409,"The resource is related with a different resource that the one referenced in the request. This is usually caused when providing two resource identifiers that aren't linked together.","RelationConflict",t,r,a,n)}},wr=class extends B{constructor(t,r,a,n){super(409,"The resource cannot be deleted because it's referenced by another resource","ReferenceConstraint",t,r,a,n)}},Ur=class extends B{constructor(t,r,a,n){super(409,"The resource is current locked and cannot be operated on until the lock is released.","ResourceLockedConflict",t,r,a,n)}},Gr=class extends B{constructor(t,r,a,n){super(400,"The provided resource reference is missing. This is usually caused when providing an invalid id inside the payload of a request.","ReferenceNotFound",t,r,a,n)}},Er=class extends B{constructor(t,r,a,n){super(400,"The provided query is invalid. This is usually caused when providing an invalid parameter for querying a resource.","InvalidQuery",t,r,a,n)}},Lr=class extends B{constructor(t,r,a,n){super(400,"An error happened during the execution of a runtime (bot or integration).","Runtime",t,r,a,n)}},Wr=class extends B{constructor(t,r,a,n){super(409,"The record attempted to be created already exists.","AlreadyExists",t,r,a,n)}},Sr=class extends B{constructor(t,r,a,n){super(429,"The request has been rate limited.","RateLimited",t,r,a,n)}},Qr=class extends B{constructor(t,r,a,n){super(402,"A payment is required to perform this request.","PaymentRequired",t,r,a,n)}},Hr=class extends B{constructor(t,r,a,n){super(403,"The request exceeds the allowed quota. Quotas are a soft limit that can be increased.","QuotaExceeded",t,r,a,n)}},Dr=class extends B{constructor(t,r,a,n){super(413,"The request exceeds the allowed limit. Limits are a hard limit that cannot be increased.","LimitExceeded",t,r,a,n)}},_r=class extends B{constructor(t,r,a,n){super(400,"Request payload contains breaking changes which is not allowed for this resource without a version increment.","BreakingChanges",t,r,a,n)}},Ok={Unknown:ge,Internal:hr,Unauthorized:br,Forbidden:fr,PayloadTooLarge:xr,InvalidPayload:Ir,UnsupportedMediaType:kr,MethodNotFound:Pr,ResourceNotFound:vr,InvalidJsonSchema:Ar,InvalidDataFormat:Tr,InvalidIdentifier:Br,RelationConflict:Cr,ReferenceConstraint:wr,ResourceLockedConflict:Ur,ReferenceNotFound:Gr,InvalidQuery:Er,Runtime:Lr,AlreadyExists:Wr,RateLimited:Sr,PaymentRequired:Qr,QuotaExceeded:Hr,LimitExceeded:Dr,BreakingChanges:_r},Fr=e=>Mk(e)?e:e instanceof Error?new ge(e.message,e):typeof e=="string"?new ge(e):Vk(e);function Vk(e){if(typeof e=="object"&&"code"in e&&"type"in e&&"id"in e&&"message"in e&&typeof e.type=="string"&&typeof e.message=="string"){let t=Ok[e.type];return t?new t(e.message,void 0,e.id||"UNKNOWN",e.metadata):new ge(`An unclassified API error occurred: ${e.message} (Type: ${e.type}, Code: ${e.code})`)}return new ge("An invalid error occurred: "+JSON.stringify(e))}var Cl=te(Ce()),Nk=e=>e[1]!==void 0,H=e=>{let{method:t,path:r,query:a,headers:n,body:o}=e,i=Object.entries(n).filter(Nk),u=Object.fromEntries(i),p=Cl.default.stringify(a,{encode:!0,arrayFormat:"repeat",allowDots:!0}),s=p?[r,p].join("?"):r,y=["put","post","delete","patch"].includes(t.toLowerCase())?o:void 0;return{method:t,url:s,headers:u,data:y}};var wl=e=>({path:"/v1/files",headers:{},query:{},params:{},body:{key:e.key,tags:e.tags,size:e.size,index:e.index,indexing:e.indexing,accessPolicies:e.accessPolicies,contentType:e.contentType,expiresAt:e.expiresAt,publicContentImmediatelyAccessible:e.publicContentImmediatelyAccessible,metadata:e.metadata}});var Gl=e=>({path:`/v1/files/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var Ll=e=>({path:"/v1/files",headers:{},query:{nextToken:e.nextToken,sortField:e.sortField,sortDirection:e.sortDirection,tags:e.tags,ids:e.ids},params:{},body:{}});var Sl=e=>({path:`/v1/files/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var Hl=e=>({path:`/v1/files/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{metadata:e.metadata,tags:e.tags,accessPolicies:e.accessPolicies,expiresAt:e.expiresAt}});var _l=e=>({path:`/v1/files/${encodeURIComponent(e.idOrKey)}/${encodeURIComponent(e.destinationKey)}`,headers:{"x-destination-bot-id":e["x-destination-bot-id"]},query:{},params:{idOrKey:e.idOrKey,destinationKey:e.destinationKey},body:{overwrite:e.overwrite}});var Ml=e=>({path:"/v1/files/search",headers:{},query:{tags:e.tags,query:e.query,contextDepth:e.contextDepth,limit:e.limit,consolidate:e.consolidate,includeBreadcrumb:e.includeBreadcrumb},params:{},body:{}});var Vl=e=>({path:`/v1/files/${encodeURIComponent(e.id)}/passages`,headers:{},query:{nextToken:e.nextToken,limit:e.limit},params:{id:e.id},body:{}});var Kl=e=>({path:`/v1/files/${encodeURIComponent(e.id)}/passages`,headers:{},query:{},params:{id:e.id},body:{passages:e.passages}});var jl=e=>({path:"/v1/files/tags",headers:{},query:{nextToken:e.nextToken},params:{},body:{}});var Jl=e=>({path:`/v1/files/tags/${encodeURIComponent(e.tag)}/values`,headers:{},query:{nextToken:e.nextToken},params:{tag:e.tag},body:{}});var Zl=e=>({path:"/v1/files/knowledge-bases",headers:{},query:{},params:{},body:{name:e.name}});var eg=e=>({path:`/v1/files/knowledge-bases/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var sg=e=>({path:`/v1/files/knowledge-bases/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{name:e.name}});var ag=e=>({path:"/v1/files/knowledge-bases",headers:{},query:{nextToken:e.nextToken},params:{},body:{}});var ut=class{constructor(t,r={}){this.axiosInstance=t;this.props=r}upsertFile=async t=>{let{path:r,headers:a,query:n,body:o}=wl(t),i=this.props.toAxiosRequest??H,u=this.props.toApiError??D,p=i({method:"put",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};deleteFile=async t=>{let{path:r,headers:a,query:n,body:o}=Gl(t),i=this.props.toAxiosRequest??H,u=this.props.toApiError??D,p=i({method:"delete",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};listFiles=async t=>{let{path:r,headers:a,query:n,body:o}=Ll(t),i=this.props.toAxiosRequest??H,u=this.props.toApiError??D,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getFile=async t=>{let{path:r,headers:a,query:n,body:o}=Sl(t),i=this.props.toAxiosRequest??H,u=this.props.toApiError??D,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};updateFileMetadata=async t=>{let{path:r,headers:a,query:n,body:o}=Hl(t),i=this.props.toAxiosRequest??H,u=this.props.toApiError??D,p=i({method:"put",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};copyFile=async t=>{let{path:r,headers:a,query:n,body:o}=_l(t),i=this.props.toAxiosRequest??H,u=this.props.toApiError??D,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};searchFiles=async t=>{let{path:r,headers:a,query:n,body:o}=Ml(t),i=this.props.toAxiosRequest??H,u=this.props.toApiError??D,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};listFilePassages=async t=>{let{path:r,headers:a,query:n,body:o}=Vl(t),i=this.props.toAxiosRequest??H,u=this.props.toApiError??D,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};setFilePassages=async t=>{let{path:r,headers:a,query:n,body:o}=Kl(t),i=this.props.toAxiosRequest??H,u=this.props.toApiError??D,p=i({method:"put",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};listFileTags=async t=>{let{path:r,headers:a,query:n,body:o}=jl(t),i=this.props.toAxiosRequest??H,u=this.props.toApiError??D,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};listFileTagValues=async t=>{let{path:r,headers:a,query:n,body:o}=Jl(t),i=this.props.toAxiosRequest??H,u=this.props.toApiError??D,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};createKnowledgeBase=async t=>{let{path:r,headers:a,query:n,body:o}=Zl(t),i=this.props.toAxiosRequest??H,u=this.props.toApiError??D,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};deleteKnowledgeBase=async t=>{let{path:r,headers:a,query:n,body:o}=eg(t),i=this.props.toAxiosRequest??H,u=this.props.toApiError??D,p=i({method:"delete",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};updateKnowledgeBase=async t=>{let{path:r,headers:a,query:n,body:o}=sg(t),i=this.props.toAxiosRequest??H,u=this.props.toApiError??D,p=i({method:"put",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};listKnowledgeBases=async t=>{let{path:r,headers:a,query:n,body:o}=ag(t),i=this.props.toAxiosRequest??H,u=this.props.toApiError??D,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})}};function D(e){return Kk.isAxiosError(e)&&e.response?.data?Fr(e.response.data):Fr(e)}import og from"axios";var ct=async(e,{key:t,index:r,tags:a,contentType:n,accessPolicies:o,content:i,url:u,indexing:p,expiresAt:s,metadata:y,publicContentImmediatelyAccessible:P})=>{if(u&&i)throw new re("Cannot provide both content and URL, please provide only one of them");if(u&&(i=await og.get(u,{responseType:"arraybuffer"}).then(w=>w.data).catch(w=>{throw new re(`Failed to download file from provided URL: ${w.message}`,w)})),!i)throw new re("No content was provided for the file");let b,k;if(typeof i=="string"){let x=new TextEncoder().encode(i);b=x,k=x.byteLength}else if(i instanceof Uint8Array)b=i,k=b.byteLength;else if(i instanceof ArrayBuffer)b=i,k=b.byteLength;else if(i instanceof Blob)b=i,k=i.size;else throw new re("The provided content is not supported");let{file:I}=await e.upsertFile({key:t,tags:a,index:r,accessPolicies:o,contentType:n,metadata:y,size:k,expiresAt:s,indexing:p,publicContentImmediatelyAccessible:P}),U={"Content-Type":I.contentType};P&&(U["x-amz-tagging"]="public=true");try{await og.put(I.uploadUrl,b,{maxBodyLength:1/0,headers:U})}catch(w){let x=w instanceof Error?w:new Error(String(w));throw new re(`Failed to upload file: ${x.message}`,x,I)}return{file:{...I,size:k}}};var Mr=class extends ut{config;constructor(t){let r=_.getClientConfig(t),a=F.createAxios(r),n=jk.create(a);super(n,{toApiError:M.toApiError}),t.retry&&O(n,t.retry),this.config=r}get list(){return{files:t=>new h.AsyncCollection(({nextToken:r})=>this.listFiles({nextToken:r,...t}).then(a=>({...a,items:a.files}))),filePassages:t=>new h.AsyncCollection(({nextToken:r})=>this.listFilePassages({nextToken:r,...t}).then(a=>({...a,items:a.passages})))}}async uploadFile(t){return await ct(this,t)}};var Vg={};z(Vg,{Client:()=>ya});import aP from"axios";import sP from"axios";import zk from"crypto";var Jk={getRandomValues:e=>new Uint8Array(e.map(()=>Math.floor(Math.random()*256)))},Or=typeof window<"u"&&typeof window.document<"u"?window.crypto:zk;Or.getRandomValues||(Or=Jk);var C=class extends Error{constructor(r,a,n,o,i,u,p){super(o);this.code=r;this.description=a;this.type=n;this.message=o;this.error=i;this.id=u;this.metadata=p;this.id||(this.id=C.generateId())}isApiError=!0;format(){return`[${this.type}] ${this.message} (Error ID: ${this.id})`}toJSON(){return{id:this.id,code:this.code,type:this.type,message:this.message,metadata:this.metadata}}static generateId(){let r=this.getPrefix(),a=new Date().toISOString().replace(/[\-:TZ]/g,"").split(".")[0],n=4,o=Array.from(Or.getRandomValues(new Uint8Array(n))).map(i=>i.toString(16).padStart(2,"0")).join("").toUpperCase();return`${r}_${a}x${o}`}static getPrefix(){return typeof window<"u"&&typeof window.document<"u"?"err_bwsr":"err"}},Yk=e=>typeof e=="object"&&!Array.isArray(e)&&e!==null,Zk=e=>e instanceof C||Yk(e)&&e.isApiError===!0,ye=class extends C{constructor(t,r,a,n){super(500,"An unknown error occurred","Unknown",t,r,a,n)}},Vr=class extends C{constructor(t,r,a,n){super(500,"An internal error occurred","Internal",t,r,a,n)}},Nr=class extends C{constructor(t,r,a,n){super(401,"The request requires to be authenticated.","Unauthorized",t,r,a,n)}},Kr=class extends C{constructor(t,r,a,n){super(403,"The requested action can't be peform by this resource.","Forbidden",t,r,a,n)}},$r=class extends C{constructor(t,r,a,n){super(413,"The request payload is too large.","PayloadTooLarge",t,r,a,n)}},jr=class extends C{constructor(t,r,a,n){super(400,"The request payload is invalid.","InvalidPayload",t,r,a,n)}},zr=class extends C{constructor(t,r,a,n){super(415,"The request is invalid because the content-type is not supported.","UnsupportedMediaType",t,r,a,n)}},Jr=class extends C{constructor(t,r,a,n){super(405,"The requested method does not exist.","MethodNotFound",t,r,a,n)}},Yr=class extends C{constructor(t,r,a,n){super(404,"The requested resource does not exist.","ResourceNotFound",t,r,a,n)}},Zr=class extends C{constructor(t,r,a,n){super(400,"The provided JSON schema is invalid.","InvalidJsonSchema",t,r,a,n)}},Xr=class extends C{constructor(t,r,a,n){super(400,"The provided data doesn't respect the provided JSON schema.","InvalidDataFormat",t,r,a,n)}},ea=class extends C{constructor(t,r,a,n){super(400,"The provided identifier is not valid. An identifier must start with a lowercase letter, be between 2 and 100 characters long and use only alphanumeric characters.","InvalidIdentifier",t,r,a,n)}},ta=class extends C{constructor(t,r,a,n){super(409,"The resource is related with a different resource that the one referenced in the request. This is usually caused when providing two resource identifiers that aren't linked together.","RelationConflict",t,r,a,n)}},sa=class extends C{constructor(t,r,a,n){super(409,"The resource cannot be deleted because it's referenced by another resource","ReferenceConstraint",t,r,a,n)}},ra=class extends C{constructor(t,r,a,n){super(409,"The resource is current locked and cannot be operated on until the lock is released.","ResourceLockedConflict",t,r,a,n)}},aa=class extends C{constructor(t,r,a,n){super(400,"The provided resource reference is missing. This is usually caused when providing an invalid id inside the payload of a request.","ReferenceNotFound",t,r,a,n)}},na=class extends C{constructor(t,r,a,n){super(400,"The provided query is invalid. This is usually caused when providing an invalid parameter for querying a resource.","InvalidQuery",t,r,a,n)}},oa=class extends C{constructor(t,r,a,n){super(400,"An error happened during the execution of a runtime (bot or integration).","Runtime",t,r,a,n)}},ia=class extends C{constructor(t,r,a,n){super(409,"The record attempted to be created already exists.","AlreadyExists",t,r,a,n)}},pa=class extends C{constructor(t,r,a,n){super(429,"The request has been rate limited.","RateLimited",t,r,a,n)}},ua=class extends C{constructor(t,r,a,n){super(402,"A payment is required to perform this request.","PaymentRequired",t,r,a,n)}},ca=class extends C{constructor(t,r,a,n){super(403,"The request exceeds the allowed quota. Quotas are a soft limit that can be increased.","QuotaExceeded",t,r,a,n)}},da=class extends C{constructor(t,r,a,n){super(413,"The request exceeds the allowed limit. Limits are a hard limit that cannot be increased.","LimitExceeded",t,r,a,n)}},la=class extends C{constructor(t,r,a,n){super(400,"Request payload contains breaking changes which is not allowed for this resource without a version increment.","BreakingChanges",t,r,a,n)}},Xk={Unknown:ye,Internal:Vr,Unauthorized:Nr,Forbidden:Kr,PayloadTooLarge:$r,InvalidPayload:jr,UnsupportedMediaType:zr,MethodNotFound:Jr,ResourceNotFound:Yr,InvalidJsonSchema:Zr,InvalidDataFormat:Xr,InvalidIdentifier:ea,RelationConflict:ta,ReferenceConstraint:sa,ResourceLockedConflict:ra,ReferenceNotFound:aa,InvalidQuery:na,Runtime:oa,AlreadyExists:ia,RateLimited:pa,PaymentRequired:ua,QuotaExceeded:ca,LimitExceeded:da,BreakingChanges:la},ga=e=>Zk(e)?e:e instanceof Error?new ye(e.message,e):typeof e=="string"?new ye(e):eP(e);function eP(e){if(typeof e=="object"&&"code"in e&&"type"in e&&"id"in e&&"message"in e&&typeof e.type=="string"&&typeof e.message=="string"){let t=Xk[e.type];return t?new t(e.message,void 0,e.id||"UNKNOWN",e.metadata):new ye(`An unclassified API error occurred: ${e.message} (Type: ${e.type}, Code: ${e.code})`)}return new ye("An invalid error occurred: "+JSON.stringify(e))}var ug=te(Ce()),tP=e=>e[1]!==void 0,L=e=>{let{method:t,path:r,query:a,headers:n,body:o}=e,i=Object.entries(n).filter(tP),u=Object.fromEntries(i),p=ug.default.stringify(a,{encode:!0,arrayFormat:"repeat",allowDots:!0}),s=p?[r,p].join("?"):r,y=["put","post","delete","patch"].includes(t.toLowerCase())?o:void 0;return{method:t,url:s,headers:u,data:y}};var cg=e=>({path:"/v1/tables",headers:{},query:{tags:e.tags},params:{},body:{}});var lg=e=>({path:`/v1/tables/${encodeURIComponent(e.table)}`,headers:{},query:{},params:{table:e.table},body:{}});var yg=e=>({path:`/v1/tables/${encodeURIComponent(e.table)}`,headers:{},query:{},params:{table:e.table},body:{factor:e.factor,frozen:e.frozen,schema:e.schema,tags:e.tags,isComputeEnabled:e.isComputeEnabled}});var mg=e=>({path:"/v1/tables",headers:{},query:{},params:{},body:{name:e.name,factor:e.factor,frozen:e.frozen,schema:e.schema,tags:e.tags,isComputeEnabled:e.isComputeEnabled}});var hg=e=>({path:`/v1/tables/${encodeURIComponent(e.sourceTableId)}/duplicate`,headers:{},query:{},params:{sourceTableId:e.sourceTableId},body:{tableName:e.tableName,schemaOnly:e.schemaOnly,factor:e.factor}});var fg=e=>({path:`/v1/tables/${encodeURIComponent(e.table)}/export`,headers:{},query:{format:e.format,compress:e.compress},params:{table:e.table},body:{}});var Ig=e=>({path:`/v1/tables/${encodeURIComponent(e.table)}/jobs`,headers:{},query:{},params:{table:e.table},body:{}});var Pg=e=>({path:`/v1/tables/${encodeURIComponent(e.table)}/import`,headers:{},query:{},params:{table:e.table},body:{fileId:e.fileId}});var Ag=e=>({path:`/v1/tables/${encodeURIComponent(e.table)}`,headers:{},query:{},params:{table:e.table},body:{name:e.name,frozen:e.frozen,schema:e.schema,tags:e.tags,isComputeEnabled:e.isComputeEnabled}});var Bg=e=>({path:`/v1/tables/${encodeURIComponent(e.table)}/column`,headers:{},query:{},params:{table:e.table},body:{name:e.name,newName:e.newName}});var wg=e=>({path:`/v1/tables/${encodeURIComponent(e.table)}`,headers:{},query:{},params:{table:e.table},body:{}});var Gg=e=>({path:`/v1/tables/${encodeURIComponent(e.table)}/row`,headers:{},query:{id:e.id},params:{table:e.table},body:{}});var Lg=e=>({path:`/v1/tables/${encodeURIComponent(e.table)}/rows/find`,headers:{},query:{},params:{table:e.table},body:{limit:e.limit,offset:e.offset,filter:e.filter,group:e.group,search:e.search,orderBy:e.orderBy,orderDirection:e.orderDirection}});var Sg=e=>({path:`/v1/tables/${encodeURIComponent(e.table)}/rows`,headers:{},query:{},params:{table:e.table},body:{rows:e.rows,waitComputed:e.waitComputed}});var Hg=e=>({path:`/v1/tables/${encodeURIComponent(e.table)}/rows/delete`,headers:{},query:{},params:{table:e.table},body:{ids:e.ids,filter:e.filter,deleteAllRows:e.deleteAllRows}});var _g=e=>({path:`/v1/tables/${encodeURIComponent(e.table)}/rows`,headers:{},query:{},params:{table:e.table},body:{rows:e.rows,waitComputed:e.waitComputed}});var Mg=e=>({path:`/v1/tables/${encodeURIComponent(e.table)}/rows/upsert`,headers:{},query:{},params:{table:e.table},body:{rows:e.rows,keyColumn:e.keyColumn,waitComputed:e.waitComputed}});var dt=class{constructor(t,r={}){this.axiosInstance=t;this.props=r}listTables=async t=>{let{path:r,headers:a,query:n,body:o}=cg(t),i=this.props.toAxiosRequest??L,u=this.props.toApiError??S,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getTable=async t=>{let{path:r,headers:a,query:n,body:o}=lg(t),i=this.props.toAxiosRequest??L,u=this.props.toApiError??S,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getOrCreateTable=async t=>{let{path:r,headers:a,query:n,body:o}=yg(t),i=this.props.toAxiosRequest??L,u=this.props.toApiError??S,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};createTable=async t=>{let{path:r,headers:a,query:n,body:o}=mg(t),i=this.props.toAxiosRequest??L,u=this.props.toApiError??S,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};duplicateTable=async t=>{let{path:r,headers:a,query:n,body:o}=hg(t),i=this.props.toAxiosRequest??L,u=this.props.toApiError??S,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};exportTable=async t=>{let{path:r,headers:a,query:n,body:o}=fg(t),i=this.props.toAxiosRequest??L,u=this.props.toApiError??S,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getTableJobs=async t=>{let{path:r,headers:a,query:n,body:o}=Ig(t),i=this.props.toAxiosRequest??L,u=this.props.toApiError??S,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};importTable=async t=>{let{path:r,headers:a,query:n,body:o}=Pg(t),i=this.props.toAxiosRequest??L,u=this.props.toApiError??S,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};updateTable=async t=>{let{path:r,headers:a,query:n,body:o}=Ag(t),i=this.props.toAxiosRequest??L,u=this.props.toApiError??S,p=i({method:"put",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};renameTableColumn=async t=>{let{path:r,headers:a,query:n,body:o}=Bg(t),i=this.props.toAxiosRequest??L,u=this.props.toApiError??S,p=i({method:"put",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};deleteTable=async t=>{let{path:r,headers:a,query:n,body:o}=wg(t),i=this.props.toAxiosRequest??L,u=this.props.toApiError??S,p=i({method:"delete",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getTableRow=async t=>{let{path:r,headers:a,query:n,body:o}=Gg(t),i=this.props.toAxiosRequest??L,u=this.props.toApiError??S,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};findTableRows=async t=>{let{path:r,headers:a,query:n,body:o}=Lg(t),i=this.props.toAxiosRequest??L,u=this.props.toApiError??S,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};createTableRows=async t=>{let{path:r,headers:a,query:n,body:o}=Sg(t),i=this.props.toAxiosRequest??L,u=this.props.toApiError??S,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};deleteTableRows=async t=>{let{path:r,headers:a,query:n,body:o}=Hg(t),i=this.props.toAxiosRequest??L,u=this.props.toApiError??S,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};updateTableRows=async t=>{let{path:r,headers:a,query:n,body:o}=_g(t),i=this.props.toAxiosRequest??L,u=this.props.toApiError??S,p=i({method:"put",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};upsertTableRows=async t=>{let{path:r,headers:a,query:n,body:o}=Mg(t),i=this.props.toAxiosRequest??L,u=this.props.toApiError??S,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})}};function S(e){return sP.isAxiosError(e)&&e.response?.data?ga(e.response.data):ga(e)}var ya=class extends dt{config;constructor(t){let r=_.getClientConfig(t),a=F.createAxios(r),n=aP.create(a);super(n,{toApiError:M.toApiError}),t.retry&&O(n,t.retry),this.config=r}};import pP from"axios";import oP from"axios";var Ng=te(Ce()),nP=e=>e[1]!==void 0,c=e=>{let{method:t,path:r,query:a,headers:n,body:o}=e,i=Object.entries(n).filter(nP),u=Object.fromEntries(i),p=Ng.default.stringify(a,{encode:!0,arrayFormat:"repeat",allowDots:!0}),s=p?[r,p].join("?"):r,y=["put","post","delete","patch"].includes(t.toLowerCase())?o:void 0;return{method:t,url:s,headers:u,data:y}};var Kg=e=>({path:"/v1/chat/conversations",headers:{},query:{},params:{},body:{channel:e.channel,tags:e.tags,integrationName:e.integrationName}});var jg=e=>({path:`/v1/chat/conversations/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var Jg=e=>({path:"/v1/chat/conversations",headers:{},query:{nextToken:e.nextToken,sortField:e.sortField,sortDirection:e.sortDirection,tags:e.tags,participantIds:e.participantIds,integrationName:e.integrationName,channel:e.channel},params:{},body:{}});var Zg=e=>({path:"/v1/chat/conversations/get-or-create",headers:{},query:{},params:{},body:{channel:e.channel,tags:e.tags,integrationName:e.integrationName,discriminateByTags:e.discriminateByTags}});var ey=e=>({path:`/v1/chat/conversations/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{currentTaskId:e.currentTaskId,tags:e.tags}});var sy=e=>({path:`/v1/chat/conversations/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var ay=e=>({path:`/v1/chat/conversations/${encodeURIComponent(e.id)}/participants`,headers:{},query:{nextToken:e.nextToken},params:{id:e.id},body:{}});var oy=e=>({path:`/v1/chat/conversations/${encodeURIComponent(e.id)}/participants`,headers:{},query:{},params:{id:e.id},body:{userId:e.userId}});var py=e=>({path:`/v1/chat/conversations/${encodeURIComponent(e.id)}/participants/${encodeURIComponent(e.userId)}`,headers:{},query:{},params:{id:e.id,userId:e.userId},body:{}});var cy=e=>({path:`/v1/chat/conversations/${encodeURIComponent(e.id)}/participants/${encodeURIComponent(e.userId)}`,headers:{},query:{},params:{id:e.id,userId:e.userId},body:{}});var ly=e=>({path:"/v1/chat/events",headers:{},query:{},params:{},body:{type:e.type,payload:e.payload,schedule:e.schedule,conversationId:e.conversationId,userId:e.userId,messageId:e.messageId}});var yy=e=>({path:`/v1/chat/events/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var my=e=>({path:"/v1/chat/events",headers:{},query:{nextToken:e.nextToken,type:e.type,conversationId:e.conversationId,userId:e.userId,messageId:e.messageId,status:e.status},params:{},body:{}});var hy=e=>({path:"/v1/chat/messages",headers:{},query:{},params:{},body:{payload:e.payload,userId:e.userId,conversationId:e.conversationId,type:e.type,tags:e.tags,schedule:e.schedule}});var fy=e=>({path:"/v1/chat/messages/get-or-create",headers:{},query:{},params:{},body:{payload:e.payload,userId:e.userId,conversationId:e.conversationId,type:e.type,tags:e.tags,schedule:e.schedule,discriminateByTags:e.discriminateByTags}});var Iy=e=>({path:`/v1/chat/messages/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var Py=e=>({path:`/v1/chat/messages/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{tags:e.tags,payload:e.payload}});var Ay=e=>({path:"/v1/chat/messages",headers:{},query:{nextToken:e.nextToken,conversationId:e.conversationId,tags:e.tags},params:{},body:{}});var By=e=>({path:`/v1/chat/messages/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var wy=e=>({path:"/v1/chat/users",headers:{},query:{},params:{},body:{tags:e.tags,integrationName:e.integrationName,name:e.name,pictureUrl:e.pictureUrl}});var Gy=e=>({path:`/v1/chat/users/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var Ly=e=>({path:"/v1/chat/users",headers:{},query:{nextToken:e.nextToken,conversationId:e.conversationId,tags:e.tags},params:{},body:{}});var Sy=e=>({path:"/v1/chat/users/get-or-create",headers:{},query:{},params:{},body:{tags:e.tags,integrationName:e.integrationName,name:e.name,pictureUrl:e.pictureUrl,discriminateByTags:e.discriminateByTags}});var Hy=e=>({path:`/v1/chat/users/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{tags:e.tags,name:e.name,pictureUrl:e.pictureUrl}});var _y=e=>({path:`/v1/chat/users/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var My=e=>({path:`/v1/chat/states/${encodeURIComponent(e.type)}/${encodeURIComponent(e.id)}/${encodeURIComponent(e.name)}/expiry`,headers:{},query:{},params:{type:e.type,id:e.id,name:e.name},body:{expiry:e.expiry}});var Vy=e=>({path:`/v1/chat/states/${encodeURIComponent(e.type)}/${encodeURIComponent(e.id)}/${encodeURIComponent(e.name)}`,headers:{},query:{},params:{type:e.type,id:e.id,name:e.name},body:{}});var Ky=e=>({path:`/v1/chat/states/${encodeURIComponent(e.type)}/${encodeURIComponent(e.id)}/${encodeURIComponent(e.name)}`,headers:{},query:{},params:{type:e.type,id:e.id,name:e.name},body:{payload:e.payload,expiry:e.expiry}});var jy=e=>({path:`/v1/chat/states/${encodeURIComponent(e.type)}/${encodeURIComponent(e.id)}/${encodeURIComponent(e.name)}/get-or-set`,headers:{},query:{},params:{type:e.type,id:e.id,name:e.name},body:{payload:e.payload,expiry:e.expiry}});var Jy=e=>({path:`/v1/chat/states/${encodeURIComponent(e.type)}/${encodeURIComponent(e.id)}/${encodeURIComponent(e.name)}`,headers:{},query:{},params:{type:e.type,id:e.id,name:e.name},body:{payload:e.payload}});var Zy=e=>({path:"/v1/chat/actions",headers:{},query:{},params:{},body:{type:e.type,input:e.input}});var eR=e=>({path:"/v1/chat/integrations/configure",headers:{},query:{},params:{},body:{identifier:e.identifier,scheduleRegisterCall:e.scheduleRegisterCall,sandboxIdentifiers:e.sandboxIdentifiers}});var sR=e=>({path:`/v1/chat/tasks/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var aR=e=>({path:"/v1/chat/tasks",headers:{},query:{},params:{},body:{title:e.title,description:e.description,type:e.type,data:e.data,parentTaskId:e.parentTaskId,conversationId:e.conversationId,userId:e.userId,timeoutAt:e.timeoutAt,tags:e.tags}});var oR=e=>({path:`/v1/chat/tasks/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{title:e.title,description:e.description,data:e.data,timeoutAt:e.timeoutAt,status:e.status,tags:e.tags}});var pR=e=>({path:`/v1/chat/tasks/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var cR=e=>({path:"/v1/chat/tasks",headers:{},query:{nextToken:e.nextToken,tags:e.tags,conversationId:e.conversationId,userId:e.userId,parentTaskId:e.parentTaskId,status:e.status,type:e.type},params:{},body:{}});var lR=e=>({path:"/v1/chat/workflows",headers:{},query:{},params:{},body:{name:e.name,input:e.input,parentWorkflowId:e.parentWorkflowId,conversationId:e.conversationId,userId:e.userId,timeoutAt:e.timeoutAt,tags:e.tags,status:e.status,eventId:e.eventId}});var yR=e=>({path:`/v1/chat/workflows/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var mR=e=>({path:`/v1/chat/workflows/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{output:e.output,timeoutAt:e.timeoutAt,status:e.status,failureReason:e.failureReason,tags:e.tags,userId:e.userId,eventId:e.eventId}});var hR=e=>({path:`/v1/chat/workflows/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var fR=e=>({path:"/v1/chat/workflows",headers:{},query:{nextToken:e.nextToken,tags:e.tags,conversationId:e.conversationId,userId:e.userId,parentWorkflowId:e.parentWorkflowId,statuses:e.statuses,name:e.name},params:{},body:{}});var IR=e=>({path:"/v1/chat/workflows/get-or-create",headers:{},query:{},params:{},body:{name:e.name,input:e.input,parentWorkflowId:e.parentWorkflowId,conversationId:e.conversationId,userId:e.userId,timeoutAt:e.timeoutAt,tags:e.tags,status:e.status,eventId:e.eventId}});var PR=e=>({path:`/v1/chat/tags/${encodeURIComponent(e.key)}/values`,headers:{},query:{nextToken:e.nextToken,type:e.type},params:{key:e.key},body:{}});var AR=e=>({path:"/v1/chat/analytics",headers:{},query:{},params:{},body:{name:e.name,count:e.count}});var BR=e=>({path:"/v1/admin/helper/vrl",headers:{},query:{},params:{},body:{data:e.data,script:e.script}});var wR=e=>({path:"/v1/admin/account/me",headers:{},query:{},params:{},body:{}});var GR=e=>({path:"/v1/admin/account/me",headers:{},query:{},params:{},body:{displayName:e.displayName,profilePicture:e.profilePicture,refresh:e.refresh}});var LR=e=>({path:"/v1/admin/account/pats",headers:{},query:{},params:{},body:{}});var SR=e=>({path:"/v1/admin/account/pats",headers:{},query:{},params:{},body:{note:e.note}});var HR=e=>({path:`/v1/admin/account/pats/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var _R=e=>({path:`/v1/admin/account/preferences/${encodeURIComponent(e.key)}`,headers:{},query:{},params:{key:e.key},body:{value:e.value}});var MR=e=>({path:`/v1/admin/account/preferences/${encodeURIComponent(e.key)}`,headers:{},query:{},params:{key:e.key},body:{}});var VR=e=>({path:"/v1/admin/hub/integrations",headers:{},query:{nextToken:e.nextToken,limit:e.limit,name:e.name,version:e.version,interfaceId:e.interfaceId,interfaceName:e.interfaceName,installedByBotId:e.installedByBotId,verificationStatus:e.verificationStatus,search:e.search,sortBy:e.sortBy,direction:e.direction},params:{},body:{}});var KR=e=>({path:`/v1/admin/hub/integrations/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var jR=e=>({path:`/v1/admin/hub/integrations/${encodeURIComponent(e.name)}/${encodeURIComponent(e.version)}`,headers:{},query:{},params:{name:e.name,version:e.version},body:{}});var JR=e=>({path:"/v1/admin/hub/plugins",headers:{},query:{nextToken:e.nextToken,name:e.name,version:e.version},params:{},body:{}});var ZR=e=>({path:`/v1/admin/hub/plugins/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var em=e=>({path:`/v1/admin/hub/plugins/${encodeURIComponent(e.name)}/${encodeURIComponent(e.version)}`,headers:{},query:{},params:{name:e.name,version:e.version},body:{}});var sm=e=>({path:`/v1/admin/hub/plugins/${encodeURIComponent(e.id)}/code/${encodeURIComponent(e.platform)}`,headers:{},query:{},params:{id:e.id,platform:e.platform},body:{}});var am=e=>({path:"/v1/admin/hub/interfaces",headers:{},query:{nextToken:e.nextToken,name:e.name,version:e.version},params:{},body:{}});var om=e=>({path:`/v1/admin/hub/interfaces/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var pm=e=>({path:`/v1/admin/hub/interfaces/${encodeURIComponent(e.name)}/${encodeURIComponent(e.version)}`,headers:{},query:{},params:{name:e.name,version:e.version},body:{}});var cm=e=>({path:"/v1/admin/bots",headers:{},query:{},params:{},body:{states:e.states,events:e.events,recurringEvents:e.recurringEvents,subscriptions:e.subscriptions,actions:e.actions,configuration:e.configuration,user:e.user,conversation:e.conversation,message:e.message,tags:e.tags,code:e.code,name:e.name,medias:e.medias,url:e.url,dev:e.dev}});var lm=e=>({path:`/v1/admin/bots/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{url:e.url,authentication:e.authentication,configuration:e.configuration,tags:e.tags,blocked:e.blocked,alwaysAlive:e.alwaysAlive,user:e.user,message:e.message,conversation:e.conversation,events:e.events,actions:e.actions,states:e.states,recurringEvents:e.recurringEvents,integrations:e.integrations,plugins:e.plugins,subscriptions:e.subscriptions,code:e.code,name:e.name,medias:e.medias,layers:e.layers}});var ym=e=>({path:`/v1/admin/bots/${encodeURIComponent(e.id)}/transfer`,headers:{},query:{},params:{id:e.id},body:{targetWorkspaceId:e.targetWorkspaceId}});var mm=e=>({path:"/v1/admin/bots",headers:{},query:{dev:e.dev,tags:e.tags,nextToken:e.nextToken,sortField:e.sortField,sortDirection:e.sortDirection},params:{},body:{}});var hm=e=>({path:`/v1/admin/bots/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var fm=e=>({path:`/v1/admin/bots/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var Im=e=>({path:`/v1/admin/bots/${encodeURIComponent(e.id)}/logs`,headers:{},query:{timeStart:e.timeStart,timeEnd:e.timeEnd,level:e.level,userId:e.userId,workflowId:e.workflowId,conversationId:e.conversationId,messageContains:e.messageContains,nextToken:e.nextToken},params:{id:e.id},body:{}});var Pm=e=>({path:`/v1/admin/bots/${encodeURIComponent(e.id)}/webchat`,headers:{},query:{type:e.type},params:{id:e.id},body:{}});var Am=e=>({path:`/v1/admin/bots/${encodeURIComponent(e.id)}/analytics`,headers:{},query:{startDate:e.startDate,endDate:e.endDate},params:{id:e.id},body:{}});var Bm=e=>({path:`/v1/admin/bots/${encodeURIComponent(e.id)}/issues/${encodeURIComponent(e.issueId)}`,headers:{},query:{},params:{id:e.id,issueId:e.issueId},body:{}});var wm=e=>({path:`/v1/admin/bots/${encodeURIComponent(e.id)}/issues`,headers:{},query:{nextToken:e.nextToken},params:{id:e.id},body:{}});var Gm=e=>({path:`/v1/admin/bots/${encodeURIComponent(e.id)}/issues/${encodeURIComponent(e.issueId)}`,headers:{},query:{},params:{id:e.id,issueId:e.issueId},body:{}});var Lm=e=>({path:`/v1/admin/bots/${encodeURIComponent(e.id)}/issues/${encodeURIComponent(e.issueId)}/events`,headers:{},query:{},params:{id:e.id,issueId:e.issueId},body:{}});var Sm=e=>({path:`/v1/admin/bots/${encodeURIComponent(e.id)}/versions`,headers:{},query:{},params:{id:e.id},body:{}});var Hm=e=>({path:`/v1/admin/bots/${encodeURIComponent(e.id)}/versions/${encodeURIComponent(e.versionId)}`,headers:{},query:{},params:{id:e.id,versionId:e.versionId},body:{}});var _m=e=>({path:`/v1/admin/bots/${encodeURIComponent(e.id)}/versions`,headers:{},query:{},params:{id:e.id},body:{name:e.name,description:e.description}});var Mm=e=>({path:`/v1/admin/bots/${encodeURIComponent(e.id)}/versions/deploy`,headers:{},query:{},params:{id:e.id},body:{versionId:e.versionId}});var Vm=e=>({path:`/v1/admin/bots/${encodeURIComponent(e.botId)}/integrations/${encodeURIComponent(e.integrationId)}/shareable-id`,headers:{},query:{},params:{botId:e.botId,integrationId:e.integrationId},body:{}});var Km=e=>({path:`/v1/admin/bots/${encodeURIComponent(e.botId)}/integrations/${encodeURIComponent(e.integrationId)}/shareable-id`,headers:{},query:{},params:{botId:e.botId,integrationId:e.integrationId},body:{}});var jm=e=>({path:`/v1/admin/bots/${encodeURIComponent(e.botId)}/integrations/${encodeURIComponent(e.integrationId)}/shareable-id`,headers:{},query:{},params:{botId:e.botId,integrationId:e.integrationId},body:{}});var Jm=e=>({path:`/v1/admin/bots/${encodeURIComponent(e.botId)}/integrations/${encodeURIComponent(e.integrationId)}/sandboxed-conversations`,headers:{},query:{},params:{botId:e.botId,integrationId:e.integrationId},body:{}});var Zm=e=>({path:"/v1/admin/bots/baks",headers:{},query:{botId:e.botId},params:{},body:{}});var eq=e=>({path:"/v1/admin/bots/baks",headers:{},query:{},params:{},body:{botId:e.botId,note:e.note}});var sq=e=>({path:`/v1/admin/bots/baks/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var aq=e=>({path:`/v1/admin/workspaces/${encodeURIComponent(e.id)}/billing/invoices`,headers:{},query:{},params:{id:e.id},body:{}});var oq=e=>({path:`/v1/admin/workspaces/${encodeURIComponent(e.id)}/billing/upcoming-invoice`,headers:{},query:{},params:{id:e.id},body:{}});var pq=e=>({path:`/v1/admin/workspaces/${encodeURIComponent(e.id)}/billing/invoices/charge-unpaid`,headers:{},query:{},params:{id:e.id},body:{invoiceIds:e.invoiceIds}});var cq=e=>({path:"/v1/admin/workspaces",headers:{},query:{},params:{},body:{name:e.name}});var lq=e=>({path:`/v1/admin/workspaces/${encodeURIComponent(e.id)}/public`,headers:{},query:{},params:{id:e.id},body:{}});var yq=e=>({path:`/v1/admin/workspaces/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var mq=e=>({path:`/v1/admin/workspaces/${encodeURIComponent(e.id)}/usages`,headers:{},query:{type:e.type,period:e.period},params:{id:e.id},body:{}});var hq=e=>({path:`/v1/admin/workspaces/${encodeURIComponent(e.id)}/usages/by-bot`,headers:{},query:{type:e.type,period:e.period},params:{id:e.id},body:{}});var fq=e=>({path:"/v1/admin/workspaces/usages/quota-completion",headers:{},query:{},params:{},body:{}});var Iq=e=>({path:`/v1/admin/workspaces/${encodeURIComponent(e.id)}/quota`,headers:{},query:{type:e.type,period:e.period},params:{id:e.id},body:{}});var Pq=e=>({path:`/v1/admin/workspaces/${encodeURIComponent(e.id)}/quotas`,headers:{},query:{period:e.period},params:{id:e.id},body:{}});var Aq=e=>({path:`/v1/admin/workspaces/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{name:e.name,spendingLimit:e.spendingLimit,about:e.about,profilePicture:e.profilePicture,contactEmail:e.contactEmail,website:e.website,socialAccounts:e.socialAccounts,isPublic:e.isPublic,handle:e.handle}});var Bq=e=>({path:"/v1/admin/workspaces/handle-availability",headers:{},query:{},params:{},body:{handle:e.handle}});var wq=e=>({path:"/v1/admin/workspaces",headers:{},query:{nextToken:e.nextToken,handle:e.handle},params:{},body:{}});var Gq=e=>({path:"/v1/admin/workspaces/public",headers:{},query:{nextToken:e.nextToken,workspaceIds:e.workspaceIds,search:e.search},params:{},body:{}});var Lq=e=>({path:`/v1/admin/workspaces/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var Sq=e=>({path:`/v1/admin/workspaces/${encodeURIComponent(e.id)}/audit-records`,headers:{},query:{nextToken:e.nextToken},params:{id:e.id},body:{}});var Hq=e=>({path:"/v1/admin/workspace-members",headers:{},query:{nextToken:e.nextToken},params:{},body:{}});var _q=e=>({path:"/v1/admin/workspace-members/me",headers:{},query:{},params:{},body:{}});var Mq=e=>({path:`/v1/admin/workspace-members/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var Vq=e=>({path:"/v1/admin/workspace-members",headers:{},query:{},params:{},body:{email:e.email,role:e.role}});var Kq=e=>({path:`/v1/admin/workspace-members/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{role:e.role}});var jq=e=>({path:"/v1/admin/integrations/iaks",headers:{},query:{integrationId:e.integrationId},params:{},body:{}});var Jq=e=>({path:"/v1/admin/integrations/iaks",headers:{},query:{},params:{},body:{integrationId:e.integrationId,note:e.note}});var Zq=e=>({path:`/v1/admin/integrations/iaks/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var eh=e=>({path:"/v1/admin/integrations",headers:{},query:{},params:{},body:{name:e.name,version:e.version,configuration:e.configuration,configurations:e.configurations,states:e.states,events:e.events,actions:e.actions,entities:e.entities,attributes:e.attributes,identifier:e.identifier,channels:e.channels,user:e.user,interfaces:e.interfaces,secrets:e.secrets,code:e.code,url:e.url,dev:e.dev,icon:e.icon,readme:e.readme,title:e.title,description:e.description,public:e.public,layers:e.layers}});var sh=e=>({path:"/v1/admin/integrations/validate",headers:{},query:{},params:{},body:{name:e.name,version:e.version,configuration:e.configuration,configurations:e.configurations,states:e.states,events:e.events,actions:e.actions,entities:e.entities,attributes:e.attributes,identifier:e.identifier,channels:e.channels,user:e.user,interfaces:e.interfaces,secrets:e.secrets,code:e.code,url:e.url,dev:e.dev,icon:e.icon,readme:e.readme,title:e.title,description:e.description,public:e.public,layers:e.layers}});var ah=e=>({path:`/v1/admin/integrations/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{configuration:e.configuration,configurations:e.configurations,channels:e.channels,identifier:e.identifier,actions:e.actions,events:e.events,states:e.states,user:e.user,entities:e.entities,interfaces:e.interfaces,sandbox:e.sandbox,attributes:e.attributes,secrets:e.secrets,code:e.code,icon:e.icon,readme:e.readme,title:e.title,description:e.description,url:e.url,public:e.public,layers:e.layers}});var oh=e=>({path:`/v1/admin/integrations/${encodeURIComponent(e.id)}/validate`,headers:{},query:{},params:{id:e.id},body:{configuration:e.configuration,configurations:e.configurations,channels:e.channels,identifier:e.identifier,actions:e.actions,events:e.events,states:e.states,user:e.user,entities:e.entities,interfaces:e.interfaces,sandbox:e.sandbox,attributes:e.attributes,secrets:e.secrets,code:e.code,icon:e.icon,readme:e.readme,title:e.title,description:e.description,url:e.url,public:e.public,layers:e.layers}});var ph=e=>({path:"/v1/admin/integrations",headers:{},query:{nextToken:e.nextToken,limit:e.limit,name:e.name,version:e.version,interfaceId:e.interfaceId,interfaceName:e.interfaceName,installedByBotId:e.installedByBotId,verificationStatus:e.verificationStatus,search:e.search,sortBy:e.sortBy,direction:e.direction,visibility:e.visibility,dev:e.dev},params:{},body:{}});var ch=e=>({path:`/v1/admin/integrations/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var lh=e=>({path:`/v1/admin/integrations/${encodeURIComponent(e.id)}/logs`,headers:{},query:{timeStart:e.timeStart,timeEnd:e.timeEnd,level:e.level,userId:e.userId,conversationId:e.conversationId,messageContains:e.messageContains,nextToken:e.nextToken},params:{id:e.id},body:{}});var yh=e=>({path:`/v1/admin/integrations/${encodeURIComponent(e.name)}/${encodeURIComponent(e.version)}`,headers:{},query:{},params:{name:e.name,version:e.version},body:{}});var mh=e=>({path:`/v1/admin/integrations/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var hh=e=>({path:"/v1/admin/integrations/request-verification",headers:{},query:{},params:{},body:{integrationId:e.integrationId}});var fh=e=>({path:"/v1/admin/interfaces",headers:{},query:{},params:{},body:{name:e.name,version:e.version,entities:e.entities,events:e.events,actions:e.actions,channels:e.channels,nameTemplate:e.nameTemplate,attributes:e.attributes,icon:e.icon,readme:e.readme,title:e.title,description:e.description,public:e.public}});var Ih=e=>({path:`/v1/admin/interfaces/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var Ph=e=>({path:`/v1/admin/interfaces/${encodeURIComponent(e.name)}/${encodeURIComponent(e.version)}`,headers:{},query:{},params:{name:e.name,version:e.version},body:{}});var Ah=e=>({path:`/v1/admin/interfaces/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{entities:e.entities,events:e.events,actions:e.actions,channels:e.channels,nameTemplate:e.nameTemplate,attributes:e.attributes,icon:e.icon,readme:e.readme,title:e.title,description:e.description,public:e.public}});var Bh=e=>({path:`/v1/admin/interfaces/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var wh=e=>({path:"/v1/admin/interfaces",headers:{},query:{nextToken:e.nextToken,name:e.name,version:e.version},params:{},body:{}});var Gh=e=>({path:"/v1/admin/plugins",headers:{},query:{},params:{},body:{name:e.name,version:e.version,configuration:e.configuration,states:e.states,events:e.events,actions:e.actions,user:e.user,conversation:e.conversation,dependencies:e.dependencies,attributes:e.attributes,code:e.code,icon:e.icon,readme:e.readme,title:e.title,description:e.description,public:e.public}});var Lh=e=>({path:`/v1/admin/plugins/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var Sh=e=>({path:`/v1/admin/plugins/${encodeURIComponent(e.name)}/${encodeURIComponent(e.version)}`,headers:{},query:{},params:{name:e.name,version:e.version},body:{}});var Hh=e=>({path:`/v1/admin/plugins/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{configuration:e.configuration,states:e.states,events:e.events,actions:e.actions,user:e.user,conversation:e.conversation,dependencies:e.dependencies,attributes:e.attributes,code:e.code,icon:e.icon,readme:e.readme,title:e.title,description:e.description,public:e.public}});var _h=e=>({path:`/v1/admin/plugins/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var Mh=e=>({path:"/v1/admin/plugins",headers:{},query:{nextToken:e.nextToken,name:e.name,version:e.version},params:{},body:{}});var Vh=e=>({path:`/v1/admin/plugins/${encodeURIComponent(e.id)}/code/${encodeURIComponent(e.platform)}`,headers:{},query:{},params:{id:e.id,platform:e.platform},body:{}});var Kh=e=>({path:`/v1/admin/usages/${encodeURIComponent(e.id)}`,headers:{},query:{type:e.type,period:e.period},params:{id:e.id},body:{}});var jh=e=>({path:"/v1/admin/usages/multiple",headers:{},query:{types:e.types,ids:e.ids,period:e.period},params:{},body:{}});var Jh=e=>({path:`/v1/admin/usages/${encodeURIComponent(e.id)}/history`,headers:{},query:{type:e.type},params:{id:e.id},body:{}});var Zh=e=>({path:`/v1/admin/usages/${encodeURIComponent(e.id)}/activity`,headers:{},query:{type:e.type,timestampFrom:e.timestampFrom,timestampUntil:e.timestampUntil,nextToken:e.nextToken},params:{id:e.id},body:{}});var eb=e=>({path:`/v1/admin/usages/${encodeURIComponent(e.id)}/daily-activity`,headers:{},query:{type:e.type,dateFrom:e.dateFrom,dateUntil:e.dateUntil,nextToken:e.nextToken},params:{id:e.id},body:{}});var sb=e=>({path:"/v1/admin/quotas/ai-spend",headers:{},query:{},params:{},body:{monthlySpendingLimit:e.monthlySpendingLimit}});var ab=e=>({path:"/v1/admin/activities",headers:{},query:{nextToken:e.nextToken,taskId:e.taskId,botId:e.botId},params:{},body:{}});var ob=e=>({path:"/v1/admin/introspect",headers:{},query:{},params:{},body:{botId:e.botId}});var pb=e=>({path:"/v1/files",headers:{},query:{},params:{},body:{key:e.key,tags:e.tags,size:e.size,index:e.index,indexing:e.indexing,accessPolicies:e.accessPolicies,contentType:e.contentType,expiresAt:e.expiresAt,publicContentImmediatelyAccessible:e.publicContentImmediatelyAccessible,metadata:e.metadata}});var cb=e=>({path:`/v1/files/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var lb=e=>({path:"/v1/files",headers:{},query:{nextToken:e.nextToken,sortField:e.sortField,sortDirection:e.sortDirection,tags:e.tags,ids:e.ids},params:{},body:{}});var yb=e=>({path:`/v1/files/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var mb=e=>({path:`/v1/files/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{metadata:e.metadata,tags:e.tags,accessPolicies:e.accessPolicies,expiresAt:e.expiresAt}});var hb=e=>({path:`/v1/files/${encodeURIComponent(e.idOrKey)}/${encodeURIComponent(e.destinationKey)}`,headers:{"x-destination-bot-id":e["x-destination-bot-id"]},query:{},params:{idOrKey:e.idOrKey,destinationKey:e.destinationKey},body:{overwrite:e.overwrite}});var fb=e=>({path:"/v1/files/search",headers:{},query:{tags:e.tags,query:e.query,contextDepth:e.contextDepth,limit:e.limit,consolidate:e.consolidate,includeBreadcrumb:e.includeBreadcrumb},params:{},body:{}});var Ib=e=>({path:`/v1/files/${encodeURIComponent(e.id)}/passages`,headers:{},query:{nextToken:e.nextToken,limit:e.limit},params:{id:e.id},body:{}});var Pb=e=>({path:`/v1/files/${encodeURIComponent(e.id)}/passages`,headers:{},query:{},params:{id:e.id},body:{passages:e.passages}});var Ab=e=>({path:"/v1/files/tags",headers:{},query:{nextToken:e.nextToken},params:{},body:{}});var Bb=e=>({path:`/v1/files/tags/${encodeURIComponent(e.tag)}/values`,headers:{},query:{nextToken:e.nextToken},params:{tag:e.tag},body:{}});var wb=e=>({path:"/v1/files/knowledge-bases",headers:{},query:{},params:{},body:{name:e.name}});var Gb=e=>({path:`/v1/files/knowledge-bases/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var Lb=e=>({path:`/v1/files/knowledge-bases/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{name:e.name}});var Sb=e=>({path:"/v1/files/knowledge-bases",headers:{},query:{nextToken:e.nextToken},params:{},body:{}});var Hb=e=>({path:"/v1/tables",headers:{},query:{tags:e.tags},params:{},body:{}});var _b=e=>({path:`/v1/tables/${encodeURIComponent(e.table)}`,headers:{},query:{},params:{table:e.table},body:{}});var Mb=e=>({path:`/v1/tables/${encodeURIComponent(e.table)}`,headers:{},query:{},params:{table:e.table},body:{factor:e.factor,frozen:e.frozen,schema:e.schema,tags:e.tags,isComputeEnabled:e.isComputeEnabled}});var Vb=e=>({path:"/v1/tables",headers:{},query:{},params:{},body:{name:e.name,factor:e.factor,frozen:e.frozen,schema:e.schema,tags:e.tags,isComputeEnabled:e.isComputeEnabled}});var Kb=e=>({path:`/v1/tables/${encodeURIComponent(e.sourceTableId)}/duplicate`,headers:{},query:{},params:{sourceTableId:e.sourceTableId},body:{tableName:e.tableName,schemaOnly:e.schemaOnly,factor:e.factor}});var jb=e=>({path:`/v1/tables/${encodeURIComponent(e.table)}/export`,headers:{},query:{format:e.format,compress:e.compress},params:{table:e.table},body:{}});var Jb=e=>({path:`/v1/tables/${encodeURIComponent(e.table)}/jobs`,headers:{},query:{},params:{table:e.table},body:{}});var Zb=e=>({path:`/v1/tables/${encodeURIComponent(e.table)}/import`,headers:{},query:{},params:{table:e.table},body:{fileId:e.fileId}});var ef=e=>({path:`/v1/tables/${encodeURIComponent(e.table)}`,headers:{},query:{},params:{table:e.table},body:{name:e.name,frozen:e.frozen,schema:e.schema,tags:e.tags,isComputeEnabled:e.isComputeEnabled}});var sf=e=>({path:`/v1/tables/${encodeURIComponent(e.table)}/column`,headers:{},query:{},params:{table:e.table},body:{name:e.name,newName:e.newName}});var af=e=>({path:`/v1/tables/${encodeURIComponent(e.table)}`,headers:{},query:{},params:{table:e.table},body:{}});var of=e=>({path:`/v1/tables/${encodeURIComponent(e.table)}/row`,headers:{},query:{id:e.id},params:{table:e.table},body:{}});var uf=e=>({path:`/v1/tables/${encodeURIComponent(e.table)}/rows/find`,headers:{},query:{},params:{table:e.table},body:{limit:e.limit,offset:e.offset,filter:e.filter,group:e.group,search:e.search,orderBy:e.orderBy,orderDirection:e.orderDirection}});var df=e=>({path:`/v1/tables/${encodeURIComponent(e.table)}/rows`,headers:{},query:{},params:{table:e.table},body:{rows:e.rows,waitComputed:e.waitComputed}});var gf=e=>({path:`/v1/tables/${encodeURIComponent(e.table)}/rows/delete`,headers:{},query:{},params:{table:e.table},body:{ids:e.ids,filter:e.filter,deleteAllRows:e.deleteAllRows}});var Rf=e=>({path:`/v1/tables/${encodeURIComponent(e.table)}/rows`,headers:{},query:{},params:{table:e.table},body:{rows:e.rows,waitComputed:e.waitComputed}});var qf=e=>({path:`/v1/tables/${encodeURIComponent(e.table)}/rows/upsert`,headers:{},query:{},params:{table:e.table},body:{rows:e.rows,keyColumn:e.keyColumn,waitComputed:e.waitComputed}});var lt=class{constructor(t,r={}){this.axiosInstance=t;this.props=r}createConversation=async t=>{let{path:r,headers:a,query:n,body:o}=Kg(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getConversation=async t=>{let{path:r,headers:a,query:n,body:o}=jg(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};listConversations=async t=>{let{path:r,headers:a,query:n,body:o}=Jg(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getOrCreateConversation=async t=>{let{path:r,headers:a,query:n,body:o}=Zg(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};updateConversation=async t=>{let{path:r,headers:a,query:n,body:o}=ey(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"put",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};deleteConversation=async t=>{let{path:r,headers:a,query:n,body:o}=sy(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"delete",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};listParticipants=async t=>{let{path:r,headers:a,query:n,body:o}=ay(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};addParticipant=async t=>{let{path:r,headers:a,query:n,body:o}=oy(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getParticipant=async t=>{let{path:r,headers:a,query:n,body:o}=py(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};removeParticipant=async t=>{let{path:r,headers:a,query:n,body:o}=cy(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"delete",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};createEvent=async t=>{let{path:r,headers:a,query:n,body:o}=ly(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getEvent=async t=>{let{path:r,headers:a,query:n,body:o}=yy(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};listEvents=async t=>{let{path:r,headers:a,query:n,body:o}=my(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};createMessage=async t=>{let{path:r,headers:a,query:n,body:o}=hy(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getOrCreateMessage=async t=>{let{path:r,headers:a,query:n,body:o}=fy(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getMessage=async t=>{let{path:r,headers:a,query:n,body:o}=Iy(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};updateMessage=async t=>{let{path:r,headers:a,query:n,body:o}=Py(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"put",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};listMessages=async t=>{let{path:r,headers:a,query:n,body:o}=Ay(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};deleteMessage=async t=>{let{path:r,headers:a,query:n,body:o}=By(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"delete",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};createUser=async t=>{let{path:r,headers:a,query:n,body:o}=wy(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getUser=async t=>{let{path:r,headers:a,query:n,body:o}=Gy(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};listUsers=async t=>{let{path:r,headers:a,query:n,body:o}=Ly(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getOrCreateUser=async t=>{let{path:r,headers:a,query:n,body:o}=Sy(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};updateUser=async t=>{let{path:r,headers:a,query:n,body:o}=Hy(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"put",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};deleteUser=async t=>{let{path:r,headers:a,query:n,body:o}=_y(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"delete",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};setStateExpiry=async t=>{let{path:r,headers:a,query:n,body:o}=My(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getState=async t=>{let{path:r,headers:a,query:n,body:o}=Vy(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};setState=async t=>{let{path:r,headers:a,query:n,body:o}=Ky(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getOrSetState=async t=>{let{path:r,headers:a,query:n,body:o}=jy(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};patchState=async t=>{let{path:r,headers:a,query:n,body:o}=Jy(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"patch",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};callAction=async t=>{let{path:r,headers:a,query:n,body:o}=Zy(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};configureIntegration=async t=>{let{path:r,headers:a,query:n,body:o}=eR(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getTask=async t=>{let{path:r,headers:a,query:n,body:o}=sR(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};createTask=async t=>{let{path:r,headers:a,query:n,body:o}=aR(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};updateTask=async t=>{let{path:r,headers:a,query:n,body:o}=oR(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"put",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};deleteTask=async t=>{let{path:r,headers:a,query:n,body:o}=pR(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"delete",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};listTasks=async t=>{let{path:r,headers:a,query:n,body:o}=cR(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};createWorkflow=async t=>{let{path:r,headers:a,query:n,body:o}=lR(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getWorkflow=async t=>{let{path:r,headers:a,query:n,body:o}=yR(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};updateWorkflow=async t=>{let{path:r,headers:a,query:n,body:o}=mR(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"put",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};deleteWorkflow=async t=>{let{path:r,headers:a,query:n,body:o}=hR(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"delete",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};listWorkflows=async t=>{let{path:r,headers:a,query:n,body:o}=fR(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getOrCreateWorkflow=async t=>{let{path:r,headers:a,query:n,body:o}=IR(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};listTagValues=async t=>{let{path:r,headers:a,query:n,body:o}=PR(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};trackAnalytics=async t=>{let{path:r,headers:a,query:n,body:o}=AR(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};runVrl=async t=>{let{path:r,headers:a,query:n,body:o}=BR(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getAccount=async t=>{let{path:r,headers:a,query:n,body:o}=wR(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};updateAccount=async t=>{let{path:r,headers:a,query:n,body:o}=GR(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"put",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};listPersonalAccessTokens=async t=>{let{path:r,headers:a,query:n,body:o}=LR(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};createPersonalAccessToken=async t=>{let{path:r,headers:a,query:n,body:o}=SR(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};deletePersonalAccessToken=async t=>{let{path:r,headers:a,query:n,body:o}=HR(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"delete",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};setAccountPreference=async t=>{let{path:r,headers:a,query:n,body:o}=_R(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getAccountPreference=async t=>{let{path:r,headers:a,query:n,body:o}=MR(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};listPublicIntegrations=async t=>{let{path:r,headers:a,query:n,body:o}=VR(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getPublicIntegrationById=async t=>{let{path:r,headers:a,query:n,body:o}=KR(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getPublicIntegration=async t=>{let{path:r,headers:a,query:n,body:o}=jR(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};listPublicPlugins=async t=>{let{path:r,headers:a,query:n,body:o}=JR(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getPublicPluginById=async t=>{let{path:r,headers:a,query:n,body:o}=ZR(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getPublicPlugin=async t=>{let{path:r,headers:a,query:n,body:o}=em(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getPublicPluginCode=async t=>{let{path:r,headers:a,query:n,body:o}=sm(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};listPublicInterfaces=async t=>{let{path:r,headers:a,query:n,body:o}=am(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getPublicInterfaceById=async t=>{let{path:r,headers:a,query:n,body:o}=om(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getPublicInterface=async t=>{let{path:r,headers:a,query:n,body:o}=pm(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};createBot=async t=>{let{path:r,headers:a,query:n,body:o}=cm(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};updateBot=async t=>{let{path:r,headers:a,query:n,body:o}=lm(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"put",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};transferBot=async t=>{let{path:r,headers:a,query:n,body:o}=ym(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};listBots=async t=>{let{path:r,headers:a,query:n,body:o}=mm(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getBot=async t=>{let{path:r,headers:a,query:n,body:o}=hm(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};deleteBot=async t=>{let{path:r,headers:a,query:n,body:o}=fm(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"delete",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getBotLogs=async t=>{let{path:r,headers:a,query:n,body:o}=Im(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getBotWebchat=async t=>{let{path:r,headers:a,query:n,body:o}=Pm(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getBotAnalytics=async t=>{let{path:r,headers:a,query:n,body:o}=Am(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getBotIssue=async t=>{let{path:r,headers:a,query:n,body:o}=Bm(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};listBotIssues=async t=>{let{path:r,headers:a,query:n,body:o}=wm(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};deleteBotIssue=async t=>{let{path:r,headers:a,query:n,body:o}=Gm(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"delete",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};listBotIssueEvents=async t=>{let{path:r,headers:a,query:n,body:o}=Lm(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};listBotVersions=async t=>{let{path:r,headers:a,query:n,body:o}=Sm(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getBotVersion=async t=>{let{path:r,headers:a,query:n,body:o}=Hm(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};createBotVersion=async t=>{let{path:r,headers:a,query:n,body:o}=_m(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};deployBotVersion=async t=>{let{path:r,headers:a,query:n,body:o}=Mm(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};createIntegrationShareableId=async t=>{let{path:r,headers:a,query:n,body:o}=Vm(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};deleteIntegrationShareableId=async t=>{let{path:r,headers:a,query:n,body:o}=Km(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"delete",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getIntegrationShareableId=async t=>{let{path:r,headers:a,query:n,body:o}=jm(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};unlinkSandboxedConversations=async t=>{let{path:r,headers:a,query:n,body:o}=Jm(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"delete",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};listBotApiKeys=async t=>{let{path:r,headers:a,query:n,body:o}=Zm(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};createBotApiKey=async t=>{let{path:r,headers:a,query:n,body:o}=eq(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};deleteBotApiKey=async t=>{let{path:r,headers:a,query:n,body:o}=sq(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"delete",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};listWorkspaceInvoices=async t=>{let{path:r,headers:a,query:n,body:o}=aq(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getUpcomingInvoice=async t=>{let{path:r,headers:a,query:n,body:o}=oq(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};chargeWorkspaceUnpaidInvoices=async t=>{let{path:r,headers:a,query:n,body:o}=pq(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};createWorkspace=async t=>{let{path:r,headers:a,query:n,body:o}=cq(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getPublicWorkspace=async t=>{let{path:r,headers:a,query:n,body:o}=lq(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getWorkspace=async t=>{let{path:r,headers:a,query:n,body:o}=yq(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};listWorkspaceUsages=async t=>{let{path:r,headers:a,query:n,body:o}=mq(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};breakDownWorkspaceUsageByBot=async t=>{let{path:r,headers:a,query:n,body:o}=hq(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getAllWorkspaceQuotaCompletion=async t=>{let{path:r,headers:a,query:n,body:o}=fq(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getWorkspaceQuota=async t=>{let{path:r,headers:a,query:n,body:o}=Iq(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};listWorkspaceQuotas=async t=>{let{path:r,headers:a,query:n,body:o}=Pq(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};updateWorkspace=async t=>{let{path:r,headers:a,query:n,body:o}=Aq(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"put",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};checkHandleAvailability=async t=>{let{path:r,headers:a,query:n,body:o}=Bq(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"put",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};listWorkspaces=async t=>{let{path:r,headers:a,query:n,body:o}=wq(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};listPublicWorkspaces=async t=>{let{path:r,headers:a,query:n,body:o}=Gq(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};deleteWorkspace=async t=>{let{path:r,headers:a,query:n,body:o}=Lq(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"delete",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getAuditRecords=async t=>{let{path:r,headers:a,query:n,body:o}=Sq(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};listWorkspaceMembers=async t=>{let{path:r,headers:a,query:n,body:o}=Hq(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getWorkspaceMember=async t=>{let{path:r,headers:a,query:n,body:o}=_q(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};deleteWorkspaceMember=async t=>{let{path:r,headers:a,query:n,body:o}=Mq(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"delete",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};createWorkspaceMember=async t=>{let{path:r,headers:a,query:n,body:o}=Vq(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};updateWorkspaceMember=async t=>{let{path:r,headers:a,query:n,body:o}=Kq(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"put",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};listIntegrationApiKeys=async t=>{let{path:r,headers:a,query:n,body:o}=jq(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};createIntegrationApiKey=async t=>{let{path:r,headers:a,query:n,body:o}=Jq(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};deleteIntegrationApiKey=async t=>{let{path:r,headers:a,query:n,body:o}=Zq(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"delete",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};createIntegration=async t=>{let{path:r,headers:a,query:n,body:o}=eh(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};validateIntegrationCreation=async t=>{let{path:r,headers:a,query:n,body:o}=sh(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};updateIntegration=async t=>{let{path:r,headers:a,query:n,body:o}=ah(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"put",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};validateIntegrationUpdate=async t=>{let{path:r,headers:a,query:n,body:o}=oh(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"put",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};listIntegrations=async t=>{let{path:r,headers:a,query:n,body:o}=ph(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getIntegration=async t=>{let{path:r,headers:a,query:n,body:o}=ch(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getIntegrationLogs=async t=>{let{path:r,headers:a,query:n,body:o}=lh(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getIntegrationByName=async t=>{let{path:r,headers:a,query:n,body:o}=yh(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};deleteIntegration=async t=>{let{path:r,headers:a,query:n,body:o}=mh(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"delete",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};requestIntegrationVerification=async t=>{let{path:r,headers:a,query:n,body:o}=hh(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};createInterface=async t=>{let{path:r,headers:a,query:n,body:o}=fh(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getInterface=async t=>{let{path:r,headers:a,query:n,body:o}=Ih(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getInterfaceByName=async t=>{let{path:r,headers:a,query:n,body:o}=Ph(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};updateInterface=async t=>{let{path:r,headers:a,query:n,body:o}=Ah(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"put",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};deleteInterface=async t=>{let{path:r,headers:a,query:n,body:o}=Bh(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"delete",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};listInterfaces=async t=>{let{path:r,headers:a,query:n,body:o}=wh(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};createPlugin=async t=>{let{path:r,headers:a,query:n,body:o}=Gh(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getPlugin=async t=>{let{path:r,headers:a,query:n,body:o}=Lh(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getPluginByName=async t=>{let{path:r,headers:a,query:n,body:o}=Sh(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};updatePlugin=async t=>{let{path:r,headers:a,query:n,body:o}=Hh(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"put",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};deletePlugin=async t=>{let{path:r,headers:a,query:n,body:o}=_h(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"delete",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};listPlugins=async t=>{let{path:r,headers:a,query:n,body:o}=Mh(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getPluginCode=async t=>{let{path:r,headers:a,query:n,body:o}=Vh(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getUsage=async t=>{let{path:r,headers:a,query:n,body:o}=Kh(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getMultipleUsages=async t=>{let{path:r,headers:a,query:n,body:o}=jh(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};listUsageHistory=async t=>{let{path:r,headers:a,query:n,body:o}=Jh(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};listUsageActivity=async t=>{let{path:r,headers:a,query:n,body:o}=Zh(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};listUsageActivityDaily=async t=>{let{path:r,headers:a,query:n,body:o}=eb(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};changeAISpendQuota=async t=>{let{path:r,headers:a,query:n,body:o}=sb(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"put",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};listActivities=async t=>{let{path:r,headers:a,query:n,body:o}=ab(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};introspect=async t=>{let{path:r,headers:a,query:n,body:o}=ob(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};upsertFile=async t=>{let{path:r,headers:a,query:n,body:o}=pb(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"put",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};deleteFile=async t=>{let{path:r,headers:a,query:n,body:o}=cb(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"delete",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};listFiles=async t=>{let{path:r,headers:a,query:n,body:o}=lb(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getFile=async t=>{let{path:r,headers:a,query:n,body:o}=yb(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};updateFileMetadata=async t=>{let{path:r,headers:a,query:n,body:o}=mb(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"put",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};copyFile=async t=>{let{path:r,headers:a,query:n,body:o}=hb(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};searchFiles=async t=>{let{path:r,headers:a,query:n,body:o}=fb(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};listFilePassages=async t=>{let{path:r,headers:a,query:n,body:o}=Ib(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};setFilePassages=async t=>{let{path:r,headers:a,query:n,body:o}=Pb(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"put",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};listFileTags=async t=>{let{path:r,headers:a,query:n,body:o}=Ab(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};listFileTagValues=async t=>{let{path:r,headers:a,query:n,body:o}=Bb(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};createKnowledgeBase=async t=>{let{path:r,headers:a,query:n,body:o}=wb(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};deleteKnowledgeBase=async t=>{let{path:r,headers:a,query:n,body:o}=Gb(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"delete",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};updateKnowledgeBase=async t=>{let{path:r,headers:a,query:n,body:o}=Lb(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"put",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};listKnowledgeBases=async t=>{let{path:r,headers:a,query:n,body:o}=Sb(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};listTables=async t=>{let{path:r,headers:a,query:n,body:o}=Hb(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getTable=async t=>{let{path:r,headers:a,query:n,body:o}=_b(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getOrCreateTable=async t=>{let{path:r,headers:a,query:n,body:o}=Mb(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};createTable=async t=>{let{path:r,headers:a,query:n,body:o}=Vb(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};duplicateTable=async t=>{let{path:r,headers:a,query:n,body:o}=Kb(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};exportTable=async t=>{let{path:r,headers:a,query:n,body:o}=jb(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getTableJobs=async t=>{let{path:r,headers:a,query:n,body:o}=Jb(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};importTable=async t=>{let{path:r,headers:a,query:n,body:o}=Zb(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};updateTable=async t=>{let{path:r,headers:a,query:n,body:o}=ef(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"put",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};renameTableColumn=async t=>{let{path:r,headers:a,query:n,body:o}=sf(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"put",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};deleteTable=async t=>{let{path:r,headers:a,query:n,body:o}=af(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"delete",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};getTableRow=async t=>{let{path:r,headers:a,query:n,body:o}=of(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"get",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};findTableRows=async t=>{let{path:r,headers:a,query:n,body:o}=uf(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};createTableRows=async t=>{let{path:r,headers:a,query:n,body:o}=df(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};deleteTableRows=async t=>{let{path:r,headers:a,query:n,body:o}=gf(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};updateTableRows=async t=>{let{path:r,headers:a,query:n,body:o}=Rf(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"put",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})};upsertTableRows=async t=>{let{path:r,headers:a,query:n,body:o}=qf(t),i=this.props.toAxiosRequest??c,u=this.props.toApiError??d,p=i({method:"post",path:r,headers:{...a},query:{...n},body:o});return this.axiosInstance.request(p).then(s=>s.data).catch(s=>{throw u(s)})}};function d(e){return oP.isAxiosError(e)&&e.response?.data?he(e.response.data):he(e)}var bf=class extends lt{config;constructor(t={}){let r=_.getClientConfig(t),a=F.createAxios(r),n=pP.create(a);super(n,{toApiError:M.toApiError}),t.retry&&O(n,t.retry),this.config=r}get list(){return{conversations:t=>new h.AsyncCollection(({nextToken:r})=>this.listConversations({nextToken:r,...t}).then(a=>({...a,items:a.conversations}))),participants:t=>new h.AsyncCollection(({nextToken:r})=>this.listParticipants({nextToken:r,...t}).then(a=>({...a,items:a.participants}))),events:t=>new h.AsyncCollection(({nextToken:r})=>this.listEvents({nextToken:r,...t}).then(a=>({...a,items:a.events}))),messages:t=>new h.AsyncCollection(({nextToken:r})=>this.listMessages({nextToken:r,...t}).then(a=>({...a,items:a.messages}))),users:t=>new h.AsyncCollection(({nextToken:r})=>this.listUsers({nextToken:r,...t}).then(a=>({...a,items:a.users}))),tasks:t=>new h.AsyncCollection(({nextToken:r})=>this.listTasks({nextToken:r,...t}).then(a=>({...a,items:a.tasks}))),publicIntegrations:t=>new h.AsyncCollection(({nextToken:r})=>this.listPublicIntegrations({nextToken:r,...t}).then(a=>({...a,items:a.integrations}))),bots:t=>new h.AsyncCollection(({nextToken:r})=>this.listBots({nextToken:r,...t}).then(a=>({...a,items:a.bots}))),botIssues:t=>new h.AsyncCollection(({nextToken:r})=>this.listBotIssues({nextToken:r,...t}).then(a=>({...a,items:a.issues}))),workspaces:t=>new h.AsyncCollection(({nextToken:r})=>this.listWorkspaces({nextToken:r,...t}).then(a=>({...a,items:a.workspaces}))),publicWorkspaces:t=>new h.AsyncCollection(({nextToken:r})=>this.listPublicWorkspaces({nextToken:r,...t}).then(a=>({...a,items:a.workspaces}))),workspaceMembers:t=>new h.AsyncCollection(({nextToken:r})=>this.listWorkspaceMembers({nextToken:r,...t}).then(a=>({...a,items:a.members}))),integrations:t=>new h.AsyncCollection(({nextToken:r})=>this.listIntegrations({nextToken:r,...t}).then(a=>({...a,items:a.integrations}))),interfaces:t=>new h.AsyncCollection(({nextToken:r})=>this.listInterfaces({nextToken:r,...t}).then(a=>({...a,items:a.interfaces}))),activities:t=>new h.AsyncCollection(({nextToken:r})=>this.listActivities({nextToken:r,...t}).then(a=>({...a,items:a.activities}))),files:t=>new h.AsyncCollection(({nextToken:r})=>this.listFiles({nextToken:r,...t}).then(a=>({...a,items:a.files}))),filePassages:t=>new h.AsyncCollection(({nextToken:r})=>this.listFilePassages({nextToken:r,...t}).then(a=>({...a,items:a.passages}))),usageActivity:t=>new h.AsyncCollection(({nextToken:r})=>this.listUsageActivity({nextToken:r,...t}).then(a=>({...a,items:a.data}))),usageActivityDaily:t=>new h.AsyncCollection(({nextToken:r})=>this.listUsageActivityDaily({nextToken:r,...t}).then(a=>({...a,items:a.data})))}}uploadFile=async t=>await ct(this,t)};export{Qt as AlreadyExistsError,Mt as BreakingChangesError,bf as Client,It as ForbiddenError,ft as InternalError,Ct as InvalidDataFormatError,wt as InvalidIdentifierError,Bt as InvalidJsonSchemaError,Pt as InvalidPayloadError,Wt as InvalidQueryError,Ft as LimitExceededError,At as MethodNotFoundError,kt as PayloadTooLargeError,Dt as PaymentRequiredError,_t as QuotaExceededError,Ht as RateLimitedError,Gt as ReferenceConstraintError,Lt as ReferenceNotFoundError,Ut as RelationConflictError,Et as ResourceLockedConflictError,Tt as ResourceNotFoundError,St as RuntimeError,xt as UnauthorizedError,pe as UnknownError,vt as UnsupportedMediaTypeError,re as UploadFileError,Bl as admin,RA as axios,va as axiosRetry,he as errorFrom,pg as files,Jf as isApiError,Kp as runtime,Vg as tables};
//# sourceMappingURL=index.mjs.map
