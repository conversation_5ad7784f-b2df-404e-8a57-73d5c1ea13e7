# Botpress Studio Integration Guide

## Overview
This guide will help you connect your Botpress Studio chatbot to your MySQL database through your backend API.

## Backend Setup (Already Complete ✅)

Your backend is already configured with:
- Webhook endpoint: `http://localhost:8080/api/botpress/webhook`
- Alternative endpoint: `http://localhost:8080/api/botpress-reservations`
- Database tables: `reservations` and `botpress_reservations`

## Botpress Studio Configuration

### Step 1: Create Custom Action in Botpress Studio

1. **Open your Botpress Studio**
2. **Go to Code Editor** (in the left sidebar)
3. **Create a new file**: `src/actions/createReservation.js`

### Step 2: Add the Custom Action Code

```javascript
/**
 * Create a restaurant reservation
 * @title Create Reservation
 * @category Restaurant
 * <AUTHOR> Restaurant
 * @param {string} customerName - Customer's full name
 * @param {string} email - Customer's email address
 * @param {string} phone - Customer's phone number
 * @param {string} date - Reservation date (YYYY-MM-DD)
 * @param {string} time - Reservation time (HH:MM)
 * @param {number} partySize - Number of people
 * @param {string} specialRequests - Special requests or notes
 */
const createReservation = async ({ customerName, email, phone, date, time, partySize, specialRequests }) => {
  const axios = require('axios');
  
  try {
    console.log('Creating reservation with data:', {
      customerName, email, phone, date, time, partySize, specialRequests
    });

    // Your backend API endpoint
    const API_URL = 'http://localhost:8080/api/botpress-reservations';
    
    // Prepare reservation data
    const reservationData = {
      email: email,
      datetime: `${date}T${time}:00`,
      partySize: parseInt(partySize),
      specialRequests: specialRequests || '',
      customerName: customerName,
      phone: phone || ''
    };

    // Make API call to your backend
    const response = await axios.post(API_URL, reservationData, {
      headers: {
        'Content-Type': 'application/json'
      },
      timeout: 10000 // 10 second timeout
    });

    console.log('Reservation created successfully:', response.data);

    return {
      success: true,
      reservationId: response.data.reservationId,
      botpressReservationId: response.data.id,
      message: 'Your reservation has been confirmed!',
      data: response.data
    };

  } catch (error) {
    console.error('Error creating reservation:', error);
    
    return {
      success: false,
      error: error.message,
      message: 'Sorry, there was an error creating your reservation. Please try again or contact us directly.'
    };
  }
};

module.exports = { createReservation };
```

### Step 3: Create Conversation Flow

1. **Go to Flows** in Botpress Studio
2. **Create a new flow** called "Restaurant Reservation"
3. **Add the following nodes:**

#### Node 1: Welcome Message
```
Type: Text
Content: "Welcome to Abraham Restaurant! I'd be happy to help you make a reservation. Let's get started!"
```

#### Node 2: Collect Customer Name
```
Type: Capture Information
Variable: customerName
Question: "What's your full name?"
```

#### Node 3: Collect Email
```
Type: Capture Information  
Variable: email
Question: "What's your email address?"
```

#### Node 4: Collect Phone
```
Type: Capture Information
Variable: phone  
Question: "What's your phone number?"
```

#### Node 5: Collect Date
```
Type: Capture Information
Variable: date
Question: "What date would you like to make a reservation? (Please use YYYY-MM-DD format, e.g., 2024-12-25)"
```

#### Node 6: Collect Time
```
Type: Capture Information
Variable: time
Question: "What time would you prefer? (Please use HH:MM format, e.g., 19:30)"
```

#### Node 7: Collect Party Size
```
Type: Capture Information
Variable: partySize
Question: "How many people will be dining with us?"
```

#### Node 8: Collect Special Requests
```
Type: Capture Information
Variable: specialRequests
Question: "Do you have any special requests or dietary requirements? (Optional - you can say 'none' if not)"
```

#### Node 9: Execute Action
```
Type: Execute Code
Action: createReservation
Parameters:
- customerName: {{event.state.customerName}}
- email: {{event.state.email}}
- phone: {{event.state.phone}}
- date: {{event.state.date}}
- time: {{event.state.time}}
- partySize: {{event.state.partySize}}
- specialRequests: {{event.state.specialRequests}}
```

#### Node 10: Success Response
```
Type: Text
Content: "Perfect! Your reservation has been confirmed. Here are the details:

📅 Date: {{event.state.date}}
🕐 Time: {{event.state.time}}
👥 Party Size: {{event.state.partySize}}
📧 Email: {{event.state.email}}
📞 Phone: {{event.state.phone}}

We look forward to serving you at Abraham Restaurant!"
```

#### Node 11: Error Response
```
Type: Text
Content: "I apologize, but there was an issue creating your reservation. Please try again or call us directly at (*************."
```

### Step 4: Set Up Conditions

Between Node 9 (Execute Action) and the response nodes, add a **Router** node with conditions:

**Success Condition:**
```
Condition: {{temp.actionResult.success}} === true
Goes to: Node 10 (Success Response)
```

**Error Condition:**
```
Condition: {{temp.actionResult.success}} === false
Goes to: Node 11 (Error Response)
```

### Step 5: Test the Integration

1. **Publish your bot** in Botpress Studio
2. **Test in the emulator** first
3. **Test on your website** using the embedded chatbot

## Testing Commands

You can test the backend endpoints directly:

```bash
# Test the Botpress reservation endpoint
curl -X POST http://localhost:8080/api/botpress-reservations \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "datetime": "2024-12-25T19:30:00",
    "partySize": 4,
    "specialRequests": "Window seat please"
  }'
```

## Troubleshooting

1. **Check backend logs** for any errors
2. **Verify database connection** is working
3. **Ensure your bot is published** in Botpress Studio
4. **Check network connectivity** between Botpress Cloud and your localhost

## Production Deployment

For production, you'll need to:
1. Deploy your backend to a public server (Heroku, AWS, etc.)
2. Update the API_URL in your Botpress action to use the public URL
3. Configure proper CORS settings
4. Set up SSL/HTTPS for secure communication
