# Browser or Node.js

[![Build Status](https://travis-ci.org/flexdinesh/browser-or-node.svg?branch=master)](https://travis-ci.org/flexdinesh/browser-or-node)
[![npm version](https://badge.fury.io/js/browser-or-node.svg)](https://www.npmjs.com/package/browser-or-node)
[![License: MIT](https://img.shields.io/badge/License-MIT-blue.svg)](https://opensource.org/licenses/MIT)

Check whether the code is running in the browser or node.js runtime.

## Install

```
$ npm install --save browser-or-node
```

## Usage

ES6 style import

```js
import { isBrowser, isNode, isWebWorker, isJsDom, isDeno } from "browser-or-node";

if (isBrowser) {
  // do browser only stuff
}

if (isNode) {
  // do node.js only stuff
}

if (isWebWorker) {
  // do web worker only stuff
}

if (isJsDom) {
  // do jsdom only stuff
}

if (isDeno) {
  // do deno only stuff
}
```

ES5 style import

```js
var jsEnv = require("browser-or-node");

if (jsEnv.isBrowser) {
  // do browser only stuff
}

if (jsEnv.isNode) {
  // do node.js only stuff
}

if (jsEnv.isWebWorker) {
  // do web worker only stuff
}

if (jsEnv.isJsDom) {
  // do jsdom only stuff
}

if (jsEnv.isDeno) {
  // do deno only stuff
}
```

## License

MIT © Dineshkumar Pandiyan