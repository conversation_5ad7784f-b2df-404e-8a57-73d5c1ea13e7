/**
 * <PERSON> Restaurant - Working Botpress Action
 * This version works with your local IP address (no ngrok needed)
 * 
 * Your Local IP Addresses Found:
 * - ************ (VirtualBox/VMware network)
 * - ************ (Main network - USE THIS ONE)
 * 
 * COPY THIS CODE TO BOTPRESS STUDIO:
 * File: src/actions/saveReservationToMySQL.js
 */

const saveReservationToMySQL = async ({ confNumber, dateTime, email, partySize, customerName, phone, specialRequests }) => {
  const axios = require('axios');
  
  try {
    console.log('🍽️ Abraham Restaurant - Saving to MySQL Database');
    console.log('Received from Botpress:', { confNumber, dateTime, email, partySize, customerName, phone, specialRequests });

    // Using your local network IP address (no ngrok needed!)
    // Your backend is running on: http://************:8080
    const API_URL = 'http://************:8080/api/botpress-reservations';
    
    // Validate required fields from your Botpress workflow
    if (!email || !dateTime || !partySize) {
      throw new Error('Missing required fields from Botpress: email, dateTime, partySize');
    }

    // Parse dateTime from Botpress format
    let parsedDate, parsedTime;
    
    try {
      const dateTimeObj = new Date(dateTime);
      if (isNaN(dateTimeObj.getTime())) {
        throw new Error('Invalid dateTime format');
      }
      
      // Convert to MySQL format
      parsedDate = dateTimeObj.toISOString().split('T')[0]; // YYYY-MM-DD
      parsedTime = dateTimeObj.toTimeString().split(' ')[0].substring(0, 5); // HH:MM
    } catch (dateError) {
      console.error('Date parsing error:', dateError);
      throw new Error('Could not parse dateTime from Botpress');
    }

    // Prepare data for Abraham Restaurant MySQL database
    const reservationData = {
      customerName: customerName || email.split('@')[0] || 'Botpress Customer',
      email: email,
      phone: phone || '',
      date: parsedDate,
      time: parsedTime,
      datetime: dateTime, // Keep original for botpress_reservations table
      partySize: parseInt(partySize),
      specialRequests: specialRequests || ''
    };

    console.log('📤 Sending to Abraham Restaurant MySQL via local network');
    console.log('📝 API URL:', API_URL);
    console.log('📝 Formatted reservation data:', reservationData);

    // Send to your existing Abraham Restaurant API
    const response = await axios.post(API_URL, reservationData, {
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'User-Agent': 'Botpress-Abraham-Restaurant-v1.0'
      },
      timeout: 15000 // 15 second timeout
    });

    console.log('✅ SUCCESS: Reservation saved to Abraham Restaurant MySQL!');
    console.log('📊 Database response:', response.data);

    // Return success response to Botpress chatbot
    return {
      success: true,
      reservationId: response.data.reservationId,
      botpressId: response.data.id,
      confNumber: confNumber || `AR-${Date.now()}`,
      message: `🎉 Perfect! Your reservation has been confirmed and saved to Abraham Restaurant's system!\n\n📅 Date: ${parsedDate}\n⏰ Time: ${parsedTime}\n👥 Party Size: ${partySize} people\n📧 Email: ${email}\n🔢 Confirmation Number: ${confNumber || 'AR-' + Date.now()}\n\nWe look forward to serving you at Abraham Restaurant! 🍽️`,
      details: {
        mysqlReservationId: response.data.reservationId,
        botpressReservationId: response.data.id,
        confirmationNumber: confNumber || `AR-${Date.now()}`,
        customerName: reservationData.customerName,
        email: reservationData.email,
        phone: reservationData.phone,
        date: parsedDate,
        time: parsedTime,
        partySize: reservationData.partySize,
        specialRequests: reservationData.specialRequests,
        savedAt: new Date().toISOString(),
        apiUrl: API_URL
      }
    };

  } catch (error) {
    console.error('❌ ERROR: Failed to save reservation to Abraham Restaurant MySQL');
    console.error('Error type:', error.name);
    console.error('Error message:', error.message);
    console.error('Full error:', error);
    
    // Return error response to Botpress chatbot
    return {
      success: false,
      error: error.message,
      confNumber: confNumber || `AR-ERROR-${Date.now()}`,
      message: `I apologize, but there was a technical issue saving your reservation to our main system.\n\n📝 Your reservation details have been recorded:\n📧 Email: ${email}\n📅 Date & Time: ${dateTime}\n👥 Party Size: ${partySize} people\n\n📞 Please call Abraham Restaurant at (************* to confirm your reservation manually.\n\n🔢 Reference Number: ${confNumber || 'AR-ERROR-' + Date.now()}\n\nWe apologize for the inconvenience and look forward to serving you!`
    };
  }
};

module.exports = { saveReservationToMySQL };

/*
SETUP INSTRUCTIONS:

1. ✅ Your backend is already running on port 8080
2. ✅ Your local IP is ************ (already configured above)
3. ✅ Copy this code to Botpress Studio: src/actions/saveReservationToMySQL.js
4. ✅ Add Execute Code node to your flow with parameters:
   - confNumber: {{event.state.confNumber}}
   - dateTime: {{event.state.dateTime}}
   - email: {{event.state.email}}
   - partySize: {{event.state.partySize}}
   - customerName: {{event.state.customerName}}
   - phone: {{event.state.phone}}
   - specialRequests: {{event.state.specialRequests}}

5. ✅ Add Router node:
   - Success: {{temp.actionResult.success}} === true
   - Error: {{temp.actionResult.success}} === false

6. ✅ Add response messages:
   - Success: {{temp.actionResult.message}}
   - Error: {{temp.actionResult.message}}

7. ✅ Test your chatbot!

NO NGROK NEEDED! This works directly with your local network.
*/
