// Botpress Direct MySQL Storage
// This file can be used in Botpress Execute Code nodes to store reservations directly to MySQL

async function action(input: never): Promise<void> {
  /** Direct MySQL storage for Botpress reservations */
  
  try {
    console.log('🍽️ Abraham Restaurant - Direct MySQL Storage')
    console.log('Received from Botpress:', { 
      confNumber: workflow.confNumber, 
      dateTime: workflow.dateTime, 
      email: workflow.email, 
      partySize: workflow.partySize 
    })

    // Validate required fields
    if (!workflow.email || !workflow.dateTime || !workflow.partySize) {
      throw new Error('Missing required fields: email, dateTime, partySize')
    }

    // Parse datetime
    const dateTimeObj = new Date(workflow.dateTime)
    const date = dateTimeObj.toISOString().split('T')[0] // YYYY-MM-DD
    const time = dateTimeObj.toTimeString().split(' ')[0].substring(0, 5) // HH:MM

    // Generate customer name from email if not provided
    const customerName = workflow.customerName || workflow.email.split('@')[0] || 'Botpress User'
    
    // Generate confirmation number if not provided
    const confNumber = workflow.confNumber || `AR-${Date.now()}`

    console.log('📝 Processed data:', {
      customerName,
      email: workflow.email,
      date,
      time,
      partySize: workflow.partySize,
      confNumber
    })

    // MySQL connection configuration
    const mysql = require('mysql2/promise')
    
    const connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: 'khoivinh2004', // Your MySQL password
      database: 'abraham_restaurant'
    })

    console.log('🔗 Connected to MySQL database')

    // Insert into main reservations table
    const reservationSql = `
      INSERT INTO reservations 
      (user_id, customer_name, phone, email, date, time, party_size, special_requests, status) 
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
    `
    
    const [reservationResult] = await connection.execute(reservationSql, [
      'botpress', // user_id
      customerName,
      workflow.phone || '', // phone (optional)
      workflow.email,
      date,
      time,
      parseInt(workflow.partySize),
      workflow.specialRequests || '', // special_requests (optional)
      'pending' // status
    ])

    const reservationId = reservationResult.insertId
    console.log('✅ Created main reservation with ID:', reservationId)

    // Insert into botpress_reservations table for tracking
    const botpressSql = `
      INSERT INTO botpress_reservations 
      (email, datetime, party_size, reservation_id, status, source) 
      VALUES (?, ?, ?, ?, ?, ?)
    `
    
    const [botpressResult] = await connection.execute(botpressSql, [
      workflow.email,
      workflow.dateTime, // Store original datetime
      parseInt(workflow.partySize),
      reservationId,
      'pending',
      'botpress'
    ])

    const botpressReservationId = botpressResult.insertId
    console.log('✅ Created Botpress reservation with ID:', botpressReservationId)

    // Close database connection
    await connection.end()
    console.log('🔒 Database connection closed')

    // Set success response in workflow
    workflow.success = true
    workflow.reservationId = reservationId
    workflow.botpressId = botpressReservationId
    workflow.confNumber = confNumber
    workflow.message = `🎉 Perfect! Your reservation has been confirmed!\n\n📅 Date & Time: ${workflow.dateTime}\n👥 Party Size: ${workflow.partySize} people\n📧 Email: ${workflow.email}\n🔢 Confirmation: ${confNumber}\n\nWe look forward to serving you at Abraham Restaurant! 🍽️`
    
    console.log('✅ SUCCESS: Reservation saved directly to MySQL!')
    console.log('📊 Reservation ID:', reservationId)
    console.log('📊 Botpress ID:', botpressReservationId)
    console.log('📊 Confirmation:', confNumber)

  } catch (error) {
    console.error('❌ ERROR: Failed to save reservation to MySQL')
    console.error('Error message:', error.message)
    console.error('Full error:', error)

    // Set error response in workflow
    workflow.success = false
    workflow.error = error.message
    workflow.confNumber = workflow.confNumber || `AR-ERROR-${Date.now()}`
    workflow.message = `I apologize, but there was a technical issue saving your reservation.\n\n📝 Your details:\n📧 Email: ${workflow.email}\n📅 Date & Time: ${workflow.dateTime}\n👥 Party Size: ${workflow.partySize}\n\n📞 Please call us at (555) 123-4567 to confirm manually.\n\n🔢 Reference: ${workflow.confNumber || 'AR-ERROR-' + Date.now()}`
  }
}
