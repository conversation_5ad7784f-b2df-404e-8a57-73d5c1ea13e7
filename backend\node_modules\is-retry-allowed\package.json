{"name": "is-retry-allowed", "version": "2.2.0", "description": "Check whether a request can be retried based on the `error.code`", "license": "MIT", "repository": "sindresorhus/is-retry-allowed", "funding": {"url": "https://github.com/sponsors/sindresorhus"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=10"}, "scripts": {"test": "xo && ava && tsd"}, "files": ["index.js", "index.d.ts"], "keywords": ["retry", "retries", "allowed", "check", "http", "https", "request", "fetch"], "devDependencies": {"ava": "^3.14.0", "tsd": "^0.14.0", "xo": "^0.36.1"}}