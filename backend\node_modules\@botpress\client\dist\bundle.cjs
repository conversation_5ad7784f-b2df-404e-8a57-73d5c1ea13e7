"use strict";var aP=Object.create;var as=Object.defineProperty;var nP=Object.getOwnPropertyDescriptor;var oP=Object.getOwnPropertyNames;var iP=Object.getPrototypeOf,pP=Object.prototype.hasOwnProperty;var b=(e,t)=>()=>(t||e((t={exports:{}}).exports,t),t.exports),he=(e,t)=>{for(var s in t)as(e,s,{get:t[s],enumerable:!0})},Jo=(e,t,s,r)=>{if(t&&typeof t=="object"||typeof t=="function")for(let n of oP(t))!pP.call(e,n)&&n!==s&&as(e,n,{get:()=>t[n],enumerable:!(r=nP(t,n))||r.enumerable});return e};var Q=(e,t,s)=>(s=e!=null?aP(iP(e)):{},Jo(t||!e||!e.__esModule?as(s,"default",{value:e,enumerable:!0}):s,e)),cP=e=>Jo(as({},"__esModule",{value:!0}),e);var ui=b((MU,ci)=>{var pi=require("stream").Stream,NP=require("util");ci.exports=ke;function ke(){this.source=null,this.dataSize=0,this.maxDataSize=1024*1024,this.pauseStream=!0,this._maxDataSizeExceeded=!1,this._released=!1,this._bufferedEvents=[]}NP.inherits(ke,pi);ke.create=function(e,t){var s=new this;t=t||{};for(var r in t)s[r]=t[r];s.source=e;var n=e.emit;return e.emit=function(){return s._handleEmit(arguments),n.apply(e,arguments)},e.on("error",function(){}),s.pauseStream&&e.pause(),s};Object.defineProperty(ke.prototype,"readable",{configurable:!0,enumerable:!0,get:function(){return this.source.readable}});ke.prototype.setEncoding=function(){return this.source.setEncoding.apply(this.source,arguments)};ke.prototype.resume=function(){this._released||this.release(),this.source.resume()};ke.prototype.pause=function(){this.source.pause()};ke.prototype.release=function(){this._released=!0,this._bufferedEvents.forEach(function(e){this.emit.apply(this,e)}.bind(this)),this._bufferedEvents=[]};ke.prototype.pipe=function(){var e=pi.prototype.pipe.apply(this,arguments);return this.resume(),e};ke.prototype._handleEmit=function(e){if(this._released){this.emit.apply(this,e);return}e[0]==="data"&&(this.dataSize+=e[1].length,this._checkIfMaxDataSizeExceeded()),this._bufferedEvents.push(e)};ke.prototype._checkIfMaxDataSizeExceeded=function(){if(!this._maxDataSizeExceeded&&!(this.dataSize<=this.maxDataSize)){this._maxDataSizeExceeded=!0;var e="DelayedStream#maxDataSize of "+this.maxDataSize+" bytes exceeded.";this.emit("error",new Error(e))}}});var mi=b((NU,gi)=>{var VP=require("util"),li=require("stream").Stream,di=ui();gi.exports=N;function N(){this.writable=!1,this.readable=!0,this.dataSize=0,this.maxDataSize=2*1024*1024,this.pauseStreams=!0,this._released=!1,this._streams=[],this._currentStream=null,this._insideLoop=!1,this._pendingNext=!1}VP.inherits(N,li);N.create=function(e){var t=new this;e=e||{};for(var s in e)t[s]=e[s];return t};N.isStreamLike=function(e){return typeof e!="function"&&typeof e!="string"&&typeof e!="boolean"&&typeof e!="number"&&!Buffer.isBuffer(e)};N.prototype.append=function(e){var t=N.isStreamLike(e);if(t){if(!(e instanceof di)){var s=di.create(e,{maxDataSize:1/0,pauseStream:this.pauseStreams});e.on("data",this._checkDataSize.bind(this)),e=s}this._handleErrors(e),this.pauseStreams&&e.pause()}return this._streams.push(e),this};N.prototype.pipe=function(e,t){return li.prototype.pipe.call(this,e,t),this.resume(),e};N.prototype._getNext=function(){if(this._currentStream=null,this._insideLoop){this._pendingNext=!0;return}this._insideLoop=!0;try{do this._pendingNext=!1,this._realGetNext();while(this._pendingNext)}finally{this._insideLoop=!1}};N.prototype._realGetNext=function(){var e=this._streams.shift();if(typeof e>"u"){this.end();return}if(typeof e!="function"){this._pipeNext(e);return}var t=e;t(function(s){var r=N.isStreamLike(s);r&&(s.on("data",this._checkDataSize.bind(this)),this._handleErrors(s)),this._pipeNext(s)}.bind(this))};N.prototype._pipeNext=function(e){this._currentStream=e;var t=N.isStreamLike(e);if(t){e.on("end",this._getNext.bind(this)),e.pipe(this,{end:!1});return}var s=e;this.write(s),this._getNext()};N.prototype._handleErrors=function(e){var t=this;e.on("error",function(s){t._emitError(s)})};N.prototype.write=function(e){this.emit("data",e)};N.prototype.pause=function(){this.pauseStreams&&(this.pauseStreams&&this._currentStream&&typeof this._currentStream.pause=="function"&&this._currentStream.pause(),this.emit("pause"))};N.prototype.resume=function(){this._released||(this._released=!0,this.writable=!0,this._getNext()),this.pauseStreams&&this._currentStream&&typeof this._currentStream.resume=="function"&&this._currentStream.resume(),this.emit("resume")};N.prototype.end=function(){this._reset(),this.emit("end")};N.prototype.destroy=function(){this._reset(),this.emit("close")};N.prototype._reset=function(){this.writable=!1,this._streams=[],this._currentStream=null};N.prototype._checkDataSize=function(){if(this._updateDataSize(),!(this.dataSize<=this.maxDataSize)){var e="DelayedStream#maxDataSize of "+this.maxDataSize+" bytes exceeded.";this._emitError(new Error(e))}};N.prototype._updateDataSize=function(){this.dataSize=0;var e=this;this._streams.forEach(function(t){t.dataSize&&(e.dataSize+=t.dataSize)}),this._currentStream&&this._currentStream.dataSize&&(this.dataSize+=this._currentStream.dataSize)};N.prototype._emitError=function(e){this._reset(),this.emit("error",e)}});var yi=b((VU,KP)=>{KP.exports={"application/1d-interleaved-parityfec":{source:"iana"},"application/3gpdash-qoe-report+xml":{source:"iana",charset:"UTF-8",compressible:!0},"application/3gpp-ims+xml":{source:"iana",compressible:!0},"application/3gpphal+json":{source:"iana",compressible:!0},"application/3gpphalforms+json":{source:"iana",compressible:!0},"application/a2l":{source:"iana"},"application/ace+cbor":{source:"iana"},"application/activemessage":{source:"iana"},"application/activity+json":{source:"iana",compressible:!0},"application/alto-costmap+json":{source:"iana",compressible:!0},"application/alto-costmapfilter+json":{source:"iana",compressible:!0},"application/alto-directory+json":{source:"iana",compressible:!0},"application/alto-endpointcost+json":{source:"iana",compressible:!0},"application/alto-endpointcostparams+json":{source:"iana",compressible:!0},"application/alto-endpointprop+json":{source:"iana",compressible:!0},"application/alto-endpointpropparams+json":{source:"iana",compressible:!0},"application/alto-error+json":{source:"iana",compressible:!0},"application/alto-networkmap+json":{source:"iana",compressible:!0},"application/alto-networkmapfilter+json":{source:"iana",compressible:!0},"application/alto-updatestreamcontrol+json":{source:"iana",compressible:!0},"application/alto-updatestreamparams+json":{source:"iana",compressible:!0},"application/aml":{source:"iana"},"application/andrew-inset":{source:"iana",extensions:["ez"]},"application/applefile":{source:"iana"},"application/applixware":{source:"apache",extensions:["aw"]},"application/at+jwt":{source:"iana"},"application/atf":{source:"iana"},"application/atfx":{source:"iana"},"application/atom+xml":{source:"iana",compressible:!0,extensions:["atom"]},"application/atomcat+xml":{source:"iana",compressible:!0,extensions:["atomcat"]},"application/atomdeleted+xml":{source:"iana",compressible:!0,extensions:["atomdeleted"]},"application/atomicmail":{source:"iana"},"application/atomsvc+xml":{source:"iana",compressible:!0,extensions:["atomsvc"]},"application/atsc-dwd+xml":{source:"iana",compressible:!0,extensions:["dwd"]},"application/atsc-dynamic-event-message":{source:"iana"},"application/atsc-held+xml":{source:"iana",compressible:!0,extensions:["held"]},"application/atsc-rdt+json":{source:"iana",compressible:!0},"application/atsc-rsat+xml":{source:"iana",compressible:!0,extensions:["rsat"]},"application/atxml":{source:"iana"},"application/auth-policy+xml":{source:"iana",compressible:!0},"application/bacnet-xdd+zip":{source:"iana",compressible:!1},"application/batch-smtp":{source:"iana"},"application/bdoc":{compressible:!1,extensions:["bdoc"]},"application/beep+xml":{source:"iana",charset:"UTF-8",compressible:!0},"application/calendar+json":{source:"iana",compressible:!0},"application/calendar+xml":{source:"iana",compressible:!0,extensions:["xcs"]},"application/call-completion":{source:"iana"},"application/cals-1840":{source:"iana"},"application/captive+json":{source:"iana",compressible:!0},"application/cbor":{source:"iana"},"application/cbor-seq":{source:"iana"},"application/cccex":{source:"iana"},"application/ccmp+xml":{source:"iana",compressible:!0},"application/ccxml+xml":{source:"iana",compressible:!0,extensions:["ccxml"]},"application/cdfx+xml":{source:"iana",compressible:!0,extensions:["cdfx"]},"application/cdmi-capability":{source:"iana",extensions:["cdmia"]},"application/cdmi-container":{source:"iana",extensions:["cdmic"]},"application/cdmi-domain":{source:"iana",extensions:["cdmid"]},"application/cdmi-object":{source:"iana",extensions:["cdmio"]},"application/cdmi-queue":{source:"iana",extensions:["cdmiq"]},"application/cdni":{source:"iana"},"application/cea":{source:"iana"},"application/cea-2018+xml":{source:"iana",compressible:!0},"application/cellml+xml":{source:"iana",compressible:!0},"application/cfw":{source:"iana"},"application/city+json":{source:"iana",compressible:!0},"application/clr":{source:"iana"},"application/clue+xml":{source:"iana",compressible:!0},"application/clue_info+xml":{source:"iana",compressible:!0},"application/cms":{source:"iana"},"application/cnrp+xml":{source:"iana",compressible:!0},"application/coap-group+json":{source:"iana",compressible:!0},"application/coap-payload":{source:"iana"},"application/commonground":{source:"iana"},"application/conference-info+xml":{source:"iana",compressible:!0},"application/cose":{source:"iana"},"application/cose-key":{source:"iana"},"application/cose-key-set":{source:"iana"},"application/cpl+xml":{source:"iana",compressible:!0,extensions:["cpl"]},"application/csrattrs":{source:"iana"},"application/csta+xml":{source:"iana",compressible:!0},"application/cstadata+xml":{source:"iana",compressible:!0},"application/csvm+json":{source:"iana",compressible:!0},"application/cu-seeme":{source:"apache",extensions:["cu"]},"application/cwt":{source:"iana"},"application/cybercash":{source:"iana"},"application/dart":{compressible:!0},"application/dash+xml":{source:"iana",compressible:!0,extensions:["mpd"]},"application/dash-patch+xml":{source:"iana",compressible:!0,extensions:["mpp"]},"application/dashdelta":{source:"iana"},"application/davmount+xml":{source:"iana",compressible:!0,extensions:["davmount"]},"application/dca-rft":{source:"iana"},"application/dcd":{source:"iana"},"application/dec-dx":{source:"iana"},"application/dialog-info+xml":{source:"iana",compressible:!0},"application/dicom":{source:"iana"},"application/dicom+json":{source:"iana",compressible:!0},"application/dicom+xml":{source:"iana",compressible:!0},"application/dii":{source:"iana"},"application/dit":{source:"iana"},"application/dns":{source:"iana"},"application/dns+json":{source:"iana",compressible:!0},"application/dns-message":{source:"iana"},"application/docbook+xml":{source:"apache",compressible:!0,extensions:["dbk"]},"application/dots+cbor":{source:"iana"},"application/dskpp+xml":{source:"iana",compressible:!0},"application/dssc+der":{source:"iana",extensions:["dssc"]},"application/dssc+xml":{source:"iana",compressible:!0,extensions:["xdssc"]},"application/dvcs":{source:"iana"},"application/ecmascript":{source:"iana",compressible:!0,extensions:["es","ecma"]},"application/edi-consent":{source:"iana"},"application/edi-x12":{source:"iana",compressible:!1},"application/edifact":{source:"iana",compressible:!1},"application/efi":{source:"iana"},"application/elm+json":{source:"iana",charset:"UTF-8",compressible:!0},"application/elm+xml":{source:"iana",compressible:!0},"application/emergencycalldata.cap+xml":{source:"iana",charset:"UTF-8",compressible:!0},"application/emergencycalldata.comment+xml":{source:"iana",compressible:!0},"application/emergencycalldata.control+xml":{source:"iana",compressible:!0},"application/emergencycalldata.deviceinfo+xml":{source:"iana",compressible:!0},"application/emergencycalldata.ecall.msd":{source:"iana"},"application/emergencycalldata.providerinfo+xml":{source:"iana",compressible:!0},"application/emergencycalldata.serviceinfo+xml":{source:"iana",compressible:!0},"application/emergencycalldata.subscriberinfo+xml":{source:"iana",compressible:!0},"application/emergencycalldata.veds+xml":{source:"iana",compressible:!0},"application/emma+xml":{source:"iana",compressible:!0,extensions:["emma"]},"application/emotionml+xml":{source:"iana",compressible:!0,extensions:["emotionml"]},"application/encaprtp":{source:"iana"},"application/epp+xml":{source:"iana",compressible:!0},"application/epub+zip":{source:"iana",compressible:!1,extensions:["epub"]},"application/eshop":{source:"iana"},"application/exi":{source:"iana",extensions:["exi"]},"application/expect-ct-report+json":{source:"iana",compressible:!0},"application/express":{source:"iana",extensions:["exp"]},"application/fastinfoset":{source:"iana"},"application/fastsoap":{source:"iana"},"application/fdt+xml":{source:"iana",compressible:!0,extensions:["fdt"]},"application/fhir+json":{source:"iana",charset:"UTF-8",compressible:!0},"application/fhir+xml":{source:"iana",charset:"UTF-8",compressible:!0},"application/fido.trusted-apps+json":{compressible:!0},"application/fits":{source:"iana"},"application/flexfec":{source:"iana"},"application/font-sfnt":{source:"iana"},"application/font-tdpfr":{source:"iana",extensions:["pfr"]},"application/font-woff":{source:"iana",compressible:!1},"application/framework-attributes+xml":{source:"iana",compressible:!0},"application/geo+json":{source:"iana",compressible:!0,extensions:["geojson"]},"application/geo+json-seq":{source:"iana"},"application/geopackage+sqlite3":{source:"iana"},"application/geoxacml+xml":{source:"iana",compressible:!0},"application/gltf-buffer":{source:"iana"},"application/gml+xml":{source:"iana",compressible:!0,extensions:["gml"]},"application/gpx+xml":{source:"apache",compressible:!0,extensions:["gpx"]},"application/gxf":{source:"apache",extensions:["gxf"]},"application/gzip":{source:"iana",compressible:!1,extensions:["gz"]},"application/h224":{source:"iana"},"application/held+xml":{source:"iana",compressible:!0},"application/hjson":{extensions:["hjson"]},"application/http":{source:"iana"},"application/hyperstudio":{source:"iana",extensions:["stk"]},"application/ibe-key-request+xml":{source:"iana",compressible:!0},"application/ibe-pkg-reply+xml":{source:"iana",compressible:!0},"application/ibe-pp-data":{source:"iana"},"application/iges":{source:"iana"},"application/im-iscomposing+xml":{source:"iana",charset:"UTF-8",compressible:!0},"application/index":{source:"iana"},"application/index.cmd":{source:"iana"},"application/index.obj":{source:"iana"},"application/index.response":{source:"iana"},"application/index.vnd":{source:"iana"},"application/inkml+xml":{source:"iana",compressible:!0,extensions:["ink","inkml"]},"application/iotp":{source:"iana"},"application/ipfix":{source:"iana",extensions:["ipfix"]},"application/ipp":{source:"iana"},"application/isup":{source:"iana"},"application/its+xml":{source:"iana",compressible:!0,extensions:["its"]},"application/java-archive":{source:"apache",compressible:!1,extensions:["jar","war","ear"]},"application/java-serialized-object":{source:"apache",compressible:!1,extensions:["ser"]},"application/java-vm":{source:"apache",compressible:!1,extensions:["class"]},"application/javascript":{source:"iana",charset:"UTF-8",compressible:!0,extensions:["js","mjs"]},"application/jf2feed+json":{source:"iana",compressible:!0},"application/jose":{source:"iana"},"application/jose+json":{source:"iana",compressible:!0},"application/jrd+json":{source:"iana",compressible:!0},"application/jscalendar+json":{source:"iana",compressible:!0},"application/json":{source:"iana",charset:"UTF-8",compressible:!0,extensions:["json","map"]},"application/json-patch+json":{source:"iana",compressible:!0},"application/json-seq":{source:"iana"},"application/json5":{extensions:["json5"]},"application/jsonml+json":{source:"apache",compressible:!0,extensions:["jsonml"]},"application/jwk+json":{source:"iana",compressible:!0},"application/jwk-set+json":{source:"iana",compressible:!0},"application/jwt":{source:"iana"},"application/kpml-request+xml":{source:"iana",compressible:!0},"application/kpml-response+xml":{source:"iana",compressible:!0},"application/ld+json":{source:"iana",compressible:!0,extensions:["jsonld"]},"application/lgr+xml":{source:"iana",compressible:!0,extensions:["lgr"]},"application/link-format":{source:"iana"},"application/load-control+xml":{source:"iana",compressible:!0},"application/lost+xml":{source:"iana",compressible:!0,extensions:["lostxml"]},"application/lostsync+xml":{source:"iana",compressible:!0},"application/lpf+zip":{source:"iana",compressible:!1},"application/lxf":{source:"iana"},"application/mac-binhex40":{source:"iana",extensions:["hqx"]},"application/mac-compactpro":{source:"apache",extensions:["cpt"]},"application/macwriteii":{source:"iana"},"application/mads+xml":{source:"iana",compressible:!0,extensions:["mads"]},"application/manifest+json":{source:"iana",charset:"UTF-8",compressible:!0,extensions:["webmanifest"]},"application/marc":{source:"iana",extensions:["mrc"]},"application/marcxml+xml":{source:"iana",compressible:!0,extensions:["mrcx"]},"application/mathematica":{source:"iana",extensions:["ma","nb","mb"]},"application/mathml+xml":{source:"iana",compressible:!0,extensions:["mathml"]},"application/mathml-content+xml":{source:"iana",compressible:!0},"application/mathml-presentation+xml":{source:"iana",compressible:!0},"application/mbms-associated-procedure-description+xml":{source:"iana",compressible:!0},"application/mbms-deregister+xml":{source:"iana",compressible:!0},"application/mbms-envelope+xml":{source:"iana",compressible:!0},"application/mbms-msk+xml":{source:"iana",compressible:!0},"application/mbms-msk-response+xml":{source:"iana",compressible:!0},"application/mbms-protection-description+xml":{source:"iana",compressible:!0},"application/mbms-reception-report+xml":{source:"iana",compressible:!0},"application/mbms-register+xml":{source:"iana",compressible:!0},"application/mbms-register-response+xml":{source:"iana",compressible:!0},"application/mbms-schedule+xml":{source:"iana",compressible:!0},"application/mbms-user-service-description+xml":{source:"iana",compressible:!0},"application/mbox":{source:"iana",extensions:["mbox"]},"application/media-policy-dataset+xml":{source:"iana",compressible:!0,extensions:["mpf"]},"application/media_control+xml":{source:"iana",compressible:!0},"application/mediaservercontrol+xml":{source:"iana",compressible:!0,extensions:["mscml"]},"application/merge-patch+json":{source:"iana",compressible:!0},"application/metalink+xml":{source:"apache",compressible:!0,extensions:["metalink"]},"application/metalink4+xml":{source:"iana",compressible:!0,extensions:["meta4"]},"application/mets+xml":{source:"iana",compressible:!0,extensions:["mets"]},"application/mf4":{source:"iana"},"application/mikey":{source:"iana"},"application/mipc":{source:"iana"},"application/missing-blocks+cbor-seq":{source:"iana"},"application/mmt-aei+xml":{source:"iana",compressible:!0,extensions:["maei"]},"application/mmt-usd+xml":{source:"iana",compressible:!0,extensions:["musd"]},"application/mods+xml":{source:"iana",compressible:!0,extensions:["mods"]},"application/moss-keys":{source:"iana"},"application/moss-signature":{source:"iana"},"application/mosskey-data":{source:"iana"},"application/mosskey-request":{source:"iana"},"application/mp21":{source:"iana",extensions:["m21","mp21"]},"application/mp4":{source:"iana",extensions:["mp4s","m4p"]},"application/mpeg4-generic":{source:"iana"},"application/mpeg4-iod":{source:"iana"},"application/mpeg4-iod-xmt":{source:"iana"},"application/mrb-consumer+xml":{source:"iana",compressible:!0},"application/mrb-publish+xml":{source:"iana",compressible:!0},"application/msc-ivr+xml":{source:"iana",charset:"UTF-8",compressible:!0},"application/msc-mixer+xml":{source:"iana",charset:"UTF-8",compressible:!0},"application/msword":{source:"iana",compressible:!1,extensions:["doc","dot"]},"application/mud+json":{source:"iana",compressible:!0},"application/multipart-core":{source:"iana"},"application/mxf":{source:"iana",extensions:["mxf"]},"application/n-quads":{source:"iana",extensions:["nq"]},"application/n-triples":{source:"iana",extensions:["nt"]},"application/nasdata":{source:"iana"},"application/news-checkgroups":{source:"iana",charset:"US-ASCII"},"application/news-groupinfo":{source:"iana",charset:"US-ASCII"},"application/news-transmission":{source:"iana"},"application/nlsml+xml":{source:"iana",compressible:!0},"application/node":{source:"iana",extensions:["cjs"]},"application/nss":{source:"iana"},"application/oauth-authz-req+jwt":{source:"iana"},"application/oblivious-dns-message":{source:"iana"},"application/ocsp-request":{source:"iana"},"application/ocsp-response":{source:"iana"},"application/octet-stream":{source:"iana",compressible:!1,extensions:["bin","dms","lrf","mar","so","dist","distz","pkg","bpk","dump","elc","deploy","exe","dll","deb","dmg","iso","img","msi","msp","msm","buffer"]},"application/oda":{source:"iana",extensions:["oda"]},"application/odm+xml":{source:"iana",compressible:!0},"application/odx":{source:"iana"},"application/oebps-package+xml":{source:"iana",compressible:!0,extensions:["opf"]},"application/ogg":{source:"iana",compressible:!1,extensions:["ogx"]},"application/omdoc+xml":{source:"apache",compressible:!0,extensions:["omdoc"]},"application/onenote":{source:"apache",extensions:["onetoc","onetoc2","onetmp","onepkg"]},"application/opc-nodeset+xml":{source:"iana",compressible:!0},"application/oscore":{source:"iana"},"application/oxps":{source:"iana",extensions:["oxps"]},"application/p21":{source:"iana"},"application/p21+zip":{source:"iana",compressible:!1},"application/p2p-overlay+xml":{source:"iana",compressible:!0,extensions:["relo"]},"application/parityfec":{source:"iana"},"application/passport":{source:"iana"},"application/patch-ops-error+xml":{source:"iana",compressible:!0,extensions:["xer"]},"application/pdf":{source:"iana",compressible:!1,extensions:["pdf"]},"application/pdx":{source:"iana"},"application/pem-certificate-chain":{source:"iana"},"application/pgp-encrypted":{source:"iana",compressible:!1,extensions:["pgp"]},"application/pgp-keys":{source:"iana",extensions:["asc"]},"application/pgp-signature":{source:"iana",extensions:["asc","sig"]},"application/pics-rules":{source:"apache",extensions:["prf"]},"application/pidf+xml":{source:"iana",charset:"UTF-8",compressible:!0},"application/pidf-diff+xml":{source:"iana",charset:"UTF-8",compressible:!0},"application/pkcs10":{source:"iana",extensions:["p10"]},"application/pkcs12":{source:"iana"},"application/pkcs7-mime":{source:"iana",extensions:["p7m","p7c"]},"application/pkcs7-signature":{source:"iana",extensions:["p7s"]},"application/pkcs8":{source:"iana",extensions:["p8"]},"application/pkcs8-encrypted":{source:"iana"},"application/pkix-attr-cert":{source:"iana",extensions:["ac"]},"application/pkix-cert":{source:"iana",extensions:["cer"]},"application/pkix-crl":{source:"iana",extensions:["crl"]},"application/pkix-pkipath":{source:"iana",extensions:["pkipath"]},"application/pkixcmp":{source:"iana",extensions:["pki"]},"application/pls+xml":{source:"iana",compressible:!0,extensions:["pls"]},"application/poc-settings+xml":{source:"iana",charset:"UTF-8",compressible:!0},"application/postscript":{source:"iana",compressible:!0,extensions:["ai","eps","ps"]},"application/ppsp-tracker+json":{source:"iana",compressible:!0},"application/problem+json":{source:"iana",compressible:!0},"application/problem+xml":{source:"iana",compressible:!0},"application/provenance+xml":{source:"iana",compressible:!0,extensions:["provx"]},"application/prs.alvestrand.titrax-sheet":{source:"iana"},"application/prs.cww":{source:"iana",extensions:["cww"]},"application/prs.cyn":{source:"iana",charset:"7-BIT"},"application/prs.hpub+zip":{source:"iana",compressible:!1},"application/prs.nprend":{source:"iana"},"application/prs.plucker":{source:"iana"},"application/prs.rdf-xml-crypt":{source:"iana"},"application/prs.xsf+xml":{source:"iana",compressible:!0},"application/pskc+xml":{source:"iana",compressible:!0,extensions:["pskcxml"]},"application/pvd+json":{source:"iana",compressible:!0},"application/qsig":{source:"iana"},"application/raml+yaml":{compressible:!0,extensions:["raml"]},"application/raptorfec":{source:"iana"},"application/rdap+json":{source:"iana",compressible:!0},"application/rdf+xml":{source:"iana",compressible:!0,extensions:["rdf","owl"]},"application/reginfo+xml":{source:"iana",compressible:!0,extensions:["rif"]},"application/relax-ng-compact-syntax":{source:"iana",extensions:["rnc"]},"application/remote-printing":{source:"iana"},"application/reputon+json":{source:"iana",compressible:!0},"application/resource-lists+xml":{source:"iana",compressible:!0,extensions:["rl"]},"application/resource-lists-diff+xml":{source:"iana",compressible:!0,extensions:["rld"]},"application/rfc+xml":{source:"iana",compressible:!0},"application/riscos":{source:"iana"},"application/rlmi+xml":{source:"iana",compressible:!0},"application/rls-services+xml":{source:"iana",compressible:!0,extensions:["rs"]},"application/route-apd+xml":{source:"iana",compressible:!0,extensions:["rapd"]},"application/route-s-tsid+xml":{source:"iana",compressible:!0,extensions:["sls"]},"application/route-usd+xml":{source:"iana",compressible:!0,extensions:["rusd"]},"application/rpki-ghostbusters":{source:"iana",extensions:["gbr"]},"application/rpki-manifest":{source:"iana",extensions:["mft"]},"application/rpki-publication":{source:"iana"},"application/rpki-roa":{source:"iana",extensions:["roa"]},"application/rpki-updown":{source:"iana"},"application/rsd+xml":{source:"apache",compressible:!0,extensions:["rsd"]},"application/rss+xml":{source:"apache",compressible:!0,extensions:["rss"]},"application/rtf":{source:"iana",compressible:!0,extensions:["rtf"]},"application/rtploopback":{source:"iana"},"application/rtx":{source:"iana"},"application/samlassertion+xml":{source:"iana",compressible:!0},"application/samlmetadata+xml":{source:"iana",compressible:!0},"application/sarif+json":{source:"iana",compressible:!0},"application/sarif-external-properties+json":{source:"iana",compressible:!0},"application/sbe":{source:"iana"},"application/sbml+xml":{source:"iana",compressible:!0,extensions:["sbml"]},"application/scaip+xml":{source:"iana",compressible:!0},"application/scim+json":{source:"iana",compressible:!0},"application/scvp-cv-request":{source:"iana",extensions:["scq"]},"application/scvp-cv-response":{source:"iana",extensions:["scs"]},"application/scvp-vp-request":{source:"iana",extensions:["spq"]},"application/scvp-vp-response":{source:"iana",extensions:["spp"]},"application/sdp":{source:"iana",extensions:["sdp"]},"application/secevent+jwt":{source:"iana"},"application/senml+cbor":{source:"iana"},"application/senml+json":{source:"iana",compressible:!0},"application/senml+xml":{source:"iana",compressible:!0,extensions:["senmlx"]},"application/senml-etch+cbor":{source:"iana"},"application/senml-etch+json":{source:"iana",compressible:!0},"application/senml-exi":{source:"iana"},"application/sensml+cbor":{source:"iana"},"application/sensml+json":{source:"iana",compressible:!0},"application/sensml+xml":{source:"iana",compressible:!0,extensions:["sensmlx"]},"application/sensml-exi":{source:"iana"},"application/sep+xml":{source:"iana",compressible:!0},"application/sep-exi":{source:"iana"},"application/session-info":{source:"iana"},"application/set-payment":{source:"iana"},"application/set-payment-initiation":{source:"iana",extensions:["setpay"]},"application/set-registration":{source:"iana"},"application/set-registration-initiation":{source:"iana",extensions:["setreg"]},"application/sgml":{source:"iana"},"application/sgml-open-catalog":{source:"iana"},"application/shf+xml":{source:"iana",compressible:!0,extensions:["shf"]},"application/sieve":{source:"iana",extensions:["siv","sieve"]},"application/simple-filter+xml":{source:"iana",compressible:!0},"application/simple-message-summary":{source:"iana"},"application/simplesymbolcontainer":{source:"iana"},"application/sipc":{source:"iana"},"application/slate":{source:"iana"},"application/smil":{source:"iana"},"application/smil+xml":{source:"iana",compressible:!0,extensions:["smi","smil"]},"application/smpte336m":{source:"iana"},"application/soap+fastinfoset":{source:"iana"},"application/soap+xml":{source:"iana",compressible:!0},"application/sparql-query":{source:"iana",extensions:["rq"]},"application/sparql-results+xml":{source:"iana",compressible:!0,extensions:["srx"]},"application/spdx+json":{source:"iana",compressible:!0},"application/spirits-event+xml":{source:"iana",compressible:!0},"application/sql":{source:"iana"},"application/srgs":{source:"iana",extensions:["gram"]},"application/srgs+xml":{source:"iana",compressible:!0,extensions:["grxml"]},"application/sru+xml":{source:"iana",compressible:!0,extensions:["sru"]},"application/ssdl+xml":{source:"apache",compressible:!0,extensions:["ssdl"]},"application/ssml+xml":{source:"iana",compressible:!0,extensions:["ssml"]},"application/stix+json":{source:"iana",compressible:!0},"application/swid+xml":{source:"iana",compressible:!0,extensions:["swidtag"]},"application/tamp-apex-update":{source:"iana"},"application/tamp-apex-update-confirm":{source:"iana"},"application/tamp-community-update":{source:"iana"},"application/tamp-community-update-confirm":{source:"iana"},"application/tamp-error":{source:"iana"},"application/tamp-sequence-adjust":{source:"iana"},"application/tamp-sequence-adjust-confirm":{source:"iana"},"application/tamp-status-query":{source:"iana"},"application/tamp-status-response":{source:"iana"},"application/tamp-update":{source:"iana"},"application/tamp-update-confirm":{source:"iana"},"application/tar":{compressible:!0},"application/taxii+json":{source:"iana",compressible:!0},"application/td+json":{source:"iana",compressible:!0},"application/tei+xml":{source:"iana",compressible:!0,extensions:["tei","teicorpus"]},"application/tetra_isi":{source:"iana"},"application/thraud+xml":{source:"iana",compressible:!0,extensions:["tfi"]},"application/timestamp-query":{source:"iana"},"application/timestamp-reply":{source:"iana"},"application/timestamped-data":{source:"iana",extensions:["tsd"]},"application/tlsrpt+gzip":{source:"iana"},"application/tlsrpt+json":{source:"iana",compressible:!0},"application/tnauthlist":{source:"iana"},"application/token-introspection+jwt":{source:"iana"},"application/toml":{compressible:!0,extensions:["toml"]},"application/trickle-ice-sdpfrag":{source:"iana"},"application/trig":{source:"iana",extensions:["trig"]},"application/ttml+xml":{source:"iana",compressible:!0,extensions:["ttml"]},"application/tve-trigger":{source:"iana"},"application/tzif":{source:"iana"},"application/tzif-leap":{source:"iana"},"application/ubjson":{compressible:!1,extensions:["ubj"]},"application/ulpfec":{source:"iana"},"application/urc-grpsheet+xml":{source:"iana",compressible:!0},"application/urc-ressheet+xml":{source:"iana",compressible:!0,extensions:["rsheet"]},"application/urc-targetdesc+xml":{source:"iana",compressible:!0,extensions:["td"]},"application/urc-uisocketdesc+xml":{source:"iana",compressible:!0},"application/vcard+json":{source:"iana",compressible:!0},"application/vcard+xml":{source:"iana",compressible:!0},"application/vemmi":{source:"iana"},"application/vividence.scriptfile":{source:"apache"},"application/vnd.1000minds.decision-model+xml":{source:"iana",compressible:!0,extensions:["1km"]},"application/vnd.3gpp-prose+xml":{source:"iana",compressible:!0},"application/vnd.3gpp-prose-pc3ch+xml":{source:"iana",compressible:!0},"application/vnd.3gpp-v2x-local-service-information":{source:"iana"},"application/vnd.3gpp.5gnas":{source:"iana"},"application/vnd.3gpp.access-transfer-events+xml":{source:"iana",compressible:!0},"application/vnd.3gpp.bsf+xml":{source:"iana",compressible:!0},"application/vnd.3gpp.gmop+xml":{source:"iana",compressible:!0},"application/vnd.3gpp.gtpc":{source:"iana"},"application/vnd.3gpp.interworking-data":{source:"iana"},"application/vnd.3gpp.lpp":{source:"iana"},"application/vnd.3gpp.mc-signalling-ear":{source:"iana"},"application/vnd.3gpp.mcdata-affiliation-command+xml":{source:"iana",compressible:!0},"application/vnd.3gpp.mcdata-info+xml":{source:"iana",compressible:!0},"application/vnd.3gpp.mcdata-payload":{source:"iana"},"application/vnd.3gpp.mcdata-service-config+xml":{source:"iana",compressible:!0},"application/vnd.3gpp.mcdata-signalling":{source:"iana"},"application/vnd.3gpp.mcdata-ue-config+xml":{source:"iana",compressible:!0},"application/vnd.3gpp.mcdata-user-profile+xml":{source:"iana",compressible:!0},"application/vnd.3gpp.mcptt-affiliation-command+xml":{source:"iana",compressible:!0},"application/vnd.3gpp.mcptt-floor-request+xml":{source:"iana",compressible:!0},"application/vnd.3gpp.mcptt-info+xml":{source:"iana",compressible:!0},"application/vnd.3gpp.mcptt-location-info+xml":{source:"iana",compressible:!0},"application/vnd.3gpp.mcptt-mbms-usage-info+xml":{source:"iana",compressible:!0},"application/vnd.3gpp.mcptt-service-config+xml":{source:"iana",compressible:!0},"application/vnd.3gpp.mcptt-signed+xml":{source:"iana",compressible:!0},"application/vnd.3gpp.mcptt-ue-config+xml":{source:"iana",compressible:!0},"application/vnd.3gpp.mcptt-ue-init-config+xml":{source:"iana",compressible:!0},"application/vnd.3gpp.mcptt-user-profile+xml":{source:"iana",compressible:!0},"application/vnd.3gpp.mcvideo-affiliation-command+xml":{source:"iana",compressible:!0},"application/vnd.3gpp.mcvideo-affiliation-info+xml":{source:"iana",compressible:!0},"application/vnd.3gpp.mcvideo-info+xml":{source:"iana",compressible:!0},"application/vnd.3gpp.mcvideo-location-info+xml":{source:"iana",compressible:!0},"application/vnd.3gpp.mcvideo-mbms-usage-info+xml":{source:"iana",compressible:!0},"application/vnd.3gpp.mcvideo-service-config+xml":{source:"iana",compressible:!0},"application/vnd.3gpp.mcvideo-transmission-request+xml":{source:"iana",compressible:!0},"application/vnd.3gpp.mcvideo-ue-config+xml":{source:"iana",compressible:!0},"application/vnd.3gpp.mcvideo-user-profile+xml":{source:"iana",compressible:!0},"application/vnd.3gpp.mid-call+xml":{source:"iana",compressible:!0},"application/vnd.3gpp.ngap":{source:"iana"},"application/vnd.3gpp.pfcp":{source:"iana"},"application/vnd.3gpp.pic-bw-large":{source:"iana",extensions:["plb"]},"application/vnd.3gpp.pic-bw-small":{source:"iana",extensions:["psb"]},"application/vnd.3gpp.pic-bw-var":{source:"iana",extensions:["pvb"]},"application/vnd.3gpp.s1ap":{source:"iana"},"application/vnd.3gpp.sms":{source:"iana"},"application/vnd.3gpp.sms+xml":{source:"iana",compressible:!0},"application/vnd.3gpp.srvcc-ext+xml":{source:"iana",compressible:!0},"application/vnd.3gpp.srvcc-info+xml":{source:"iana",compressible:!0},"application/vnd.3gpp.state-and-event-info+xml":{source:"iana",compressible:!0},"application/vnd.3gpp.ussd+xml":{source:"iana",compressible:!0},"application/vnd.3gpp2.bcmcsinfo+xml":{source:"iana",compressible:!0},"application/vnd.3gpp2.sms":{source:"iana"},"application/vnd.3gpp2.tcap":{source:"iana",extensions:["tcap"]},"application/vnd.3lightssoftware.imagescal":{source:"iana"},"application/vnd.3m.post-it-notes":{source:"iana",extensions:["pwn"]},"application/vnd.accpac.simply.aso":{source:"iana",extensions:["aso"]},"application/vnd.accpac.simply.imp":{source:"iana",extensions:["imp"]},"application/vnd.acucobol":{source:"iana",extensions:["acu"]},"application/vnd.acucorp":{source:"iana",extensions:["atc","acutc"]},"application/vnd.adobe.air-application-installer-package+zip":{source:"apache",compressible:!1,extensions:["air"]},"application/vnd.adobe.flash.movie":{source:"iana"},"application/vnd.adobe.formscentral.fcdt":{source:"iana",extensions:["fcdt"]},"application/vnd.adobe.fxp":{source:"iana",extensions:["fxp","fxpl"]},"application/vnd.adobe.partial-upload":{source:"iana"},"application/vnd.adobe.xdp+xml":{source:"iana",compressible:!0,extensions:["xdp"]},"application/vnd.adobe.xfdf":{source:"iana",extensions:["xfdf"]},"application/vnd.aether.imp":{source:"iana"},"application/vnd.afpc.afplinedata":{source:"iana"},"application/vnd.afpc.afplinedata-pagedef":{source:"iana"},"application/vnd.afpc.cmoca-cmresource":{source:"iana"},"application/vnd.afpc.foca-charset":{source:"iana"},"application/vnd.afpc.foca-codedfont":{source:"iana"},"application/vnd.afpc.foca-codepage":{source:"iana"},"application/vnd.afpc.modca":{source:"iana"},"application/vnd.afpc.modca-cmtable":{source:"iana"},"application/vnd.afpc.modca-formdef":{source:"iana"},"application/vnd.afpc.modca-mediummap":{source:"iana"},"application/vnd.afpc.modca-objectcontainer":{source:"iana"},"application/vnd.afpc.modca-overlay":{source:"iana"},"application/vnd.afpc.modca-pagesegment":{source:"iana"},"application/vnd.age":{source:"iana",extensions:["age"]},"application/vnd.ah-barcode":{source:"iana"},"application/vnd.ahead.space":{source:"iana",extensions:["ahead"]},"application/vnd.airzip.filesecure.azf":{source:"iana",extensions:["azf"]},"application/vnd.airzip.filesecure.azs":{source:"iana",extensions:["azs"]},"application/vnd.amadeus+json":{source:"iana",compressible:!0},"application/vnd.amazon.ebook":{source:"apache",extensions:["azw"]},"application/vnd.amazon.mobi8-ebook":{source:"iana"},"application/vnd.americandynamics.acc":{source:"iana",extensions:["acc"]},"application/vnd.amiga.ami":{source:"iana",extensions:["ami"]},"application/vnd.amundsen.maze+xml":{source:"iana",compressible:!0},"application/vnd.android.ota":{source:"iana"},"application/vnd.android.package-archive":{source:"apache",compressible:!1,extensions:["apk"]},"application/vnd.anki":{source:"iana"},"application/vnd.anser-web-certificate-issue-initiation":{source:"iana",extensions:["cii"]},"application/vnd.anser-web-funds-transfer-initiation":{source:"apache",extensions:["fti"]},"application/vnd.antix.game-component":{source:"iana",extensions:["atx"]},"application/vnd.apache.arrow.file":{source:"iana"},"application/vnd.apache.arrow.stream":{source:"iana"},"application/vnd.apache.thrift.binary":{source:"iana"},"application/vnd.apache.thrift.compact":{source:"iana"},"application/vnd.apache.thrift.json":{source:"iana"},"application/vnd.api+json":{source:"iana",compressible:!0},"application/vnd.aplextor.warrp+json":{source:"iana",compressible:!0},"application/vnd.apothekende.reservation+json":{source:"iana",compressible:!0},"application/vnd.apple.installer+xml":{source:"iana",compressible:!0,extensions:["mpkg"]},"application/vnd.apple.keynote":{source:"iana",extensions:["key"]},"application/vnd.apple.mpegurl":{source:"iana",extensions:["m3u8"]},"application/vnd.apple.numbers":{source:"iana",extensions:["numbers"]},"application/vnd.apple.pages":{source:"iana",extensions:["pages"]},"application/vnd.apple.pkpass":{compressible:!1,extensions:["pkpass"]},"application/vnd.arastra.swi":{source:"iana"},"application/vnd.aristanetworks.swi":{source:"iana",extensions:["swi"]},"application/vnd.artisan+json":{source:"iana",compressible:!0},"application/vnd.artsquare":{source:"iana"},"application/vnd.astraea-software.iota":{source:"iana",extensions:["iota"]},"application/vnd.audiograph":{source:"iana",extensions:["aep"]},"application/vnd.autopackage":{source:"iana"},"application/vnd.avalon+json":{source:"iana",compressible:!0},"application/vnd.avistar+xml":{source:"iana",compressible:!0},"application/vnd.balsamiq.bmml+xml":{source:"iana",compressible:!0,extensions:["bmml"]},"application/vnd.balsamiq.bmpr":{source:"iana"},"application/vnd.banana-accounting":{source:"iana"},"application/vnd.bbf.usp.error":{source:"iana"},"application/vnd.bbf.usp.msg":{source:"iana"},"application/vnd.bbf.usp.msg+json":{source:"iana",compressible:!0},"application/vnd.bekitzur-stech+json":{source:"iana",compressible:!0},"application/vnd.bint.med-content":{source:"iana"},"application/vnd.biopax.rdf+xml":{source:"iana",compressible:!0},"application/vnd.blink-idb-value-wrapper":{source:"iana"},"application/vnd.blueice.multipass":{source:"iana",extensions:["mpm"]},"application/vnd.bluetooth.ep.oob":{source:"iana"},"application/vnd.bluetooth.le.oob":{source:"iana"},"application/vnd.bmi":{source:"iana",extensions:["bmi"]},"application/vnd.bpf":{source:"iana"},"application/vnd.bpf3":{source:"iana"},"application/vnd.businessobjects":{source:"iana",extensions:["rep"]},"application/vnd.byu.uapi+json":{source:"iana",compressible:!0},"application/vnd.cab-jscript":{source:"iana"},"application/vnd.canon-cpdl":{source:"iana"},"application/vnd.canon-lips":{source:"iana"},"application/vnd.capasystems-pg+json":{source:"iana",compressible:!0},"application/vnd.cendio.thinlinc.clientconf":{source:"iana"},"application/vnd.century-systems.tcp_stream":{source:"iana"},"application/vnd.chemdraw+xml":{source:"iana",compressible:!0,extensions:["cdxml"]},"application/vnd.chess-pgn":{source:"iana"},"application/vnd.chipnuts.karaoke-mmd":{source:"iana",extensions:["mmd"]},"application/vnd.ciedi":{source:"iana"},"application/vnd.cinderella":{source:"iana",extensions:["cdy"]},"application/vnd.cirpack.isdn-ext":{source:"iana"},"application/vnd.citationstyles.style+xml":{source:"iana",compressible:!0,extensions:["csl"]},"application/vnd.claymore":{source:"iana",extensions:["cla"]},"application/vnd.cloanto.rp9":{source:"iana",extensions:["rp9"]},"application/vnd.clonk.c4group":{source:"iana",extensions:["c4g","c4d","c4f","c4p","c4u"]},"application/vnd.cluetrust.cartomobile-config":{source:"iana",extensions:["c11amc"]},"application/vnd.cluetrust.cartomobile-config-pkg":{source:"iana",extensions:["c11amz"]},"application/vnd.coffeescript":{source:"iana"},"application/vnd.collabio.xodocuments.document":{source:"iana"},"application/vnd.collabio.xodocuments.document-template":{source:"iana"},"application/vnd.collabio.xodocuments.presentation":{source:"iana"},"application/vnd.collabio.xodocuments.presentation-template":{source:"iana"},"application/vnd.collabio.xodocuments.spreadsheet":{source:"iana"},"application/vnd.collabio.xodocuments.spreadsheet-template":{source:"iana"},"application/vnd.collection+json":{source:"iana",compressible:!0},"application/vnd.collection.doc+json":{source:"iana",compressible:!0},"application/vnd.collection.next+json":{source:"iana",compressible:!0},"application/vnd.comicbook+zip":{source:"iana",compressible:!1},"application/vnd.comicbook-rar":{source:"iana"},"application/vnd.commerce-battelle":{source:"iana"},"application/vnd.commonspace":{source:"iana",extensions:["csp"]},"application/vnd.contact.cmsg":{source:"iana",extensions:["cdbcmsg"]},"application/vnd.coreos.ignition+json":{source:"iana",compressible:!0},"application/vnd.cosmocaller":{source:"iana",extensions:["cmc"]},"application/vnd.crick.clicker":{source:"iana",extensions:["clkx"]},"application/vnd.crick.clicker.keyboard":{source:"iana",extensions:["clkk"]},"application/vnd.crick.clicker.palette":{source:"iana",extensions:["clkp"]},"application/vnd.crick.clicker.template":{source:"iana",extensions:["clkt"]},"application/vnd.crick.clicker.wordbank":{source:"iana",extensions:["clkw"]},"application/vnd.criticaltools.wbs+xml":{source:"iana",compressible:!0,extensions:["wbs"]},"application/vnd.cryptii.pipe+json":{source:"iana",compressible:!0},"application/vnd.crypto-shade-file":{source:"iana"},"application/vnd.cryptomator.encrypted":{source:"iana"},"application/vnd.cryptomator.vault":{source:"iana"},"application/vnd.ctc-posml":{source:"iana",extensions:["pml"]},"application/vnd.ctct.ws+xml":{source:"iana",compressible:!0},"application/vnd.cups-pdf":{source:"iana"},"application/vnd.cups-postscript":{source:"iana"},"application/vnd.cups-ppd":{source:"iana",extensions:["ppd"]},"application/vnd.cups-raster":{source:"iana"},"application/vnd.cups-raw":{source:"iana"},"application/vnd.curl":{source:"iana"},"application/vnd.curl.car":{source:"apache",extensions:["car"]},"application/vnd.curl.pcurl":{source:"apache",extensions:["pcurl"]},"application/vnd.cyan.dean.root+xml":{source:"iana",compressible:!0},"application/vnd.cybank":{source:"iana"},"application/vnd.cyclonedx+json":{source:"iana",compressible:!0},"application/vnd.cyclonedx+xml":{source:"iana",compressible:!0},"application/vnd.d2l.coursepackage1p0+zip":{source:"iana",compressible:!1},"application/vnd.d3m-dataset":{source:"iana"},"application/vnd.d3m-problem":{source:"iana"},"application/vnd.dart":{source:"iana",compressible:!0,extensions:["dart"]},"application/vnd.data-vision.rdz":{source:"iana",extensions:["rdz"]},"application/vnd.datapackage+json":{source:"iana",compressible:!0},"application/vnd.dataresource+json":{source:"iana",compressible:!0},"application/vnd.dbf":{source:"iana",extensions:["dbf"]},"application/vnd.debian.binary-package":{source:"iana"},"application/vnd.dece.data":{source:"iana",extensions:["uvf","uvvf","uvd","uvvd"]},"application/vnd.dece.ttml+xml":{source:"iana",compressible:!0,extensions:["uvt","uvvt"]},"application/vnd.dece.unspecified":{source:"iana",extensions:["uvx","uvvx"]},"application/vnd.dece.zip":{source:"iana",extensions:["uvz","uvvz"]},"application/vnd.denovo.fcselayout-link":{source:"iana",extensions:["fe_launch"]},"application/vnd.desmume.movie":{source:"iana"},"application/vnd.dir-bi.plate-dl-nosuffix":{source:"iana"},"application/vnd.dm.delegation+xml":{source:"iana",compressible:!0},"application/vnd.dna":{source:"iana",extensions:["dna"]},"application/vnd.document+json":{source:"iana",compressible:!0},"application/vnd.dolby.mlp":{source:"apache",extensions:["mlp"]},"application/vnd.dolby.mobile.1":{source:"iana"},"application/vnd.dolby.mobile.2":{source:"iana"},"application/vnd.doremir.scorecloud-binary-document":{source:"iana"},"application/vnd.dpgraph":{source:"iana",extensions:["dpg"]},"application/vnd.dreamfactory":{source:"iana",extensions:["dfac"]},"application/vnd.drive+json":{source:"iana",compressible:!0},"application/vnd.ds-keypoint":{source:"apache",extensions:["kpxx"]},"application/vnd.dtg.local":{source:"iana"},"application/vnd.dtg.local.flash":{source:"iana"},"application/vnd.dtg.local.html":{source:"iana"},"application/vnd.dvb.ait":{source:"iana",extensions:["ait"]},"application/vnd.dvb.dvbisl+xml":{source:"iana",compressible:!0},"application/vnd.dvb.dvbj":{source:"iana"},"application/vnd.dvb.esgcontainer":{source:"iana"},"application/vnd.dvb.ipdcdftnotifaccess":{source:"iana"},"application/vnd.dvb.ipdcesgaccess":{source:"iana"},"application/vnd.dvb.ipdcesgaccess2":{source:"iana"},"application/vnd.dvb.ipdcesgpdd":{source:"iana"},"application/vnd.dvb.ipdcroaming":{source:"iana"},"application/vnd.dvb.iptv.alfec-base":{source:"iana"},"application/vnd.dvb.iptv.alfec-enhancement":{source:"iana"},"application/vnd.dvb.notif-aggregate-root+xml":{source:"iana",compressible:!0},"application/vnd.dvb.notif-container+xml":{source:"iana",compressible:!0},"application/vnd.dvb.notif-generic+xml":{source:"iana",compressible:!0},"application/vnd.dvb.notif-ia-msglist+xml":{source:"iana",compressible:!0},"application/vnd.dvb.notif-ia-registration-request+xml":{source:"iana",compressible:!0},"application/vnd.dvb.notif-ia-registration-response+xml":{source:"iana",compressible:!0},"application/vnd.dvb.notif-init+xml":{source:"iana",compressible:!0},"application/vnd.dvb.pfr":{source:"iana"},"application/vnd.dvb.service":{source:"iana",extensions:["svc"]},"application/vnd.dxr":{source:"iana"},"application/vnd.dynageo":{source:"iana",extensions:["geo"]},"application/vnd.dzr":{source:"iana"},"application/vnd.easykaraoke.cdgdownload":{source:"iana"},"application/vnd.ecdis-update":{source:"iana"},"application/vnd.ecip.rlp":{source:"iana"},"application/vnd.eclipse.ditto+json":{source:"iana",compressible:!0},"application/vnd.ecowin.chart":{source:"iana",extensions:["mag"]},"application/vnd.ecowin.filerequest":{source:"iana"},"application/vnd.ecowin.fileupdate":{source:"iana"},"application/vnd.ecowin.series":{source:"iana"},"application/vnd.ecowin.seriesrequest":{source:"iana"},"application/vnd.ecowin.seriesupdate":{source:"iana"},"application/vnd.efi.img":{source:"iana"},"application/vnd.efi.iso":{source:"iana"},"application/vnd.emclient.accessrequest+xml":{source:"iana",compressible:!0},"application/vnd.enliven":{source:"iana",extensions:["nml"]},"application/vnd.enphase.envoy":{source:"iana"},"application/vnd.eprints.data+xml":{source:"iana",compressible:!0},"application/vnd.epson.esf":{source:"iana",extensions:["esf"]},"application/vnd.epson.msf":{source:"iana",extensions:["msf"]},"application/vnd.epson.quickanime":{source:"iana",extensions:["qam"]},"application/vnd.epson.salt":{source:"iana",extensions:["slt"]},"application/vnd.epson.ssf":{source:"iana",extensions:["ssf"]},"application/vnd.ericsson.quickcall":{source:"iana"},"application/vnd.espass-espass+zip":{source:"iana",compressible:!1},"application/vnd.eszigno3+xml":{source:"iana",compressible:!0,extensions:["es3","et3"]},"application/vnd.etsi.aoc+xml":{source:"iana",compressible:!0},"application/vnd.etsi.asic-e+zip":{source:"iana",compressible:!1},"application/vnd.etsi.asic-s+zip":{source:"iana",compressible:!1},"application/vnd.etsi.cug+xml":{source:"iana",compressible:!0},"application/vnd.etsi.iptvcommand+xml":{source:"iana",compressible:!0},"application/vnd.etsi.iptvdiscovery+xml":{source:"iana",compressible:!0},"application/vnd.etsi.iptvprofile+xml":{source:"iana",compressible:!0},"application/vnd.etsi.iptvsad-bc+xml":{source:"iana",compressible:!0},"application/vnd.etsi.iptvsad-cod+xml":{source:"iana",compressible:!0},"application/vnd.etsi.iptvsad-npvr+xml":{source:"iana",compressible:!0},"application/vnd.etsi.iptvservice+xml":{source:"iana",compressible:!0},"application/vnd.etsi.iptvsync+xml":{source:"iana",compressible:!0},"application/vnd.etsi.iptvueprofile+xml":{source:"iana",compressible:!0},"application/vnd.etsi.mcid+xml":{source:"iana",compressible:!0},"application/vnd.etsi.mheg5":{source:"iana"},"application/vnd.etsi.overload-control-policy-dataset+xml":{source:"iana",compressible:!0},"application/vnd.etsi.pstn+xml":{source:"iana",compressible:!0},"application/vnd.etsi.sci+xml":{source:"iana",compressible:!0},"application/vnd.etsi.simservs+xml":{source:"iana",compressible:!0},"application/vnd.etsi.timestamp-token":{source:"iana"},"application/vnd.etsi.tsl+xml":{source:"iana",compressible:!0},"application/vnd.etsi.tsl.der":{source:"iana"},"application/vnd.eu.kasparian.car+json":{source:"iana",compressible:!0},"application/vnd.eudora.data":{source:"iana"},"application/vnd.evolv.ecig.profile":{source:"iana"},"application/vnd.evolv.ecig.settings":{source:"iana"},"application/vnd.evolv.ecig.theme":{source:"iana"},"application/vnd.exstream-empower+zip":{source:"iana",compressible:!1},"application/vnd.exstream-package":{source:"iana"},"application/vnd.ezpix-album":{source:"iana",extensions:["ez2"]},"application/vnd.ezpix-package":{source:"iana",extensions:["ez3"]},"application/vnd.f-secure.mobile":{source:"iana"},"application/vnd.familysearch.gedcom+zip":{source:"iana",compressible:!1},"application/vnd.fastcopy-disk-image":{source:"iana"},"application/vnd.fdf":{source:"iana",extensions:["fdf"]},"application/vnd.fdsn.mseed":{source:"iana",extensions:["mseed"]},"application/vnd.fdsn.seed":{source:"iana",extensions:["seed","dataless"]},"application/vnd.ffsns":{source:"iana"},"application/vnd.ficlab.flb+zip":{source:"iana",compressible:!1},"application/vnd.filmit.zfc":{source:"iana"},"application/vnd.fints":{source:"iana"},"application/vnd.firemonkeys.cloudcell":{source:"iana"},"application/vnd.flographit":{source:"iana",extensions:["gph"]},"application/vnd.fluxtime.clip":{source:"iana",extensions:["ftc"]},"application/vnd.font-fontforge-sfd":{source:"iana"},"application/vnd.framemaker":{source:"iana",extensions:["fm","frame","maker","book"]},"application/vnd.frogans.fnc":{source:"iana",extensions:["fnc"]},"application/vnd.frogans.ltf":{source:"iana",extensions:["ltf"]},"application/vnd.fsc.weblaunch":{source:"iana",extensions:["fsc"]},"application/vnd.fujifilm.fb.docuworks":{source:"iana"},"application/vnd.fujifilm.fb.docuworks.binder":{source:"iana"},"application/vnd.fujifilm.fb.docuworks.container":{source:"iana"},"application/vnd.fujifilm.fb.jfi+xml":{source:"iana",compressible:!0},"application/vnd.fujitsu.oasys":{source:"iana",extensions:["oas"]},"application/vnd.fujitsu.oasys2":{source:"iana",extensions:["oa2"]},"application/vnd.fujitsu.oasys3":{source:"iana",extensions:["oa3"]},"application/vnd.fujitsu.oasysgp":{source:"iana",extensions:["fg5"]},"application/vnd.fujitsu.oasysprs":{source:"iana",extensions:["bh2"]},"application/vnd.fujixerox.art-ex":{source:"iana"},"application/vnd.fujixerox.art4":{source:"iana"},"application/vnd.fujixerox.ddd":{source:"iana",extensions:["ddd"]},"application/vnd.fujixerox.docuworks":{source:"iana",extensions:["xdw"]},"application/vnd.fujixerox.docuworks.binder":{source:"iana",extensions:["xbd"]},"application/vnd.fujixerox.docuworks.container":{source:"iana"},"application/vnd.fujixerox.hbpl":{source:"iana"},"application/vnd.fut-misnet":{source:"iana"},"application/vnd.futoin+cbor":{source:"iana"},"application/vnd.futoin+json":{source:"iana",compressible:!0},"application/vnd.fuzzysheet":{source:"iana",extensions:["fzs"]},"application/vnd.genomatix.tuxedo":{source:"iana",extensions:["txd"]},"application/vnd.gentics.grd+json":{source:"iana",compressible:!0},"application/vnd.geo+json":{source:"iana",compressible:!0},"application/vnd.geocube+xml":{source:"iana",compressible:!0},"application/vnd.geogebra.file":{source:"iana",extensions:["ggb"]},"application/vnd.geogebra.slides":{source:"iana"},"application/vnd.geogebra.tool":{source:"iana",extensions:["ggt"]},"application/vnd.geometry-explorer":{source:"iana",extensions:["gex","gre"]},"application/vnd.geonext":{source:"iana",extensions:["gxt"]},"application/vnd.geoplan":{source:"iana",extensions:["g2w"]},"application/vnd.geospace":{source:"iana",extensions:["g3w"]},"application/vnd.gerber":{source:"iana"},"application/vnd.globalplatform.card-content-mgt":{source:"iana"},"application/vnd.globalplatform.card-content-mgt-response":{source:"iana"},"application/vnd.gmx":{source:"iana",extensions:["gmx"]},"application/vnd.google-apps.document":{compressible:!1,extensions:["gdoc"]},"application/vnd.google-apps.presentation":{compressible:!1,extensions:["gslides"]},"application/vnd.google-apps.spreadsheet":{compressible:!1,extensions:["gsheet"]},"application/vnd.google-earth.kml+xml":{source:"iana",compressible:!0,extensions:["kml"]},"application/vnd.google-earth.kmz":{source:"iana",compressible:!1,extensions:["kmz"]},"application/vnd.gov.sk.e-form+xml":{source:"iana",compressible:!0},"application/vnd.gov.sk.e-form+zip":{source:"iana",compressible:!1},"application/vnd.gov.sk.xmldatacontainer+xml":{source:"iana",compressible:!0},"application/vnd.grafeq":{source:"iana",extensions:["gqf","gqs"]},"application/vnd.gridmp":{source:"iana"},"application/vnd.groove-account":{source:"iana",extensions:["gac"]},"application/vnd.groove-help":{source:"iana",extensions:["ghf"]},"application/vnd.groove-identity-message":{source:"iana",extensions:["gim"]},"application/vnd.groove-injector":{source:"iana",extensions:["grv"]},"application/vnd.groove-tool-message":{source:"iana",extensions:["gtm"]},"application/vnd.groove-tool-template":{source:"iana",extensions:["tpl"]},"application/vnd.groove-vcard":{source:"iana",extensions:["vcg"]},"application/vnd.hal+json":{source:"iana",compressible:!0},"application/vnd.hal+xml":{source:"iana",compressible:!0,extensions:["hal"]},"application/vnd.handheld-entertainment+xml":{source:"iana",compressible:!0,extensions:["zmm"]},"application/vnd.hbci":{source:"iana",extensions:["hbci"]},"application/vnd.hc+json":{source:"iana",compressible:!0},"application/vnd.hcl-bireports":{source:"iana"},"application/vnd.hdt":{source:"iana"},"application/vnd.heroku+json":{source:"iana",compressible:!0},"application/vnd.hhe.lesson-player":{source:"iana",extensions:["les"]},"application/vnd.hl7cda+xml":{source:"iana",charset:"UTF-8",compressible:!0},"application/vnd.hl7v2+xml":{source:"iana",charset:"UTF-8",compressible:!0},"application/vnd.hp-hpgl":{source:"iana",extensions:["hpgl"]},"application/vnd.hp-hpid":{source:"iana",extensions:["hpid"]},"application/vnd.hp-hps":{source:"iana",extensions:["hps"]},"application/vnd.hp-jlyt":{source:"iana",extensions:["jlt"]},"application/vnd.hp-pcl":{source:"iana",extensions:["pcl"]},"application/vnd.hp-pclxl":{source:"iana",extensions:["pclxl"]},"application/vnd.httphone":{source:"iana"},"application/vnd.hydrostatix.sof-data":{source:"iana",extensions:["sfd-hdstx"]},"application/vnd.hyper+json":{source:"iana",compressible:!0},"application/vnd.hyper-item+json":{source:"iana",compressible:!0},"application/vnd.hyperdrive+json":{source:"iana",compressible:!0},"application/vnd.hzn-3d-crossword":{source:"iana"},"application/vnd.ibm.afplinedata":{source:"iana"},"application/vnd.ibm.electronic-media":{source:"iana"},"application/vnd.ibm.minipay":{source:"iana",extensions:["mpy"]},"application/vnd.ibm.modcap":{source:"iana",extensions:["afp","listafp","list3820"]},"application/vnd.ibm.rights-management":{source:"iana",extensions:["irm"]},"application/vnd.ibm.secure-container":{source:"iana",extensions:["sc"]},"application/vnd.iccprofile":{source:"iana",extensions:["icc","icm"]},"application/vnd.ieee.1905":{source:"iana"},"application/vnd.igloader":{source:"iana",extensions:["igl"]},"application/vnd.imagemeter.folder+zip":{source:"iana",compressible:!1},"application/vnd.imagemeter.image+zip":{source:"iana",compressible:!1},"application/vnd.immervision-ivp":{source:"iana",extensions:["ivp"]},"application/vnd.immervision-ivu":{source:"iana",extensions:["ivu"]},"application/vnd.ims.imsccv1p1":{source:"iana"},"application/vnd.ims.imsccv1p2":{source:"iana"},"application/vnd.ims.imsccv1p3":{source:"iana"},"application/vnd.ims.lis.v2.result+json":{source:"iana",compressible:!0},"application/vnd.ims.lti.v2.toolconsumerprofile+json":{source:"iana",compressible:!0},"application/vnd.ims.lti.v2.toolproxy+json":{source:"iana",compressible:!0},"application/vnd.ims.lti.v2.toolproxy.id+json":{source:"iana",compressible:!0},"application/vnd.ims.lti.v2.toolsettings+json":{source:"iana",compressible:!0},"application/vnd.ims.lti.v2.toolsettings.simple+json":{source:"iana",compressible:!0},"application/vnd.informedcontrol.rms+xml":{source:"iana",compressible:!0},"application/vnd.informix-visionary":{source:"iana"},"application/vnd.infotech.project":{source:"iana"},"application/vnd.infotech.project+xml":{source:"iana",compressible:!0},"application/vnd.innopath.wamp.notification":{source:"iana"},"application/vnd.insors.igm":{source:"iana",extensions:["igm"]},"application/vnd.intercon.formnet":{source:"iana",extensions:["xpw","xpx"]},"application/vnd.intergeo":{source:"iana",extensions:["i2g"]},"application/vnd.intertrust.digibox":{source:"iana"},"application/vnd.intertrust.nncp":{source:"iana"},"application/vnd.intu.qbo":{source:"iana",extensions:["qbo"]},"application/vnd.intu.qfx":{source:"iana",extensions:["qfx"]},"application/vnd.iptc.g2.catalogitem+xml":{source:"iana",compressible:!0},"application/vnd.iptc.g2.conceptitem+xml":{source:"iana",compressible:!0},"application/vnd.iptc.g2.knowledgeitem+xml":{source:"iana",compressible:!0},"application/vnd.iptc.g2.newsitem+xml":{source:"iana",compressible:!0},"application/vnd.iptc.g2.newsmessage+xml":{source:"iana",compressible:!0},"application/vnd.iptc.g2.packageitem+xml":{source:"iana",compressible:!0},"application/vnd.iptc.g2.planningitem+xml":{source:"iana",compressible:!0},"application/vnd.ipunplugged.rcprofile":{source:"iana",extensions:["rcprofile"]},"application/vnd.irepository.package+xml":{source:"iana",compressible:!0,extensions:["irp"]},"application/vnd.is-xpr":{source:"iana",extensions:["xpr"]},"application/vnd.isac.fcs":{source:"iana",extensions:["fcs"]},"application/vnd.iso11783-10+zip":{source:"iana",compressible:!1},"application/vnd.jam":{source:"iana",extensions:["jam"]},"application/vnd.japannet-directory-service":{source:"iana"},"application/vnd.japannet-jpnstore-wakeup":{source:"iana"},"application/vnd.japannet-payment-wakeup":{source:"iana"},"application/vnd.japannet-registration":{source:"iana"},"application/vnd.japannet-registration-wakeup":{source:"iana"},"application/vnd.japannet-setstore-wakeup":{source:"iana"},"application/vnd.japannet-verification":{source:"iana"},"application/vnd.japannet-verification-wakeup":{source:"iana"},"application/vnd.jcp.javame.midlet-rms":{source:"iana",extensions:["rms"]},"application/vnd.jisp":{source:"iana",extensions:["jisp"]},"application/vnd.joost.joda-archive":{source:"iana",extensions:["joda"]},"application/vnd.jsk.isdn-ngn":{source:"iana"},"application/vnd.kahootz":{source:"iana",extensions:["ktz","ktr"]},"application/vnd.kde.karbon":{source:"iana",extensions:["karbon"]},"application/vnd.kde.kchart":{source:"iana",extensions:["chrt"]},"application/vnd.kde.kformula":{source:"iana",extensions:["kfo"]},"application/vnd.kde.kivio":{source:"iana",extensions:["flw"]},"application/vnd.kde.kontour":{source:"iana",extensions:["kon"]},"application/vnd.kde.kpresenter":{source:"iana",extensions:["kpr","kpt"]},"application/vnd.kde.kspread":{source:"iana",extensions:["ksp"]},"application/vnd.kde.kword":{source:"iana",extensions:["kwd","kwt"]},"application/vnd.kenameaapp":{source:"iana",extensions:["htke"]},"application/vnd.kidspiration":{source:"iana",extensions:["kia"]},"application/vnd.kinar":{source:"iana",extensions:["kne","knp"]},"application/vnd.koan":{source:"iana",extensions:["skp","skd","skt","skm"]},"application/vnd.kodak-descriptor":{source:"iana",extensions:["sse"]},"application/vnd.las":{source:"iana"},"application/vnd.las.las+json":{source:"iana",compressible:!0},"application/vnd.las.las+xml":{source:"iana",compressible:!0,extensions:["lasxml"]},"application/vnd.laszip":{source:"iana"},"application/vnd.leap+json":{source:"iana",compressible:!0},"application/vnd.liberty-request+xml":{source:"iana",compressible:!0},"application/vnd.llamagraphics.life-balance.desktop":{source:"iana",extensions:["lbd"]},"application/vnd.llamagraphics.life-balance.exchange+xml":{source:"iana",compressible:!0,extensions:["lbe"]},"application/vnd.logipipe.circuit+zip":{source:"iana",compressible:!1},"application/vnd.loom":{source:"iana"},"application/vnd.lotus-1-2-3":{source:"iana",extensions:["123"]},"application/vnd.lotus-approach":{source:"iana",extensions:["apr"]},"application/vnd.lotus-freelance":{source:"iana",extensions:["pre"]},"application/vnd.lotus-notes":{source:"iana",extensions:["nsf"]},"application/vnd.lotus-organizer":{source:"iana",extensions:["org"]},"application/vnd.lotus-screencam":{source:"iana",extensions:["scm"]},"application/vnd.lotus-wordpro":{source:"iana",extensions:["lwp"]},"application/vnd.macports.portpkg":{source:"iana",extensions:["portpkg"]},"application/vnd.mapbox-vector-tile":{source:"iana",extensions:["mvt"]},"application/vnd.marlin.drm.actiontoken+xml":{source:"iana",compressible:!0},"application/vnd.marlin.drm.conftoken+xml":{source:"iana",compressible:!0},"application/vnd.marlin.drm.license+xml":{source:"iana",compressible:!0},"application/vnd.marlin.drm.mdcf":{source:"iana"},"application/vnd.mason+json":{source:"iana",compressible:!0},"application/vnd.maxar.archive.3tz+zip":{source:"iana",compressible:!1},"application/vnd.maxmind.maxmind-db":{source:"iana"},"application/vnd.mcd":{source:"iana",extensions:["mcd"]},"application/vnd.medcalcdata":{source:"iana",extensions:["mc1"]},"application/vnd.mediastation.cdkey":{source:"iana",extensions:["cdkey"]},"application/vnd.meridian-slingshot":{source:"iana"},"application/vnd.mfer":{source:"iana",extensions:["mwf"]},"application/vnd.mfmp":{source:"iana",extensions:["mfm"]},"application/vnd.micro+json":{source:"iana",compressible:!0},"application/vnd.micrografx.flo":{source:"iana",extensions:["flo"]},"application/vnd.micrografx.igx":{source:"iana",extensions:["igx"]},"application/vnd.microsoft.portable-executable":{source:"iana"},"application/vnd.microsoft.windows.thumbnail-cache":{source:"iana"},"application/vnd.miele+json":{source:"iana",compressible:!0},"application/vnd.mif":{source:"iana",extensions:["mif"]},"application/vnd.minisoft-hp3000-save":{source:"iana"},"application/vnd.mitsubishi.misty-guard.trustweb":{source:"iana"},"application/vnd.mobius.daf":{source:"iana",extensions:["daf"]},"application/vnd.mobius.dis":{source:"iana",extensions:["dis"]},"application/vnd.mobius.mbk":{source:"iana",extensions:["mbk"]},"application/vnd.mobius.mqy":{source:"iana",extensions:["mqy"]},"application/vnd.mobius.msl":{source:"iana",extensions:["msl"]},"application/vnd.mobius.plc":{source:"iana",extensions:["plc"]},"application/vnd.mobius.txf":{source:"iana",extensions:["txf"]},"application/vnd.mophun.application":{source:"iana",extensions:["mpn"]},"application/vnd.mophun.certificate":{source:"iana",extensions:["mpc"]},"application/vnd.motorola.flexsuite":{source:"iana"},"application/vnd.motorola.flexsuite.adsi":{source:"iana"},"application/vnd.motorola.flexsuite.fis":{source:"iana"},"application/vnd.motorola.flexsuite.gotap":{source:"iana"},"application/vnd.motorola.flexsuite.kmr":{source:"iana"},"application/vnd.motorola.flexsuite.ttc":{source:"iana"},"application/vnd.motorola.flexsuite.wem":{source:"iana"},"application/vnd.motorola.iprm":{source:"iana"},"application/vnd.mozilla.xul+xml":{source:"iana",compressible:!0,extensions:["xul"]},"application/vnd.ms-3mfdocument":{source:"iana"},"application/vnd.ms-artgalry":{source:"iana",extensions:["cil"]},"application/vnd.ms-asf":{source:"iana"},"application/vnd.ms-cab-compressed":{source:"iana",extensions:["cab"]},"application/vnd.ms-color.iccprofile":{source:"apache"},"application/vnd.ms-excel":{source:"iana",compressible:!1,extensions:["xls","xlm","xla","xlc","xlt","xlw"]},"application/vnd.ms-excel.addin.macroenabled.12":{source:"iana",extensions:["xlam"]},"application/vnd.ms-excel.sheet.binary.macroenabled.12":{source:"iana",extensions:["xlsb"]},"application/vnd.ms-excel.sheet.macroenabled.12":{source:"iana",extensions:["xlsm"]},"application/vnd.ms-excel.template.macroenabled.12":{source:"iana",extensions:["xltm"]},"application/vnd.ms-fontobject":{source:"iana",compressible:!0,extensions:["eot"]},"application/vnd.ms-htmlhelp":{source:"iana",extensions:["chm"]},"application/vnd.ms-ims":{source:"iana",extensions:["ims"]},"application/vnd.ms-lrm":{source:"iana",extensions:["lrm"]},"application/vnd.ms-office.activex+xml":{source:"iana",compressible:!0},"application/vnd.ms-officetheme":{source:"iana",extensions:["thmx"]},"application/vnd.ms-opentype":{source:"apache",compressible:!0},"application/vnd.ms-outlook":{compressible:!1,extensions:["msg"]},"application/vnd.ms-package.obfuscated-opentype":{source:"apache"},"application/vnd.ms-pki.seccat":{source:"apache",extensions:["cat"]},"application/vnd.ms-pki.stl":{source:"apache",extensions:["stl"]},"application/vnd.ms-playready.initiator+xml":{source:"iana",compressible:!0},"application/vnd.ms-powerpoint":{source:"iana",compressible:!1,extensions:["ppt","pps","pot"]},"application/vnd.ms-powerpoint.addin.macroenabled.12":{source:"iana",extensions:["ppam"]},"application/vnd.ms-powerpoint.presentation.macroenabled.12":{source:"iana",extensions:["pptm"]},"application/vnd.ms-powerpoint.slide.macroenabled.12":{source:"iana",extensions:["sldm"]},"application/vnd.ms-powerpoint.slideshow.macroenabled.12":{source:"iana",extensions:["ppsm"]},"application/vnd.ms-powerpoint.template.macroenabled.12":{source:"iana",extensions:["potm"]},"application/vnd.ms-printdevicecapabilities+xml":{source:"iana",compressible:!0},"application/vnd.ms-printing.printticket+xml":{source:"apache",compressible:!0},"application/vnd.ms-printschematicket+xml":{source:"iana",compressible:!0},"application/vnd.ms-project":{source:"iana",extensions:["mpp","mpt"]},"application/vnd.ms-tnef":{source:"iana"},"application/vnd.ms-windows.devicepairing":{source:"iana"},"application/vnd.ms-windows.nwprinting.oob":{source:"iana"},"application/vnd.ms-windows.printerpairing":{source:"iana"},"application/vnd.ms-windows.wsd.oob":{source:"iana"},"application/vnd.ms-wmdrm.lic-chlg-req":{source:"iana"},"application/vnd.ms-wmdrm.lic-resp":{source:"iana"},"application/vnd.ms-wmdrm.meter-chlg-req":{source:"iana"},"application/vnd.ms-wmdrm.meter-resp":{source:"iana"},"application/vnd.ms-word.document.macroenabled.12":{source:"iana",extensions:["docm"]},"application/vnd.ms-word.template.macroenabled.12":{source:"iana",extensions:["dotm"]},"application/vnd.ms-works":{source:"iana",extensions:["wps","wks","wcm","wdb"]},"application/vnd.ms-wpl":{source:"iana",extensions:["wpl"]},"application/vnd.ms-xpsdocument":{source:"iana",compressible:!1,extensions:["xps"]},"application/vnd.msa-disk-image":{source:"iana"},"application/vnd.mseq":{source:"iana",extensions:["mseq"]},"application/vnd.msign":{source:"iana"},"application/vnd.multiad.creator":{source:"iana"},"application/vnd.multiad.creator.cif":{source:"iana"},"application/vnd.music-niff":{source:"iana"},"application/vnd.musician":{source:"iana",extensions:["mus"]},"application/vnd.muvee.style":{source:"iana",extensions:["msty"]},"application/vnd.mynfc":{source:"iana",extensions:["taglet"]},"application/vnd.nacamar.ybrid+json":{source:"iana",compressible:!0},"application/vnd.ncd.control":{source:"iana"},"application/vnd.ncd.reference":{source:"iana"},"application/vnd.nearst.inv+json":{source:"iana",compressible:!0},"application/vnd.nebumind.line":{source:"iana"},"application/vnd.nervana":{source:"iana"},"application/vnd.netfpx":{source:"iana"},"application/vnd.neurolanguage.nlu":{source:"iana",extensions:["nlu"]},"application/vnd.nimn":{source:"iana"},"application/vnd.nintendo.nitro.rom":{source:"iana"},"application/vnd.nintendo.snes.rom":{source:"iana"},"application/vnd.nitf":{source:"iana",extensions:["ntf","nitf"]},"application/vnd.noblenet-directory":{source:"iana",extensions:["nnd"]},"application/vnd.noblenet-sealer":{source:"iana",extensions:["nns"]},"application/vnd.noblenet-web":{source:"iana",extensions:["nnw"]},"application/vnd.nokia.catalogs":{source:"iana"},"application/vnd.nokia.conml+wbxml":{source:"iana"},"application/vnd.nokia.conml+xml":{source:"iana",compressible:!0},"application/vnd.nokia.iptv.config+xml":{source:"iana",compressible:!0},"application/vnd.nokia.isds-radio-presets":{source:"iana"},"application/vnd.nokia.landmark+wbxml":{source:"iana"},"application/vnd.nokia.landmark+xml":{source:"iana",compressible:!0},"application/vnd.nokia.landmarkcollection+xml":{source:"iana",compressible:!0},"application/vnd.nokia.n-gage.ac+xml":{source:"iana",compressible:!0,extensions:["ac"]},"application/vnd.nokia.n-gage.data":{source:"iana",extensions:["ngdat"]},"application/vnd.nokia.n-gage.symbian.install":{source:"iana",extensions:["n-gage"]},"application/vnd.nokia.ncd":{source:"iana"},"application/vnd.nokia.pcd+wbxml":{source:"iana"},"application/vnd.nokia.pcd+xml":{source:"iana",compressible:!0},"application/vnd.nokia.radio-preset":{source:"iana",extensions:["rpst"]},"application/vnd.nokia.radio-presets":{source:"iana",extensions:["rpss"]},"application/vnd.novadigm.edm":{source:"iana",extensions:["edm"]},"application/vnd.novadigm.edx":{source:"iana",extensions:["edx"]},"application/vnd.novadigm.ext":{source:"iana",extensions:["ext"]},"application/vnd.ntt-local.content-share":{source:"iana"},"application/vnd.ntt-local.file-transfer":{source:"iana"},"application/vnd.ntt-local.ogw_remote-access":{source:"iana"},"application/vnd.ntt-local.sip-ta_remote":{source:"iana"},"application/vnd.ntt-local.sip-ta_tcp_stream":{source:"iana"},"application/vnd.oasis.opendocument.chart":{source:"iana",extensions:["odc"]},"application/vnd.oasis.opendocument.chart-template":{source:"iana",extensions:["otc"]},"application/vnd.oasis.opendocument.database":{source:"iana",extensions:["odb"]},"application/vnd.oasis.opendocument.formula":{source:"iana",extensions:["odf"]},"application/vnd.oasis.opendocument.formula-template":{source:"iana",extensions:["odft"]},"application/vnd.oasis.opendocument.graphics":{source:"iana",compressible:!1,extensions:["odg"]},"application/vnd.oasis.opendocument.graphics-template":{source:"iana",extensions:["otg"]},"application/vnd.oasis.opendocument.image":{source:"iana",extensions:["odi"]},"application/vnd.oasis.opendocument.image-template":{source:"iana",extensions:["oti"]},"application/vnd.oasis.opendocument.presentation":{source:"iana",compressible:!1,extensions:["odp"]},"application/vnd.oasis.opendocument.presentation-template":{source:"iana",extensions:["otp"]},"application/vnd.oasis.opendocument.spreadsheet":{source:"iana",compressible:!1,extensions:["ods"]},"application/vnd.oasis.opendocument.spreadsheet-template":{source:"iana",extensions:["ots"]},"application/vnd.oasis.opendocument.text":{source:"iana",compressible:!1,extensions:["odt"]},"application/vnd.oasis.opendocument.text-master":{source:"iana",extensions:["odm"]},"application/vnd.oasis.opendocument.text-template":{source:"iana",extensions:["ott"]},"application/vnd.oasis.opendocument.text-web":{source:"iana",extensions:["oth"]},"application/vnd.obn":{source:"iana"},"application/vnd.ocf+cbor":{source:"iana"},"application/vnd.oci.image.manifest.v1+json":{source:"iana",compressible:!0},"application/vnd.oftn.l10n+json":{source:"iana",compressible:!0},"application/vnd.oipf.contentaccessdownload+xml":{source:"iana",compressible:!0},"application/vnd.oipf.contentaccessstreaming+xml":{source:"iana",compressible:!0},"application/vnd.oipf.cspg-hexbinary":{source:"iana"},"application/vnd.oipf.dae.svg+xml":{source:"iana",compressible:!0},"application/vnd.oipf.dae.xhtml+xml":{source:"iana",compressible:!0},"application/vnd.oipf.mippvcontrolmessage+xml":{source:"iana",compressible:!0},"application/vnd.oipf.pae.gem":{source:"iana"},"application/vnd.oipf.spdiscovery+xml":{source:"iana",compressible:!0},"application/vnd.oipf.spdlist+xml":{source:"iana",compressible:!0},"application/vnd.oipf.ueprofile+xml":{source:"iana",compressible:!0},"application/vnd.oipf.userprofile+xml":{source:"iana",compressible:!0},"application/vnd.olpc-sugar":{source:"iana",extensions:["xo"]},"application/vnd.oma-scws-config":{source:"iana"},"application/vnd.oma-scws-http-request":{source:"iana"},"application/vnd.oma-scws-http-response":{source:"iana"},"application/vnd.oma.bcast.associated-procedure-parameter+xml":{source:"iana",compressible:!0},"application/vnd.oma.bcast.drm-trigger+xml":{source:"iana",compressible:!0},"application/vnd.oma.bcast.imd+xml":{source:"iana",compressible:!0},"application/vnd.oma.bcast.ltkm":{source:"iana"},"application/vnd.oma.bcast.notification+xml":{source:"iana",compressible:!0},"application/vnd.oma.bcast.provisioningtrigger":{source:"iana"},"application/vnd.oma.bcast.sgboot":{source:"iana"},"application/vnd.oma.bcast.sgdd+xml":{source:"iana",compressible:!0},"application/vnd.oma.bcast.sgdu":{source:"iana"},"application/vnd.oma.bcast.simple-symbol-container":{source:"iana"},"application/vnd.oma.bcast.smartcard-trigger+xml":{source:"iana",compressible:!0},"application/vnd.oma.bcast.sprov+xml":{source:"iana",compressible:!0},"application/vnd.oma.bcast.stkm":{source:"iana"},"application/vnd.oma.cab-address-book+xml":{source:"iana",compressible:!0},"application/vnd.oma.cab-feature-handler+xml":{source:"iana",compressible:!0},"application/vnd.oma.cab-pcc+xml":{source:"iana",compressible:!0},"application/vnd.oma.cab-subs-invite+xml":{source:"iana",compressible:!0},"application/vnd.oma.cab-user-prefs+xml":{source:"iana",compressible:!0},"application/vnd.oma.dcd":{source:"iana"},"application/vnd.oma.dcdc":{source:"iana"},"application/vnd.oma.dd2+xml":{source:"iana",compressible:!0,extensions:["dd2"]},"application/vnd.oma.drm.risd+xml":{source:"iana",compressible:!0},"application/vnd.oma.group-usage-list+xml":{source:"iana",compressible:!0},"application/vnd.oma.lwm2m+cbor":{source:"iana"},"application/vnd.oma.lwm2m+json":{source:"iana",compressible:!0},"application/vnd.oma.lwm2m+tlv":{source:"iana"},"application/vnd.oma.pal+xml":{source:"iana",compressible:!0},"application/vnd.oma.poc.detailed-progress-report+xml":{source:"iana",compressible:!0},"application/vnd.oma.poc.final-report+xml":{source:"iana",compressible:!0},"application/vnd.oma.poc.groups+xml":{source:"iana",compressible:!0},"application/vnd.oma.poc.invocation-descriptor+xml":{source:"iana",compressible:!0},"application/vnd.oma.poc.optimized-progress-report+xml":{source:"iana",compressible:!0},"application/vnd.oma.push":{source:"iana"},"application/vnd.oma.scidm.messages+xml":{source:"iana",compressible:!0},"application/vnd.oma.xcap-directory+xml":{source:"iana",compressible:!0},"application/vnd.omads-email+xml":{source:"iana",charset:"UTF-8",compressible:!0},"application/vnd.omads-file+xml":{source:"iana",charset:"UTF-8",compressible:!0},"application/vnd.omads-folder+xml":{source:"iana",charset:"UTF-8",compressible:!0},"application/vnd.omaloc-supl-init":{source:"iana"},"application/vnd.onepager":{source:"iana"},"application/vnd.onepagertamp":{source:"iana"},"application/vnd.onepagertamx":{source:"iana"},"application/vnd.onepagertat":{source:"iana"},"application/vnd.onepagertatp":{source:"iana"},"application/vnd.onepagertatx":{source:"iana"},"application/vnd.openblox.game+xml":{source:"iana",compressible:!0,extensions:["obgx"]},"application/vnd.openblox.game-binary":{source:"iana"},"application/vnd.openeye.oeb":{source:"iana"},"application/vnd.openofficeorg.extension":{source:"apache",extensions:["oxt"]},"application/vnd.openstreetmap.data+xml":{source:"iana",compressible:!0,extensions:["osm"]},"application/vnd.opentimestamps.ots":{source:"iana"},"application/vnd.openxmlformats-officedocument.custom-properties+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.customxmlproperties+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.drawing+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.drawingml.chart+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.drawingml.chartshapes+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.drawingml.diagramcolors+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.drawingml.diagramdata+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.drawingml.diagramlayout+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.drawingml.diagramstyle+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.extended-properties+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.presentationml.commentauthors+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.presentationml.comments+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.presentationml.handoutmaster+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.presentationml.notesmaster+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.presentationml.notesslide+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.presentationml.presentation":{source:"iana",compressible:!1,extensions:["pptx"]},"application/vnd.openxmlformats-officedocument.presentationml.presentation.main+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.presentationml.presprops+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.presentationml.slide":{source:"iana",extensions:["sldx"]},"application/vnd.openxmlformats-officedocument.presentationml.slide+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.presentationml.slidelayout+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.presentationml.slidemaster+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.presentationml.slideshow":{source:"iana",extensions:["ppsx"]},"application/vnd.openxmlformats-officedocument.presentationml.slideshow.main+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.presentationml.slideupdateinfo+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.presentationml.tablestyles+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.presentationml.tags+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.presentationml.template":{source:"iana",extensions:["potx"]},"application/vnd.openxmlformats-officedocument.presentationml.template.main+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.presentationml.viewprops+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.spreadsheetml.calcchain+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.spreadsheetml.chartsheet+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.spreadsheetml.comments+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.spreadsheetml.connections+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.spreadsheetml.dialogsheet+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.spreadsheetml.externallink+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.spreadsheetml.pivotcachedefinition+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.spreadsheetml.pivotcacherecords+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.spreadsheetml.pivottable+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.spreadsheetml.querytable+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.spreadsheetml.revisionheaders+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.spreadsheetml.revisionlog+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.spreadsheetml.sharedstrings+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet":{source:"iana",compressible:!1,extensions:["xlsx"]},"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet.main+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.spreadsheetml.sheetmetadata+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.spreadsheetml.styles+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.spreadsheetml.table+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.spreadsheetml.tablesinglecells+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.spreadsheetml.template":{source:"iana",extensions:["xltx"]},"application/vnd.openxmlformats-officedocument.spreadsheetml.template.main+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.spreadsheetml.usernames+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.spreadsheetml.volatiledependencies+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.spreadsheetml.worksheet+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.theme+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.themeoverride+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.vmldrawing":{source:"iana"},"application/vnd.openxmlformats-officedocument.wordprocessingml.comments+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.wordprocessingml.document":{source:"iana",compressible:!1,extensions:["docx"]},"application/vnd.openxmlformats-officedocument.wordprocessingml.document.glossary+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.wordprocessingml.document.main+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.wordprocessingml.endnotes+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.wordprocessingml.fonttable+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.wordprocessingml.footer+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.wordprocessingml.footnotes+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.wordprocessingml.numbering+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.wordprocessingml.settings+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.wordprocessingml.styles+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.wordprocessingml.template":{source:"iana",extensions:["dotx"]},"application/vnd.openxmlformats-officedocument.wordprocessingml.template.main+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-officedocument.wordprocessingml.websettings+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-package.core-properties+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-package.digital-signature-xmlsignature+xml":{source:"iana",compressible:!0},"application/vnd.openxmlformats-package.relationships+xml":{source:"iana",compressible:!0},"application/vnd.oracle.resource+json":{source:"iana",compressible:!0},"application/vnd.orange.indata":{source:"iana"},"application/vnd.osa.netdeploy":{source:"iana"},"application/vnd.osgeo.mapguide.package":{source:"iana",extensions:["mgp"]},"application/vnd.osgi.bundle":{source:"iana"},"application/vnd.osgi.dp":{source:"iana",extensions:["dp"]},"application/vnd.osgi.subsystem":{source:"iana",extensions:["esa"]},"application/vnd.otps.ct-kip+xml":{source:"iana",compressible:!0},"application/vnd.oxli.countgraph":{source:"iana"},"application/vnd.pagerduty+json":{source:"iana",compressible:!0},"application/vnd.palm":{source:"iana",extensions:["pdb","pqa","oprc"]},"application/vnd.panoply":{source:"iana"},"application/vnd.paos.xml":{source:"iana"},"application/vnd.patentdive":{source:"iana"},"application/vnd.patientecommsdoc":{source:"iana"},"application/vnd.pawaafile":{source:"iana",extensions:["paw"]},"application/vnd.pcos":{source:"iana"},"application/vnd.pg.format":{source:"iana",extensions:["str"]},"application/vnd.pg.osasli":{source:"iana",extensions:["ei6"]},"application/vnd.piaccess.application-licence":{source:"iana"},"application/vnd.picsel":{source:"iana",extensions:["efif"]},"application/vnd.pmi.widget":{source:"iana",extensions:["wg"]},"application/vnd.poc.group-advertisement+xml":{source:"iana",compressible:!0},"application/vnd.pocketlearn":{source:"iana",extensions:["plf"]},"application/vnd.powerbuilder6":{source:"iana",extensions:["pbd"]},"application/vnd.powerbuilder6-s":{source:"iana"},"application/vnd.powerbuilder7":{source:"iana"},"application/vnd.powerbuilder7-s":{source:"iana"},"application/vnd.powerbuilder75":{source:"iana"},"application/vnd.powerbuilder75-s":{source:"iana"},"application/vnd.preminet":{source:"iana"},"application/vnd.previewsystems.box":{source:"iana",extensions:["box"]},"application/vnd.proteus.magazine":{source:"iana",extensions:["mgz"]},"application/vnd.psfs":{source:"iana"},"application/vnd.publishare-delta-tree":{source:"iana",extensions:["qps"]},"application/vnd.pvi.ptid1":{source:"iana",extensions:["ptid"]},"application/vnd.pwg-multiplexed":{source:"iana"},"application/vnd.pwg-xhtml-print+xml":{source:"iana",compressible:!0},"application/vnd.qualcomm.brew-app-res":{source:"iana"},"application/vnd.quarantainenet":{source:"iana"},"application/vnd.quark.quarkxpress":{source:"iana",extensions:["qxd","qxt","qwd","qwt","qxl","qxb"]},"application/vnd.quobject-quoxdocument":{source:"iana"},"application/vnd.radisys.moml+xml":{source:"iana",compressible:!0},"application/vnd.radisys.msml+xml":{source:"iana",compressible:!0},"application/vnd.radisys.msml-audit+xml":{source:"iana",compressible:!0},"application/vnd.radisys.msml-audit-conf+xml":{source:"iana",compressible:!0},"application/vnd.radisys.msml-audit-conn+xml":{source:"iana",compressible:!0},"application/vnd.radisys.msml-audit-dialog+xml":{source:"iana",compressible:!0},"application/vnd.radisys.msml-audit-stream+xml":{source:"iana",compressible:!0},"application/vnd.radisys.msml-conf+xml":{source:"iana",compressible:!0},"application/vnd.radisys.msml-dialog+xml":{source:"iana",compressible:!0},"application/vnd.radisys.msml-dialog-base+xml":{source:"iana",compressible:!0},"application/vnd.radisys.msml-dialog-fax-detect+xml":{source:"iana",compressible:!0},"application/vnd.radisys.msml-dialog-fax-sendrecv+xml":{source:"iana",compressible:!0},"application/vnd.radisys.msml-dialog-group+xml":{source:"iana",compressible:!0},"application/vnd.radisys.msml-dialog-speech+xml":{source:"iana",compressible:!0},"application/vnd.radisys.msml-dialog-transform+xml":{source:"iana",compressible:!0},"application/vnd.rainstor.data":{source:"iana"},"application/vnd.rapid":{source:"iana"},"application/vnd.rar":{source:"iana",extensions:["rar"]},"application/vnd.realvnc.bed":{source:"iana",extensions:["bed"]},"application/vnd.recordare.musicxml":{source:"iana",extensions:["mxl"]},"application/vnd.recordare.musicxml+xml":{source:"iana",compressible:!0,extensions:["musicxml"]},"application/vnd.renlearn.rlprint":{source:"iana"},"application/vnd.resilient.logic":{source:"iana"},"application/vnd.restful+json":{source:"iana",compressible:!0},"application/vnd.rig.cryptonote":{source:"iana",extensions:["cryptonote"]},"application/vnd.rim.cod":{source:"apache",extensions:["cod"]},"application/vnd.rn-realmedia":{source:"apache",extensions:["rm"]},"application/vnd.rn-realmedia-vbr":{source:"apache",extensions:["rmvb"]},"application/vnd.route66.link66+xml":{source:"iana",compressible:!0,extensions:["link66"]},"application/vnd.rs-274x":{source:"iana"},"application/vnd.ruckus.download":{source:"iana"},"application/vnd.s3sms":{source:"iana"},"application/vnd.sailingtracker.track":{source:"iana",extensions:["st"]},"application/vnd.sar":{source:"iana"},"application/vnd.sbm.cid":{source:"iana"},"application/vnd.sbm.mid2":{source:"iana"},"application/vnd.scribus":{source:"iana"},"application/vnd.sealed.3df":{source:"iana"},"application/vnd.sealed.csf":{source:"iana"},"application/vnd.sealed.doc":{source:"iana"},"application/vnd.sealed.eml":{source:"iana"},"application/vnd.sealed.mht":{source:"iana"},"application/vnd.sealed.net":{source:"iana"},"application/vnd.sealed.ppt":{source:"iana"},"application/vnd.sealed.tiff":{source:"iana"},"application/vnd.sealed.xls":{source:"iana"},"application/vnd.sealedmedia.softseal.html":{source:"iana"},"application/vnd.sealedmedia.softseal.pdf":{source:"iana"},"application/vnd.seemail":{source:"iana",extensions:["see"]},"application/vnd.seis+json":{source:"iana",compressible:!0},"application/vnd.sema":{source:"iana",extensions:["sema"]},"application/vnd.semd":{source:"iana",extensions:["semd"]},"application/vnd.semf":{source:"iana",extensions:["semf"]},"application/vnd.shade-save-file":{source:"iana"},"application/vnd.shana.informed.formdata":{source:"iana",extensions:["ifm"]},"application/vnd.shana.informed.formtemplate":{source:"iana",extensions:["itp"]},"application/vnd.shana.informed.interchange":{source:"iana",extensions:["iif"]},"application/vnd.shana.informed.package":{source:"iana",extensions:["ipk"]},"application/vnd.shootproof+json":{source:"iana",compressible:!0},"application/vnd.shopkick+json":{source:"iana",compressible:!0},"application/vnd.shp":{source:"iana"},"application/vnd.shx":{source:"iana"},"application/vnd.sigrok.session":{source:"iana"},"application/vnd.simtech-mindmapper":{source:"iana",extensions:["twd","twds"]},"application/vnd.siren+json":{source:"iana",compressible:!0},"application/vnd.smaf":{source:"iana",extensions:["mmf"]},"application/vnd.smart.notebook":{source:"iana"},"application/vnd.smart.teacher":{source:"iana",extensions:["teacher"]},"application/vnd.snesdev-page-table":{source:"iana"},"application/vnd.software602.filler.form+xml":{source:"iana",compressible:!0,extensions:["fo"]},"application/vnd.software602.filler.form-xml-zip":{source:"iana"},"application/vnd.solent.sdkm+xml":{source:"iana",compressible:!0,extensions:["sdkm","sdkd"]},"application/vnd.spotfire.dxp":{source:"iana",extensions:["dxp"]},"application/vnd.spotfire.sfs":{source:"iana",extensions:["sfs"]},"application/vnd.sqlite3":{source:"iana"},"application/vnd.sss-cod":{source:"iana"},"application/vnd.sss-dtf":{source:"iana"},"application/vnd.sss-ntf":{source:"iana"},"application/vnd.stardivision.calc":{source:"apache",extensions:["sdc"]},"application/vnd.stardivision.draw":{source:"apache",extensions:["sda"]},"application/vnd.stardivision.impress":{source:"apache",extensions:["sdd"]},"application/vnd.stardivision.math":{source:"apache",extensions:["smf"]},"application/vnd.stardivision.writer":{source:"apache",extensions:["sdw","vor"]},"application/vnd.stardivision.writer-global":{source:"apache",extensions:["sgl"]},"application/vnd.stepmania.package":{source:"iana",extensions:["smzip"]},"application/vnd.stepmania.stepchart":{source:"iana",extensions:["sm"]},"application/vnd.street-stream":{source:"iana"},"application/vnd.sun.wadl+xml":{source:"iana",compressible:!0,extensions:["wadl"]},"application/vnd.sun.xml.calc":{source:"apache",extensions:["sxc"]},"application/vnd.sun.xml.calc.template":{source:"apache",extensions:["stc"]},"application/vnd.sun.xml.draw":{source:"apache",extensions:["sxd"]},"application/vnd.sun.xml.draw.template":{source:"apache",extensions:["std"]},"application/vnd.sun.xml.impress":{source:"apache",extensions:["sxi"]},"application/vnd.sun.xml.impress.template":{source:"apache",extensions:["sti"]},"application/vnd.sun.xml.math":{source:"apache",extensions:["sxm"]},"application/vnd.sun.xml.writer":{source:"apache",extensions:["sxw"]},"application/vnd.sun.xml.writer.global":{source:"apache",extensions:["sxg"]},"application/vnd.sun.xml.writer.template":{source:"apache",extensions:["stw"]},"application/vnd.sus-calendar":{source:"iana",extensions:["sus","susp"]},"application/vnd.svd":{source:"iana",extensions:["svd"]},"application/vnd.swiftview-ics":{source:"iana"},"application/vnd.sycle+xml":{source:"iana",compressible:!0},"application/vnd.syft+json":{source:"iana",compressible:!0},"application/vnd.symbian.install":{source:"apache",extensions:["sis","sisx"]},"application/vnd.syncml+xml":{source:"iana",charset:"UTF-8",compressible:!0,extensions:["xsm"]},"application/vnd.syncml.dm+wbxml":{source:"iana",charset:"UTF-8",extensions:["bdm"]},"application/vnd.syncml.dm+xml":{source:"iana",charset:"UTF-8",compressible:!0,extensions:["xdm"]},"application/vnd.syncml.dm.notification":{source:"iana"},"application/vnd.syncml.dmddf+wbxml":{source:"iana"},"application/vnd.syncml.dmddf+xml":{source:"iana",charset:"UTF-8",compressible:!0,extensions:["ddf"]},"application/vnd.syncml.dmtnds+wbxml":{source:"iana"},"application/vnd.syncml.dmtnds+xml":{source:"iana",charset:"UTF-8",compressible:!0},"application/vnd.syncml.ds.notification":{source:"iana"},"application/vnd.tableschema+json":{source:"iana",compressible:!0},"application/vnd.tao.intent-module-archive":{source:"iana",extensions:["tao"]},"application/vnd.tcpdump.pcap":{source:"iana",extensions:["pcap","cap","dmp"]},"application/vnd.think-cell.ppttc+json":{source:"iana",compressible:!0},"application/vnd.tmd.mediaflex.api+xml":{source:"iana",compressible:!0},"application/vnd.tml":{source:"iana"},"application/vnd.tmobile-livetv":{source:"iana",extensions:["tmo"]},"application/vnd.tri.onesource":{source:"iana"},"application/vnd.trid.tpt":{source:"iana",extensions:["tpt"]},"application/vnd.triscape.mxs":{source:"iana",extensions:["mxs"]},"application/vnd.trueapp":{source:"iana",extensions:["tra"]},"application/vnd.truedoc":{source:"iana"},"application/vnd.ubisoft.webplayer":{source:"iana"},"application/vnd.ufdl":{source:"iana",extensions:["ufd","ufdl"]},"application/vnd.uiq.theme":{source:"iana",extensions:["utz"]},"application/vnd.umajin":{source:"iana",extensions:["umj"]},"application/vnd.unity":{source:"iana",extensions:["unityweb"]},"application/vnd.uoml+xml":{source:"iana",compressible:!0,extensions:["uoml"]},"application/vnd.uplanet.alert":{source:"iana"},"application/vnd.uplanet.alert-wbxml":{source:"iana"},"application/vnd.uplanet.bearer-choice":{source:"iana"},"application/vnd.uplanet.bearer-choice-wbxml":{source:"iana"},"application/vnd.uplanet.cacheop":{source:"iana"},"application/vnd.uplanet.cacheop-wbxml":{source:"iana"},"application/vnd.uplanet.channel":{source:"iana"},"application/vnd.uplanet.channel-wbxml":{source:"iana"},"application/vnd.uplanet.list":{source:"iana"},"application/vnd.uplanet.list-wbxml":{source:"iana"},"application/vnd.uplanet.listcmd":{source:"iana"},"application/vnd.uplanet.listcmd-wbxml":{source:"iana"},"application/vnd.uplanet.signal":{source:"iana"},"application/vnd.uri-map":{source:"iana"},"application/vnd.valve.source.material":{source:"iana"},"application/vnd.vcx":{source:"iana",extensions:["vcx"]},"application/vnd.vd-study":{source:"iana"},"application/vnd.vectorworks":{source:"iana"},"application/vnd.vel+json":{source:"iana",compressible:!0},"application/vnd.verimatrix.vcas":{source:"iana"},"application/vnd.veritone.aion+json":{source:"iana",compressible:!0},"application/vnd.veryant.thin":{source:"iana"},"application/vnd.ves.encrypted":{source:"iana"},"application/vnd.vidsoft.vidconference":{source:"iana"},"application/vnd.visio":{source:"iana",extensions:["vsd","vst","vss","vsw"]},"application/vnd.visionary":{source:"iana",extensions:["vis"]},"application/vnd.vividence.scriptfile":{source:"iana"},"application/vnd.vsf":{source:"iana",extensions:["vsf"]},"application/vnd.wap.sic":{source:"iana"},"application/vnd.wap.slc":{source:"iana"},"application/vnd.wap.wbxml":{source:"iana",charset:"UTF-8",extensions:["wbxml"]},"application/vnd.wap.wmlc":{source:"iana",extensions:["wmlc"]},"application/vnd.wap.wmlscriptc":{source:"iana",extensions:["wmlsc"]},"application/vnd.webturbo":{source:"iana",extensions:["wtb"]},"application/vnd.wfa.dpp":{source:"iana"},"application/vnd.wfa.p2p":{source:"iana"},"application/vnd.wfa.wsc":{source:"iana"},"application/vnd.windows.devicepairing":{source:"iana"},"application/vnd.wmc":{source:"iana"},"application/vnd.wmf.bootstrap":{source:"iana"},"application/vnd.wolfram.mathematica":{source:"iana"},"application/vnd.wolfram.mathematica.package":{source:"iana"},"application/vnd.wolfram.player":{source:"iana",extensions:["nbp"]},"application/vnd.wordperfect":{source:"iana",extensions:["wpd"]},"application/vnd.wqd":{source:"iana",extensions:["wqd"]},"application/vnd.wrq-hp3000-labelled":{source:"iana"},"application/vnd.wt.stf":{source:"iana",extensions:["stf"]},"application/vnd.wv.csp+wbxml":{source:"iana"},"application/vnd.wv.csp+xml":{source:"iana",compressible:!0},"application/vnd.wv.ssp+xml":{source:"iana",compressible:!0},"application/vnd.xacml+json":{source:"iana",compressible:!0},"application/vnd.xara":{source:"iana",extensions:["xar"]},"application/vnd.xfdl":{source:"iana",extensions:["xfdl"]},"application/vnd.xfdl.webform":{source:"iana"},"application/vnd.xmi+xml":{source:"iana",compressible:!0},"application/vnd.xmpie.cpkg":{source:"iana"},"application/vnd.xmpie.dpkg":{source:"iana"},"application/vnd.xmpie.plan":{source:"iana"},"application/vnd.xmpie.ppkg":{source:"iana"},"application/vnd.xmpie.xlim":{source:"iana"},"application/vnd.yamaha.hv-dic":{source:"iana",extensions:["hvd"]},"application/vnd.yamaha.hv-script":{source:"iana",extensions:["hvs"]},"application/vnd.yamaha.hv-voice":{source:"iana",extensions:["hvp"]},"application/vnd.yamaha.openscoreformat":{source:"iana",extensions:["osf"]},"application/vnd.yamaha.openscoreformat.osfpvg+xml":{source:"iana",compressible:!0,extensions:["osfpvg"]},"application/vnd.yamaha.remote-setup":{source:"iana"},"application/vnd.yamaha.smaf-audio":{source:"iana",extensions:["saf"]},"application/vnd.yamaha.smaf-phrase":{source:"iana",extensions:["spf"]},"application/vnd.yamaha.through-ngn":{source:"iana"},"application/vnd.yamaha.tunnel-udpencap":{source:"iana"},"application/vnd.yaoweme":{source:"iana"},"application/vnd.yellowriver-custom-menu":{source:"iana",extensions:["cmp"]},"application/vnd.youtube.yt":{source:"iana"},"application/vnd.zul":{source:"iana",extensions:["zir","zirz"]},"application/vnd.zzazz.deck+xml":{source:"iana",compressible:!0,extensions:["zaz"]},"application/voicexml+xml":{source:"iana",compressible:!0,extensions:["vxml"]},"application/voucher-cms+json":{source:"iana",compressible:!0},"application/vq-rtcpxr":{source:"iana"},"application/wasm":{source:"iana",compressible:!0,extensions:["wasm"]},"application/watcherinfo+xml":{source:"iana",compressible:!0,extensions:["wif"]},"application/webpush-options+json":{source:"iana",compressible:!0},"application/whoispp-query":{source:"iana"},"application/whoispp-response":{source:"iana"},"application/widget":{source:"iana",extensions:["wgt"]},"application/winhlp":{source:"apache",extensions:["hlp"]},"application/wita":{source:"iana"},"application/wordperfect5.1":{source:"iana"},"application/wsdl+xml":{source:"iana",compressible:!0,extensions:["wsdl"]},"application/wspolicy+xml":{source:"iana",compressible:!0,extensions:["wspolicy"]},"application/x-7z-compressed":{source:"apache",compressible:!1,extensions:["7z"]},"application/x-abiword":{source:"apache",extensions:["abw"]},"application/x-ace-compressed":{source:"apache",extensions:["ace"]},"application/x-amf":{source:"apache"},"application/x-apple-diskimage":{source:"apache",extensions:["dmg"]},"application/x-arj":{compressible:!1,extensions:["arj"]},"application/x-authorware-bin":{source:"apache",extensions:["aab","x32","u32","vox"]},"application/x-authorware-map":{source:"apache",extensions:["aam"]},"application/x-authorware-seg":{source:"apache",extensions:["aas"]},"application/x-bcpio":{source:"apache",extensions:["bcpio"]},"application/x-bdoc":{compressible:!1,extensions:["bdoc"]},"application/x-bittorrent":{source:"apache",extensions:["torrent"]},"application/x-blorb":{source:"apache",extensions:["blb","blorb"]},"application/x-bzip":{source:"apache",compressible:!1,extensions:["bz"]},"application/x-bzip2":{source:"apache",compressible:!1,extensions:["bz2","boz"]},"application/x-cbr":{source:"apache",extensions:["cbr","cba","cbt","cbz","cb7"]},"application/x-cdlink":{source:"apache",extensions:["vcd"]},"application/x-cfs-compressed":{source:"apache",extensions:["cfs"]},"application/x-chat":{source:"apache",extensions:["chat"]},"application/x-chess-pgn":{source:"apache",extensions:["pgn"]},"application/x-chrome-extension":{extensions:["crx"]},"application/x-cocoa":{source:"nginx",extensions:["cco"]},"application/x-compress":{source:"apache"},"application/x-conference":{source:"apache",extensions:["nsc"]},"application/x-cpio":{source:"apache",extensions:["cpio"]},"application/x-csh":{source:"apache",extensions:["csh"]},"application/x-deb":{compressible:!1},"application/x-debian-package":{source:"apache",extensions:["deb","udeb"]},"application/x-dgc-compressed":{source:"apache",extensions:["dgc"]},"application/x-director":{source:"apache",extensions:["dir","dcr","dxr","cst","cct","cxt","w3d","fgd","swa"]},"application/x-doom":{source:"apache",extensions:["wad"]},"application/x-dtbncx+xml":{source:"apache",compressible:!0,extensions:["ncx"]},"application/x-dtbook+xml":{source:"apache",compressible:!0,extensions:["dtb"]},"application/x-dtbresource+xml":{source:"apache",compressible:!0,extensions:["res"]},"application/x-dvi":{source:"apache",compressible:!1,extensions:["dvi"]},"application/x-envoy":{source:"apache",extensions:["evy"]},"application/x-eva":{source:"apache",extensions:["eva"]},"application/x-font-bdf":{source:"apache",extensions:["bdf"]},"application/x-font-dos":{source:"apache"},"application/x-font-framemaker":{source:"apache"},"application/x-font-ghostscript":{source:"apache",extensions:["gsf"]},"application/x-font-libgrx":{source:"apache"},"application/x-font-linux-psf":{source:"apache",extensions:["psf"]},"application/x-font-pcf":{source:"apache",extensions:["pcf"]},"application/x-font-snf":{source:"apache",extensions:["snf"]},"application/x-font-speedo":{source:"apache"},"application/x-font-sunos-news":{source:"apache"},"application/x-font-type1":{source:"apache",extensions:["pfa","pfb","pfm","afm"]},"application/x-font-vfont":{source:"apache"},"application/x-freearc":{source:"apache",extensions:["arc"]},"application/x-futuresplash":{source:"apache",extensions:["spl"]},"application/x-gca-compressed":{source:"apache",extensions:["gca"]},"application/x-glulx":{source:"apache",extensions:["ulx"]},"application/x-gnumeric":{source:"apache",extensions:["gnumeric"]},"application/x-gramps-xml":{source:"apache",extensions:["gramps"]},"application/x-gtar":{source:"apache",extensions:["gtar"]},"application/x-gzip":{source:"apache"},"application/x-hdf":{source:"apache",extensions:["hdf"]},"application/x-httpd-php":{compressible:!0,extensions:["php"]},"application/x-install-instructions":{source:"apache",extensions:["install"]},"application/x-iso9660-image":{source:"apache",extensions:["iso"]},"application/x-iwork-keynote-sffkey":{extensions:["key"]},"application/x-iwork-numbers-sffnumbers":{extensions:["numbers"]},"application/x-iwork-pages-sffpages":{extensions:["pages"]},"application/x-java-archive-diff":{source:"nginx",extensions:["jardiff"]},"application/x-java-jnlp-file":{source:"apache",compressible:!1,extensions:["jnlp"]},"application/x-javascript":{compressible:!0},"application/x-keepass2":{extensions:["kdbx"]},"application/x-latex":{source:"apache",compressible:!1,extensions:["latex"]},"application/x-lua-bytecode":{extensions:["luac"]},"application/x-lzh-compressed":{source:"apache",extensions:["lzh","lha"]},"application/x-makeself":{source:"nginx",extensions:["run"]},"application/x-mie":{source:"apache",extensions:["mie"]},"application/x-mobipocket-ebook":{source:"apache",extensions:["prc","mobi"]},"application/x-mpegurl":{compressible:!1},"application/x-ms-application":{source:"apache",extensions:["application"]},"application/x-ms-shortcut":{source:"apache",extensions:["lnk"]},"application/x-ms-wmd":{source:"apache",extensions:["wmd"]},"application/x-ms-wmz":{source:"apache",extensions:["wmz"]},"application/x-ms-xbap":{source:"apache",extensions:["xbap"]},"application/x-msaccess":{source:"apache",extensions:["mdb"]},"application/x-msbinder":{source:"apache",extensions:["obd"]},"application/x-mscardfile":{source:"apache",extensions:["crd"]},"application/x-msclip":{source:"apache",extensions:["clp"]},"application/x-msdos-program":{extensions:["exe"]},"application/x-msdownload":{source:"apache",extensions:["exe","dll","com","bat","msi"]},"application/x-msmediaview":{source:"apache",extensions:["mvb","m13","m14"]},"application/x-msmetafile":{source:"apache",extensions:["wmf","wmz","emf","emz"]},"application/x-msmoney":{source:"apache",extensions:["mny"]},"application/x-mspublisher":{source:"apache",extensions:["pub"]},"application/x-msschedule":{source:"apache",extensions:["scd"]},"application/x-msterminal":{source:"apache",extensions:["trm"]},"application/x-mswrite":{source:"apache",extensions:["wri"]},"application/x-netcdf":{source:"apache",extensions:["nc","cdf"]},"application/x-ns-proxy-autoconfig":{compressible:!0,extensions:["pac"]},"application/x-nzb":{source:"apache",extensions:["nzb"]},"application/x-perl":{source:"nginx",extensions:["pl","pm"]},"application/x-pilot":{source:"nginx",extensions:["prc","pdb"]},"application/x-pkcs12":{source:"apache",compressible:!1,extensions:["p12","pfx"]},"application/x-pkcs7-certificates":{source:"apache",extensions:["p7b","spc"]},"application/x-pkcs7-certreqresp":{source:"apache",extensions:["p7r"]},"application/x-pki-message":{source:"iana"},"application/x-rar-compressed":{source:"apache",compressible:!1,extensions:["rar"]},"application/x-redhat-package-manager":{source:"nginx",extensions:["rpm"]},"application/x-research-info-systems":{source:"apache",extensions:["ris"]},"application/x-sea":{source:"nginx",extensions:["sea"]},"application/x-sh":{source:"apache",compressible:!0,extensions:["sh"]},"application/x-shar":{source:"apache",extensions:["shar"]},"application/x-shockwave-flash":{source:"apache",compressible:!1,extensions:["swf"]},"application/x-silverlight-app":{source:"apache",extensions:["xap"]},"application/x-sql":{source:"apache",extensions:["sql"]},"application/x-stuffit":{source:"apache",compressible:!1,extensions:["sit"]},"application/x-stuffitx":{source:"apache",extensions:["sitx"]},"application/x-subrip":{source:"apache",extensions:["srt"]},"application/x-sv4cpio":{source:"apache",extensions:["sv4cpio"]},"application/x-sv4crc":{source:"apache",extensions:["sv4crc"]},"application/x-t3vm-image":{source:"apache",extensions:["t3"]},"application/x-tads":{source:"apache",extensions:["gam"]},"application/x-tar":{source:"apache",compressible:!0,extensions:["tar"]},"application/x-tcl":{source:"apache",extensions:["tcl","tk"]},"application/x-tex":{source:"apache",extensions:["tex"]},"application/x-tex-tfm":{source:"apache",extensions:["tfm"]},"application/x-texinfo":{source:"apache",extensions:["texinfo","texi"]},"application/x-tgif":{source:"apache",extensions:["obj"]},"application/x-ustar":{source:"apache",extensions:["ustar"]},"application/x-virtualbox-hdd":{compressible:!0,extensions:["hdd"]},"application/x-virtualbox-ova":{compressible:!0,extensions:["ova"]},"application/x-virtualbox-ovf":{compressible:!0,extensions:["ovf"]},"application/x-virtualbox-vbox":{compressible:!0,extensions:["vbox"]},"application/x-virtualbox-vbox-extpack":{compressible:!1,extensions:["vbox-extpack"]},"application/x-virtualbox-vdi":{compressible:!0,extensions:["vdi"]},"application/x-virtualbox-vhd":{compressible:!0,extensions:["vhd"]},"application/x-virtualbox-vmdk":{compressible:!0,extensions:["vmdk"]},"application/x-wais-source":{source:"apache",extensions:["src"]},"application/x-web-app-manifest+json":{compressible:!0,extensions:["webapp"]},"application/x-www-form-urlencoded":{source:"iana",compressible:!0},"application/x-x509-ca-cert":{source:"iana",extensions:["der","crt","pem"]},"application/x-x509-ca-ra-cert":{source:"iana"},"application/x-x509-next-ca-cert":{source:"iana"},"application/x-xfig":{source:"apache",extensions:["fig"]},"application/x-xliff+xml":{source:"apache",compressible:!0,extensions:["xlf"]},"application/x-xpinstall":{source:"apache",compressible:!1,extensions:["xpi"]},"application/x-xz":{source:"apache",extensions:["xz"]},"application/x-zmachine":{source:"apache",extensions:["z1","z2","z3","z4","z5","z6","z7","z8"]},"application/x400-bp":{source:"iana"},"application/xacml+xml":{source:"iana",compressible:!0},"application/xaml+xml":{source:"apache",compressible:!0,extensions:["xaml"]},"application/xcap-att+xml":{source:"iana",compressible:!0,extensions:["xav"]},"application/xcap-caps+xml":{source:"iana",compressible:!0,extensions:["xca"]},"application/xcap-diff+xml":{source:"iana",compressible:!0,extensions:["xdf"]},"application/xcap-el+xml":{source:"iana",compressible:!0,extensions:["xel"]},"application/xcap-error+xml":{source:"iana",compressible:!0},"application/xcap-ns+xml":{source:"iana",compressible:!0,extensions:["xns"]},"application/xcon-conference-info+xml":{source:"iana",compressible:!0},"application/xcon-conference-info-diff+xml":{source:"iana",compressible:!0},"application/xenc+xml":{source:"iana",compressible:!0,extensions:["xenc"]},"application/xhtml+xml":{source:"iana",compressible:!0,extensions:["xhtml","xht"]},"application/xhtml-voice+xml":{source:"apache",compressible:!0},"application/xliff+xml":{source:"iana",compressible:!0,extensions:["xlf"]},"application/xml":{source:"iana",compressible:!0,extensions:["xml","xsl","xsd","rng"]},"application/xml-dtd":{source:"iana",compressible:!0,extensions:["dtd"]},"application/xml-external-parsed-entity":{source:"iana"},"application/xml-patch+xml":{source:"iana",compressible:!0},"application/xmpp+xml":{source:"iana",compressible:!0},"application/xop+xml":{source:"iana",compressible:!0,extensions:["xop"]},"application/xproc+xml":{source:"apache",compressible:!0,extensions:["xpl"]},"application/xslt+xml":{source:"iana",compressible:!0,extensions:["xsl","xslt"]},"application/xspf+xml":{source:"apache",compressible:!0,extensions:["xspf"]},"application/xv+xml":{source:"iana",compressible:!0,extensions:["mxml","xhvml","xvml","xvm"]},"application/yang":{source:"iana",extensions:["yang"]},"application/yang-data+json":{source:"iana",compressible:!0},"application/yang-data+xml":{source:"iana",compressible:!0},"application/yang-patch+json":{source:"iana",compressible:!0},"application/yang-patch+xml":{source:"iana",compressible:!0},"application/yin+xml":{source:"iana",compressible:!0,extensions:["yin"]},"application/zip":{source:"iana",compressible:!1,extensions:["zip"]},"application/zlib":{source:"iana"},"application/zstd":{source:"iana"},"audio/1d-interleaved-parityfec":{source:"iana"},"audio/32kadpcm":{source:"iana"},"audio/3gpp":{source:"iana",compressible:!1,extensions:["3gpp"]},"audio/3gpp2":{source:"iana"},"audio/aac":{source:"iana"},"audio/ac3":{source:"iana"},"audio/adpcm":{source:"apache",extensions:["adp"]},"audio/amr":{source:"iana",extensions:["amr"]},"audio/amr-wb":{source:"iana"},"audio/amr-wb+":{source:"iana"},"audio/aptx":{source:"iana"},"audio/asc":{source:"iana"},"audio/atrac-advanced-lossless":{source:"iana"},"audio/atrac-x":{source:"iana"},"audio/atrac3":{source:"iana"},"audio/basic":{source:"iana",compressible:!1,extensions:["au","snd"]},"audio/bv16":{source:"iana"},"audio/bv32":{source:"iana"},"audio/clearmode":{source:"iana"},"audio/cn":{source:"iana"},"audio/dat12":{source:"iana"},"audio/dls":{source:"iana"},"audio/dsr-es201108":{source:"iana"},"audio/dsr-es202050":{source:"iana"},"audio/dsr-es202211":{source:"iana"},"audio/dsr-es202212":{source:"iana"},"audio/dv":{source:"iana"},"audio/dvi4":{source:"iana"},"audio/eac3":{source:"iana"},"audio/encaprtp":{source:"iana"},"audio/evrc":{source:"iana"},"audio/evrc-qcp":{source:"iana"},"audio/evrc0":{source:"iana"},"audio/evrc1":{source:"iana"},"audio/evrcb":{source:"iana"},"audio/evrcb0":{source:"iana"},"audio/evrcb1":{source:"iana"},"audio/evrcnw":{source:"iana"},"audio/evrcnw0":{source:"iana"},"audio/evrcnw1":{source:"iana"},"audio/evrcwb":{source:"iana"},"audio/evrcwb0":{source:"iana"},"audio/evrcwb1":{source:"iana"},"audio/evs":{source:"iana"},"audio/flexfec":{source:"iana"},"audio/fwdred":{source:"iana"},"audio/g711-0":{source:"iana"},"audio/g719":{source:"iana"},"audio/g722":{source:"iana"},"audio/g7221":{source:"iana"},"audio/g723":{source:"iana"},"audio/g726-16":{source:"iana"},"audio/g726-24":{source:"iana"},"audio/g726-32":{source:"iana"},"audio/g726-40":{source:"iana"},"audio/g728":{source:"iana"},"audio/g729":{source:"iana"},"audio/g7291":{source:"iana"},"audio/g729d":{source:"iana"},"audio/g729e":{source:"iana"},"audio/gsm":{source:"iana"},"audio/gsm-efr":{source:"iana"},"audio/gsm-hr-08":{source:"iana"},"audio/ilbc":{source:"iana"},"audio/ip-mr_v2.5":{source:"iana"},"audio/isac":{source:"apache"},"audio/l16":{source:"iana"},"audio/l20":{source:"iana"},"audio/l24":{source:"iana",compressible:!1},"audio/l8":{source:"iana"},"audio/lpc":{source:"iana"},"audio/melp":{source:"iana"},"audio/melp1200":{source:"iana"},"audio/melp2400":{source:"iana"},"audio/melp600":{source:"iana"},"audio/mhas":{source:"iana"},"audio/midi":{source:"apache",extensions:["mid","midi","kar","rmi"]},"audio/mobile-xmf":{source:"iana",extensions:["mxmf"]},"audio/mp3":{compressible:!1,extensions:["mp3"]},"audio/mp4":{source:"iana",compressible:!1,extensions:["m4a","mp4a"]},"audio/mp4a-latm":{source:"iana"},"audio/mpa":{source:"iana"},"audio/mpa-robust":{source:"iana"},"audio/mpeg":{source:"iana",compressible:!1,extensions:["mpga","mp2","mp2a","mp3","m2a","m3a"]},"audio/mpeg4-generic":{source:"iana"},"audio/musepack":{source:"apache"},"audio/ogg":{source:"iana",compressible:!1,extensions:["oga","ogg","spx","opus"]},"audio/opus":{source:"iana"},"audio/parityfec":{source:"iana"},"audio/pcma":{source:"iana"},"audio/pcma-wb":{source:"iana"},"audio/pcmu":{source:"iana"},"audio/pcmu-wb":{source:"iana"},"audio/prs.sid":{source:"iana"},"audio/qcelp":{source:"iana"},"audio/raptorfec":{source:"iana"},"audio/red":{source:"iana"},"audio/rtp-enc-aescm128":{source:"iana"},"audio/rtp-midi":{source:"iana"},"audio/rtploopback":{source:"iana"},"audio/rtx":{source:"iana"},"audio/s3m":{source:"apache",extensions:["s3m"]},"audio/scip":{source:"iana"},"audio/silk":{source:"apache",extensions:["sil"]},"audio/smv":{source:"iana"},"audio/smv-qcp":{source:"iana"},"audio/smv0":{source:"iana"},"audio/sofa":{source:"iana"},"audio/sp-midi":{source:"iana"},"audio/speex":{source:"iana"},"audio/t140c":{source:"iana"},"audio/t38":{source:"iana"},"audio/telephone-event":{source:"iana"},"audio/tetra_acelp":{source:"iana"},"audio/tetra_acelp_bb":{source:"iana"},"audio/tone":{source:"iana"},"audio/tsvcis":{source:"iana"},"audio/uemclip":{source:"iana"},"audio/ulpfec":{source:"iana"},"audio/usac":{source:"iana"},"audio/vdvi":{source:"iana"},"audio/vmr-wb":{source:"iana"},"audio/vnd.3gpp.iufp":{source:"iana"},"audio/vnd.4sb":{source:"iana"},"audio/vnd.audiokoz":{source:"iana"},"audio/vnd.celp":{source:"iana"},"audio/vnd.cisco.nse":{source:"iana"},"audio/vnd.cmles.radio-events":{source:"iana"},"audio/vnd.cns.anp1":{source:"iana"},"audio/vnd.cns.inf1":{source:"iana"},"audio/vnd.dece.audio":{source:"iana",extensions:["uva","uvva"]},"audio/vnd.digital-winds":{source:"iana",extensions:["eol"]},"audio/vnd.dlna.adts":{source:"iana"},"audio/vnd.dolby.heaac.1":{source:"iana"},"audio/vnd.dolby.heaac.2":{source:"iana"},"audio/vnd.dolby.mlp":{source:"iana"},"audio/vnd.dolby.mps":{source:"iana"},"audio/vnd.dolby.pl2":{source:"iana"},"audio/vnd.dolby.pl2x":{source:"iana"},"audio/vnd.dolby.pl2z":{source:"iana"},"audio/vnd.dolby.pulse.1":{source:"iana"},"audio/vnd.dra":{source:"iana",extensions:["dra"]},"audio/vnd.dts":{source:"iana",extensions:["dts"]},"audio/vnd.dts.hd":{source:"iana",extensions:["dtshd"]},"audio/vnd.dts.uhd":{source:"iana"},"audio/vnd.dvb.file":{source:"iana"},"audio/vnd.everad.plj":{source:"iana"},"audio/vnd.hns.audio":{source:"iana"},"audio/vnd.lucent.voice":{source:"iana",extensions:["lvp"]},"audio/vnd.ms-playready.media.pya":{source:"iana",extensions:["pya"]},"audio/vnd.nokia.mobile-xmf":{source:"iana"},"audio/vnd.nortel.vbk":{source:"iana"},"audio/vnd.nuera.ecelp4800":{source:"iana",extensions:["ecelp4800"]},"audio/vnd.nuera.ecelp7470":{source:"iana",extensions:["ecelp7470"]},"audio/vnd.nuera.ecelp9600":{source:"iana",extensions:["ecelp9600"]},"audio/vnd.octel.sbc":{source:"iana"},"audio/vnd.presonus.multitrack":{source:"iana"},"audio/vnd.qcelp":{source:"iana"},"audio/vnd.rhetorex.32kadpcm":{source:"iana"},"audio/vnd.rip":{source:"iana",extensions:["rip"]},"audio/vnd.rn-realaudio":{compressible:!1},"audio/vnd.sealedmedia.softseal.mpeg":{source:"iana"},"audio/vnd.vmx.cvsd":{source:"iana"},"audio/vnd.wave":{compressible:!1},"audio/vorbis":{source:"iana",compressible:!1},"audio/vorbis-config":{source:"iana"},"audio/wav":{compressible:!1,extensions:["wav"]},"audio/wave":{compressible:!1,extensions:["wav"]},"audio/webm":{source:"apache",compressible:!1,extensions:["weba"]},"audio/x-aac":{source:"apache",compressible:!1,extensions:["aac"]},"audio/x-aiff":{source:"apache",extensions:["aif","aiff","aifc"]},"audio/x-caf":{source:"apache",compressible:!1,extensions:["caf"]},"audio/x-flac":{source:"apache",extensions:["flac"]},"audio/x-m4a":{source:"nginx",extensions:["m4a"]},"audio/x-matroska":{source:"apache",extensions:["mka"]},"audio/x-mpegurl":{source:"apache",extensions:["m3u"]},"audio/x-ms-wax":{source:"apache",extensions:["wax"]},"audio/x-ms-wma":{source:"apache",extensions:["wma"]},"audio/x-pn-realaudio":{source:"apache",extensions:["ram","ra"]},"audio/x-pn-realaudio-plugin":{source:"apache",extensions:["rmp"]},"audio/x-realaudio":{source:"nginx",extensions:["ra"]},"audio/x-tta":{source:"apache"},"audio/x-wav":{source:"apache",extensions:["wav"]},"audio/xm":{source:"apache",extensions:["xm"]},"chemical/x-cdx":{source:"apache",extensions:["cdx"]},"chemical/x-cif":{source:"apache",extensions:["cif"]},"chemical/x-cmdf":{source:"apache",extensions:["cmdf"]},"chemical/x-cml":{source:"apache",extensions:["cml"]},"chemical/x-csml":{source:"apache",extensions:["csml"]},"chemical/x-pdb":{source:"apache"},"chemical/x-xyz":{source:"apache",extensions:["xyz"]},"font/collection":{source:"iana",extensions:["ttc"]},"font/otf":{source:"iana",compressible:!0,extensions:["otf"]},"font/sfnt":{source:"iana"},"font/ttf":{source:"iana",compressible:!0,extensions:["ttf"]},"font/woff":{source:"iana",extensions:["woff"]},"font/woff2":{source:"iana",extensions:["woff2"]},"image/aces":{source:"iana",extensions:["exr"]},"image/apng":{compressible:!1,extensions:["apng"]},"image/avci":{source:"iana",extensions:["avci"]},"image/avcs":{source:"iana",extensions:["avcs"]},"image/avif":{source:"iana",compressible:!1,extensions:["avif"]},"image/bmp":{source:"iana",compressible:!0,extensions:["bmp"]},"image/cgm":{source:"iana",extensions:["cgm"]},"image/dicom-rle":{source:"iana",extensions:["drle"]},"image/emf":{source:"iana",extensions:["emf"]},"image/fits":{source:"iana",extensions:["fits"]},"image/g3fax":{source:"iana",extensions:["g3"]},"image/gif":{source:"iana",compressible:!1,extensions:["gif"]},"image/heic":{source:"iana",extensions:["heic"]},"image/heic-sequence":{source:"iana",extensions:["heics"]},"image/heif":{source:"iana",extensions:["heif"]},"image/heif-sequence":{source:"iana",extensions:["heifs"]},"image/hej2k":{source:"iana",extensions:["hej2"]},"image/hsj2":{source:"iana",extensions:["hsj2"]},"image/ief":{source:"iana",extensions:["ief"]},"image/jls":{source:"iana",extensions:["jls"]},"image/jp2":{source:"iana",compressible:!1,extensions:["jp2","jpg2"]},"image/jpeg":{source:"iana",compressible:!1,extensions:["jpeg","jpg","jpe"]},"image/jph":{source:"iana",extensions:["jph"]},"image/jphc":{source:"iana",extensions:["jhc"]},"image/jpm":{source:"iana",compressible:!1,extensions:["jpm"]},"image/jpx":{source:"iana",compressible:!1,extensions:["jpx","jpf"]},"image/jxr":{source:"iana",extensions:["jxr"]},"image/jxra":{source:"iana",extensions:["jxra"]},"image/jxrs":{source:"iana",extensions:["jxrs"]},"image/jxs":{source:"iana",extensions:["jxs"]},"image/jxsc":{source:"iana",extensions:["jxsc"]},"image/jxsi":{source:"iana",extensions:["jxsi"]},"image/jxss":{source:"iana",extensions:["jxss"]},"image/ktx":{source:"iana",extensions:["ktx"]},"image/ktx2":{source:"iana",extensions:["ktx2"]},"image/naplps":{source:"iana"},"image/pjpeg":{compressible:!1},"image/png":{source:"iana",compressible:!1,extensions:["png"]},"image/prs.btif":{source:"iana",extensions:["btif"]},"image/prs.pti":{source:"iana",extensions:["pti"]},"image/pwg-raster":{source:"iana"},"image/sgi":{source:"apache",extensions:["sgi"]},"image/svg+xml":{source:"iana",compressible:!0,extensions:["svg","svgz"]},"image/t38":{source:"iana",extensions:["t38"]},"image/tiff":{source:"iana",compressible:!1,extensions:["tif","tiff"]},"image/tiff-fx":{source:"iana",extensions:["tfx"]},"image/vnd.adobe.photoshop":{source:"iana",compressible:!0,extensions:["psd"]},"image/vnd.airzip.accelerator.azv":{source:"iana",extensions:["azv"]},"image/vnd.cns.inf2":{source:"iana"},"image/vnd.dece.graphic":{source:"iana",extensions:["uvi","uvvi","uvg","uvvg"]},"image/vnd.djvu":{source:"iana",extensions:["djvu","djv"]},"image/vnd.dvb.subtitle":{source:"iana",extensions:["sub"]},"image/vnd.dwg":{source:"iana",extensions:["dwg"]},"image/vnd.dxf":{source:"iana",extensions:["dxf"]},"image/vnd.fastbidsheet":{source:"iana",extensions:["fbs"]},"image/vnd.fpx":{source:"iana",extensions:["fpx"]},"image/vnd.fst":{source:"iana",extensions:["fst"]},"image/vnd.fujixerox.edmics-mmr":{source:"iana",extensions:["mmr"]},"image/vnd.fujixerox.edmics-rlc":{source:"iana",extensions:["rlc"]},"image/vnd.globalgraphics.pgb":{source:"iana"},"image/vnd.microsoft.icon":{source:"iana",compressible:!0,extensions:["ico"]},"image/vnd.mix":{source:"iana"},"image/vnd.mozilla.apng":{source:"iana"},"image/vnd.ms-dds":{compressible:!0,extensions:["dds"]},"image/vnd.ms-modi":{source:"iana",extensions:["mdi"]},"image/vnd.ms-photo":{source:"apache",extensions:["wdp"]},"image/vnd.net-fpx":{source:"iana",extensions:["npx"]},"image/vnd.pco.b16":{source:"iana",extensions:["b16"]},"image/vnd.radiance":{source:"iana"},"image/vnd.sealed.png":{source:"iana"},"image/vnd.sealedmedia.softseal.gif":{source:"iana"},"image/vnd.sealedmedia.softseal.jpg":{source:"iana"},"image/vnd.svf":{source:"iana"},"image/vnd.tencent.tap":{source:"iana",extensions:["tap"]},"image/vnd.valve.source.texture":{source:"iana",extensions:["vtf"]},"image/vnd.wap.wbmp":{source:"iana",extensions:["wbmp"]},"image/vnd.xiff":{source:"iana",extensions:["xif"]},"image/vnd.zbrush.pcx":{source:"iana",extensions:["pcx"]},"image/webp":{source:"apache",extensions:["webp"]},"image/wmf":{source:"iana",extensions:["wmf"]},"image/x-3ds":{source:"apache",extensions:["3ds"]},"image/x-cmu-raster":{source:"apache",extensions:["ras"]},"image/x-cmx":{source:"apache",extensions:["cmx"]},"image/x-freehand":{source:"apache",extensions:["fh","fhc","fh4","fh5","fh7"]},"image/x-icon":{source:"apache",compressible:!0,extensions:["ico"]},"image/x-jng":{source:"nginx",extensions:["jng"]},"image/x-mrsid-image":{source:"apache",extensions:["sid"]},"image/x-ms-bmp":{source:"nginx",compressible:!0,extensions:["bmp"]},"image/x-pcx":{source:"apache",extensions:["pcx"]},"image/x-pict":{source:"apache",extensions:["pic","pct"]},"image/x-portable-anymap":{source:"apache",extensions:["pnm"]},"image/x-portable-bitmap":{source:"apache",extensions:["pbm"]},"image/x-portable-graymap":{source:"apache",extensions:["pgm"]},"image/x-portable-pixmap":{source:"apache",extensions:["ppm"]},"image/x-rgb":{source:"apache",extensions:["rgb"]},"image/x-tga":{source:"apache",extensions:["tga"]},"image/x-xbitmap":{source:"apache",extensions:["xbm"]},"image/x-xcf":{compressible:!1},"image/x-xpixmap":{source:"apache",extensions:["xpm"]},"image/x-xwindowdump":{source:"apache",extensions:["xwd"]},"message/cpim":{source:"iana"},"message/delivery-status":{source:"iana"},"message/disposition-notification":{source:"iana",extensions:["disposition-notification"]},"message/external-body":{source:"iana"},"message/feedback-report":{source:"iana"},"message/global":{source:"iana",extensions:["u8msg"]},"message/global-delivery-status":{source:"iana",extensions:["u8dsn"]},"message/global-disposition-notification":{source:"iana",extensions:["u8mdn"]},"message/global-headers":{source:"iana",extensions:["u8hdr"]},"message/http":{source:"iana",compressible:!1},"message/imdn+xml":{source:"iana",compressible:!0},"message/news":{source:"iana"},"message/partial":{source:"iana",compressible:!1},"message/rfc822":{source:"iana",compressible:!0,extensions:["eml","mime"]},"message/s-http":{source:"iana"},"message/sip":{source:"iana"},"message/sipfrag":{source:"iana"},"message/tracking-status":{source:"iana"},"message/vnd.si.simp":{source:"iana"},"message/vnd.wfa.wsc":{source:"iana",extensions:["wsc"]},"model/3mf":{source:"iana",extensions:["3mf"]},"model/e57":{source:"iana"},"model/gltf+json":{source:"iana",compressible:!0,extensions:["gltf"]},"model/gltf-binary":{source:"iana",compressible:!0,extensions:["glb"]},"model/iges":{source:"iana",compressible:!1,extensions:["igs","iges"]},"model/mesh":{source:"iana",compressible:!1,extensions:["msh","mesh","silo"]},"model/mtl":{source:"iana",extensions:["mtl"]},"model/obj":{source:"iana",extensions:["obj"]},"model/step":{source:"iana"},"model/step+xml":{source:"iana",compressible:!0,extensions:["stpx"]},"model/step+zip":{source:"iana",compressible:!1,extensions:["stpz"]},"model/step-xml+zip":{source:"iana",compressible:!1,extensions:["stpxz"]},"model/stl":{source:"iana",extensions:["stl"]},"model/vnd.collada+xml":{source:"iana",compressible:!0,extensions:["dae"]},"model/vnd.dwf":{source:"iana",extensions:["dwf"]},"model/vnd.flatland.3dml":{source:"iana"},"model/vnd.gdl":{source:"iana",extensions:["gdl"]},"model/vnd.gs-gdl":{source:"apache"},"model/vnd.gs.gdl":{source:"iana"},"model/vnd.gtw":{source:"iana",extensions:["gtw"]},"model/vnd.moml+xml":{source:"iana",compressible:!0},"model/vnd.mts":{source:"iana",extensions:["mts"]},"model/vnd.opengex":{source:"iana",extensions:["ogex"]},"model/vnd.parasolid.transmit.binary":{source:"iana",extensions:["x_b"]},"model/vnd.parasolid.transmit.text":{source:"iana",extensions:["x_t"]},"model/vnd.pytha.pyox":{source:"iana"},"model/vnd.rosette.annotated-data-model":{source:"iana"},"model/vnd.sap.vds":{source:"iana",extensions:["vds"]},"model/vnd.usdz+zip":{source:"iana",compressible:!1,extensions:["usdz"]},"model/vnd.valve.source.compiled-map":{source:"iana",extensions:["bsp"]},"model/vnd.vtu":{source:"iana",extensions:["vtu"]},"model/vrml":{source:"iana",compressible:!1,extensions:["wrl","vrml"]},"model/x3d+binary":{source:"apache",compressible:!1,extensions:["x3db","x3dbz"]},"model/x3d+fastinfoset":{source:"iana",extensions:["x3db"]},"model/x3d+vrml":{source:"apache",compressible:!1,extensions:["x3dv","x3dvz"]},"model/x3d+xml":{source:"iana",compressible:!0,extensions:["x3d","x3dz"]},"model/x3d-vrml":{source:"iana",extensions:["x3dv"]},"multipart/alternative":{source:"iana",compressible:!1},"multipart/appledouble":{source:"iana"},"multipart/byteranges":{source:"iana"},"multipart/digest":{source:"iana"},"multipart/encrypted":{source:"iana",compressible:!1},"multipart/form-data":{source:"iana",compressible:!1},"multipart/header-set":{source:"iana"},"multipart/mixed":{source:"iana"},"multipart/multilingual":{source:"iana"},"multipart/parallel":{source:"iana"},"multipart/related":{source:"iana",compressible:!1},"multipart/report":{source:"iana"},"multipart/signed":{source:"iana",compressible:!1},"multipart/vnd.bint.med-plus":{source:"iana"},"multipart/voice-message":{source:"iana"},"multipart/x-mixed-replace":{source:"iana"},"text/1d-interleaved-parityfec":{source:"iana"},"text/cache-manifest":{source:"iana",compressible:!0,extensions:["appcache","manifest"]},"text/calendar":{source:"iana",extensions:["ics","ifb"]},"text/calender":{compressible:!0},"text/cmd":{compressible:!0},"text/coffeescript":{extensions:["coffee","litcoffee"]},"text/cql":{source:"iana"},"text/cql-expression":{source:"iana"},"text/cql-identifier":{source:"iana"},"text/css":{source:"iana",charset:"UTF-8",compressible:!0,extensions:["css"]},"text/csv":{source:"iana",compressible:!0,extensions:["csv"]},"text/csv-schema":{source:"iana"},"text/directory":{source:"iana"},"text/dns":{source:"iana"},"text/ecmascript":{source:"iana"},"text/encaprtp":{source:"iana"},"text/enriched":{source:"iana"},"text/fhirpath":{source:"iana"},"text/flexfec":{source:"iana"},"text/fwdred":{source:"iana"},"text/gff3":{source:"iana"},"text/grammar-ref-list":{source:"iana"},"text/html":{source:"iana",compressible:!0,extensions:["html","htm","shtml"]},"text/jade":{extensions:["jade"]},"text/javascript":{source:"iana",compressible:!0},"text/jcr-cnd":{source:"iana"},"text/jsx":{compressible:!0,extensions:["jsx"]},"text/less":{compressible:!0,extensions:["less"]},"text/markdown":{source:"iana",compressible:!0,extensions:["markdown","md"]},"text/mathml":{source:"nginx",extensions:["mml"]},"text/mdx":{compressible:!0,extensions:["mdx"]},"text/mizar":{source:"iana"},"text/n3":{source:"iana",charset:"UTF-8",compressible:!0,extensions:["n3"]},"text/parameters":{source:"iana",charset:"UTF-8"},"text/parityfec":{source:"iana"},"text/plain":{source:"iana",compressible:!0,extensions:["txt","text","conf","def","list","log","in","ini"]},"text/provenance-notation":{source:"iana",charset:"UTF-8"},"text/prs.fallenstein.rst":{source:"iana"},"text/prs.lines.tag":{source:"iana",extensions:["dsc"]},"text/prs.prop.logic":{source:"iana"},"text/raptorfec":{source:"iana"},"text/red":{source:"iana"},"text/rfc822-headers":{source:"iana"},"text/richtext":{source:"iana",compressible:!0,extensions:["rtx"]},"text/rtf":{source:"iana",compressible:!0,extensions:["rtf"]},"text/rtp-enc-aescm128":{source:"iana"},"text/rtploopback":{source:"iana"},"text/rtx":{source:"iana"},"text/sgml":{source:"iana",extensions:["sgml","sgm"]},"text/shaclc":{source:"iana"},"text/shex":{source:"iana",extensions:["shex"]},"text/slim":{extensions:["slim","slm"]},"text/spdx":{source:"iana",extensions:["spdx"]},"text/strings":{source:"iana"},"text/stylus":{extensions:["stylus","styl"]},"text/t140":{source:"iana"},"text/tab-separated-values":{source:"iana",compressible:!0,extensions:["tsv"]},"text/troff":{source:"iana",extensions:["t","tr","roff","man","me","ms"]},"text/turtle":{source:"iana",charset:"UTF-8",extensions:["ttl"]},"text/ulpfec":{source:"iana"},"text/uri-list":{source:"iana",compressible:!0,extensions:["uri","uris","urls"]},"text/vcard":{source:"iana",compressible:!0,extensions:["vcard"]},"text/vnd.a":{source:"iana"},"text/vnd.abc":{source:"iana"},"text/vnd.ascii-art":{source:"iana"},"text/vnd.curl":{source:"iana",extensions:["curl"]},"text/vnd.curl.dcurl":{source:"apache",extensions:["dcurl"]},"text/vnd.curl.mcurl":{source:"apache",extensions:["mcurl"]},"text/vnd.curl.scurl":{source:"apache",extensions:["scurl"]},"text/vnd.debian.copyright":{source:"iana",charset:"UTF-8"},"text/vnd.dmclientscript":{source:"iana"},"text/vnd.dvb.subtitle":{source:"iana",extensions:["sub"]},"text/vnd.esmertec.theme-descriptor":{source:"iana",charset:"UTF-8"},"text/vnd.familysearch.gedcom":{source:"iana",extensions:["ged"]},"text/vnd.ficlab.flt":{source:"iana"},"text/vnd.fly":{source:"iana",extensions:["fly"]},"text/vnd.fmi.flexstor":{source:"iana",extensions:["flx"]},"text/vnd.gml":{source:"iana"},"text/vnd.graphviz":{source:"iana",extensions:["gv"]},"text/vnd.hans":{source:"iana"},"text/vnd.hgl":{source:"iana"},"text/vnd.in3d.3dml":{source:"iana",extensions:["3dml"]},"text/vnd.in3d.spot":{source:"iana",extensions:["spot"]},"text/vnd.iptc.newsml":{source:"iana"},"text/vnd.iptc.nitf":{source:"iana"},"text/vnd.latex-z":{source:"iana"},"text/vnd.motorola.reflex":{source:"iana"},"text/vnd.ms-mediapackage":{source:"iana"},"text/vnd.net2phone.commcenter.command":{source:"iana"},"text/vnd.radisys.msml-basic-layout":{source:"iana"},"text/vnd.senx.warpscript":{source:"iana"},"text/vnd.si.uricatalogue":{source:"iana"},"text/vnd.sosi":{source:"iana"},"text/vnd.sun.j2me.app-descriptor":{source:"iana",charset:"UTF-8",extensions:["jad"]},"text/vnd.trolltech.linguist":{source:"iana",charset:"UTF-8"},"text/vnd.wap.si":{source:"iana"},"text/vnd.wap.sl":{source:"iana"},"text/vnd.wap.wml":{source:"iana",extensions:["wml"]},"text/vnd.wap.wmlscript":{source:"iana",extensions:["wmls"]},"text/vtt":{source:"iana",charset:"UTF-8",compressible:!0,extensions:["vtt"]},"text/x-asm":{source:"apache",extensions:["s","asm"]},"text/x-c":{source:"apache",extensions:["c","cc","cxx","cpp","h","hh","dic"]},"text/x-component":{source:"nginx",extensions:["htc"]},"text/x-fortran":{source:"apache",extensions:["f","for","f77","f90"]},"text/x-gwt-rpc":{compressible:!0},"text/x-handlebars-template":{extensions:["hbs"]},"text/x-java-source":{source:"apache",extensions:["java"]},"text/x-jquery-tmpl":{compressible:!0},"text/x-lua":{extensions:["lua"]},"text/x-markdown":{compressible:!0,extensions:["mkd"]},"text/x-nfo":{source:"apache",extensions:["nfo"]},"text/x-opml":{source:"apache",extensions:["opml"]},"text/x-org":{compressible:!0,extensions:["org"]},"text/x-pascal":{source:"apache",extensions:["p","pas"]},"text/x-processing":{compressible:!0,extensions:["pde"]},"text/x-sass":{extensions:["sass"]},"text/x-scss":{extensions:["scss"]},"text/x-setext":{source:"apache",extensions:["etx"]},"text/x-sfv":{source:"apache",extensions:["sfv"]},"text/x-suse-ymp":{compressible:!0,extensions:["ymp"]},"text/x-uuencode":{source:"apache",extensions:["uu"]},"text/x-vcalendar":{source:"apache",extensions:["vcs"]},"text/x-vcard":{source:"apache",extensions:["vcf"]},"text/xml":{source:"iana",compressible:!0,extensions:["xml"]},"text/xml-external-parsed-entity":{source:"iana"},"text/yaml":{compressible:!0,extensions:["yaml","yml"]},"video/1d-interleaved-parityfec":{source:"iana"},"video/3gpp":{source:"iana",extensions:["3gp","3gpp"]},"video/3gpp-tt":{source:"iana"},"video/3gpp2":{source:"iana",extensions:["3g2"]},"video/av1":{source:"iana"},"video/bmpeg":{source:"iana"},"video/bt656":{source:"iana"},"video/celb":{source:"iana"},"video/dv":{source:"iana"},"video/encaprtp":{source:"iana"},"video/ffv1":{source:"iana"},"video/flexfec":{source:"iana"},"video/h261":{source:"iana",extensions:["h261"]},"video/h263":{source:"iana",extensions:["h263"]},"video/h263-1998":{source:"iana"},"video/h263-2000":{source:"iana"},"video/h264":{source:"iana",extensions:["h264"]},"video/h264-rcdo":{source:"iana"},"video/h264-svc":{source:"iana"},"video/h265":{source:"iana"},"video/iso.segment":{source:"iana",extensions:["m4s"]},"video/jpeg":{source:"iana",extensions:["jpgv"]},"video/jpeg2000":{source:"iana"},"video/jpm":{source:"apache",extensions:["jpm","jpgm"]},"video/jxsv":{source:"iana"},"video/mj2":{source:"iana",extensions:["mj2","mjp2"]},"video/mp1s":{source:"iana"},"video/mp2p":{source:"iana"},"video/mp2t":{source:"iana",extensions:["ts"]},"video/mp4":{source:"iana",compressible:!1,extensions:["mp4","mp4v","mpg4"]},"video/mp4v-es":{source:"iana"},"video/mpeg":{source:"iana",compressible:!1,extensions:["mpeg","mpg","mpe","m1v","m2v"]},"video/mpeg4-generic":{source:"iana"},"video/mpv":{source:"iana"},"video/nv":{source:"iana"},"video/ogg":{source:"iana",compressible:!1,extensions:["ogv"]},"video/parityfec":{source:"iana"},"video/pointer":{source:"iana"},"video/quicktime":{source:"iana",compressible:!1,extensions:["qt","mov"]},"video/raptorfec":{source:"iana"},"video/raw":{source:"iana"},"video/rtp-enc-aescm128":{source:"iana"},"video/rtploopback":{source:"iana"},"video/rtx":{source:"iana"},"video/scip":{source:"iana"},"video/smpte291":{source:"iana"},"video/smpte292m":{source:"iana"},"video/ulpfec":{source:"iana"},"video/vc1":{source:"iana"},"video/vc2":{source:"iana"},"video/vnd.cctv":{source:"iana"},"video/vnd.dece.hd":{source:"iana",extensions:["uvh","uvvh"]},"video/vnd.dece.mobile":{source:"iana",extensions:["uvm","uvvm"]},"video/vnd.dece.mp4":{source:"iana"},"video/vnd.dece.pd":{source:"iana",extensions:["uvp","uvvp"]},"video/vnd.dece.sd":{source:"iana",extensions:["uvs","uvvs"]},"video/vnd.dece.video":{source:"iana",extensions:["uvv","uvvv"]},"video/vnd.directv.mpeg":{source:"iana"},"video/vnd.directv.mpeg-tts":{source:"iana"},"video/vnd.dlna.mpeg-tts":{source:"iana"},"video/vnd.dvb.file":{source:"iana",extensions:["dvb"]},"video/vnd.fvt":{source:"iana",extensions:["fvt"]},"video/vnd.hns.video":{source:"iana"},"video/vnd.iptvforum.1dparityfec-1010":{source:"iana"},"video/vnd.iptvforum.1dparityfec-2005":{source:"iana"},"video/vnd.iptvforum.2dparityfec-1010":{source:"iana"},"video/vnd.iptvforum.2dparityfec-2005":{source:"iana"},"video/vnd.iptvforum.ttsavc":{source:"iana"},"video/vnd.iptvforum.ttsmpeg2":{source:"iana"},"video/vnd.motorola.video":{source:"iana"},"video/vnd.motorola.videop":{source:"iana"},"video/vnd.mpegurl":{source:"iana",extensions:["mxu","m4u"]},"video/vnd.ms-playready.media.pyv":{source:"iana",extensions:["pyv"]},"video/vnd.nokia.interleaved-multimedia":{source:"iana"},"video/vnd.nokia.mp4vr":{source:"iana"},"video/vnd.nokia.videovoip":{source:"iana"},"video/vnd.objectvideo":{source:"iana"},"video/vnd.radgamettools.bink":{source:"iana"},"video/vnd.radgamettools.smacker":{source:"iana"},"video/vnd.sealed.mpeg1":{source:"iana"},"video/vnd.sealed.mpeg4":{source:"iana"},"video/vnd.sealed.swf":{source:"iana"},"video/vnd.sealedmedia.softseal.mov":{source:"iana"},"video/vnd.uvvu.mp4":{source:"iana",extensions:["uvu","uvvu"]},"video/vnd.vivo":{source:"iana",extensions:["viv"]},"video/vnd.youtube.yt":{source:"iana"},"video/vp8":{source:"iana"},"video/vp9":{source:"iana"},"video/webm":{source:"apache",compressible:!1,extensions:["webm"]},"video/x-f4v":{source:"apache",extensions:["f4v"]},"video/x-fli":{source:"apache",extensions:["fli"]},"video/x-flv":{source:"apache",compressible:!1,extensions:["flv"]},"video/x-m4v":{source:"apache",extensions:["m4v"]},"video/x-matroska":{source:"apache",compressible:!1,extensions:["mkv","mk3d","mks"]},"video/x-mng":{source:"apache",extensions:["mng"]},"video/x-ms-asf":{source:"apache",extensions:["asf","asx"]},"video/x-ms-vob":{source:"apache",extensions:["vob"]},"video/x-ms-wm":{source:"apache",extensions:["wm"]},"video/x-ms-wmv":{source:"apache",compressible:!1,extensions:["wmv"]},"video/x-ms-wmx":{source:"apache",extensions:["wmx"]},"video/x-ms-wvx":{source:"apache",extensions:["wvx"]},"video/x-msvideo":{source:"apache",extensions:["avi"]},"video/x-sgi-movie":{source:"apache",extensions:["movie"]},"video/x-smv":{source:"apache",extensions:["smv"]},"x-conference/x-cooltalk":{source:"apache",extensions:["ice"]},"x-shader/x-fragment":{compressible:!0},"x-shader/x-vertex":{compressible:!0}}});var hi=b((KU,Ri)=>{Ri.exports=yi()});var bi=b(ce=>{"use strict";var cs=hi(),jP=require("path").extname,qi=/^\s*([^;\s]*)(?:;|\s|$)/,$P=/^text\//i;ce.charset=fi;ce.charsets={lookup:fi};ce.contentType=zP;ce.extension=JP;ce.extensions=Object.create(null);ce.lookup=YP;ce.types=Object.create(null);XP(ce.extensions,ce.types);function fi(e){if(!e||typeof e!="string")return!1;var t=qi.exec(e),s=t&&cs[t[1].toLowerCase()];return s&&s.charset?s.charset:t&&$P.test(t[1])?"UTF-8":!1}function zP(e){if(!e||typeof e!="string")return!1;var t=e.indexOf("/")===-1?ce.lookup(e):e;if(!t)return!1;if(t.indexOf("charset")===-1){var s=ce.charset(t);s&&(t+="; charset="+s.toLowerCase())}return t}function JP(e){if(!e||typeof e!="string")return!1;var t=qi.exec(e),s=t&&ce.extensions[t[1].toLowerCase()];return!s||!s.length?!1:s[0]}function YP(e){if(!e||typeof e!="string")return!1;var t=jP("x."+e).toLowerCase().substr(1);return t&&ce.types[t]||!1}function XP(e,t){var s=["nginx","apache",void 0,"iana"];Object.keys(cs).forEach(function(n){var o=cs[n],i=o.extensions;if(!(!i||!i.length)){e[n]=i;for(var p=0;p<i.length;p++){var c=i[p];if(t[c]){var a=s.indexOf(cs[t[c]].source),u=s.indexOf(o.source);if(t[c]!=="application/octet-stream"&&(a>u||a===u&&t[c].substr(0,12)==="application/"))continue}t[c]=n}}})}});var Ii=b(($U,xi)=>{xi.exports=ZP;function ZP(e){var t=typeof setImmediate=="function"?setImmediate:typeof process=="object"&&typeof process.nextTick=="function"?process.nextTick:null;t?t(e):setTimeout(e,0)}});var xr=b((zU,ki)=>{var vi=Ii();ki.exports=eA;function eA(e){var t=!1;return vi(function(){t=!0}),function(r,n){t?e(r,n):vi(function(){e(r,n)})}}});var Ir=b((JU,Pi)=>{Pi.exports=tA;function tA(e){Object.keys(e.jobs).forEach(sA.bind(e)),e.jobs={}}function sA(e){typeof this.jobs[e]=="function"&&this.jobs[e]()}});var vr=b((YU,Ti)=>{var Ai=xr(),rA=Ir();Ti.exports=aA;function aA(e,t,s,r){var n=s.keyedList?s.keyedList[s.index]:s.index;s.jobs[n]=nA(t,n,e[n],function(o,i){n in s.jobs&&(delete s.jobs[n],o?rA(s):s.results[n]=i,r(o,s.results))})}function nA(e,t,s,r){var n;return e.length==2?n=e(s,Ai(r)):n=e(s,t,Ai(r)),n}});var kr=b((XU,wi)=>{wi.exports=oA;function oA(e,t){var s=!Array.isArray(e),r={index:0,keyedList:s||t?Object.keys(e):null,jobs:{},results:s?{}:[],size:s?Object.keys(e).length:e.length};return t&&r.keyedList.sort(s?t:function(n,o){return t(e[n],e[o])}),r}});var Pr=b((ZU,Bi)=>{var iA=Ir(),pA=xr();Bi.exports=cA;function cA(e){Object.keys(this.jobs).length&&(this.index=this.size,iA(this),pA(e)(null,this.results))}});var Ui=b((eE,Ci)=>{var uA=vr(),dA=kr(),lA=Pr();Ci.exports=gA;function gA(e,t,s){for(var r=dA(e);r.index<(r.keyedList||e).length;)uA(e,t,r,function(n,o){if(n){s(n,o);return}if(Object.keys(r.jobs).length===0){s(null,r.results);return}}),r.index++;return lA.bind(r,s)}});var Ar=b((tE,us)=>{var Ei=vr(),mA=kr(),yA=Pr();us.exports=RA;us.exports.ascending=Li;us.exports.descending=hA;function RA(e,t,s,r){var n=mA(e,s);return Ei(e,t,n,function o(i,p){if(i){r(i,p);return}if(n.index++,n.index<(n.keyedList||e).length){Ei(e,t,n,o);return}r(null,n.results)}),yA.bind(n,r)}function Li(e,t){return e<t?-1:e>t?1:0}function hA(e,t){return-1*Li(e,t)}});var Si=b((sE,Gi)=>{var qA=Ar();Gi.exports=fA;function fA(e,t,s){return qA(e,t,null,s)}});var _i=b((rE,Wi)=>{Wi.exports={parallel:Ui(),serial:Si(),serialOrdered:Ar()}});var Tr=b((aE,Di)=>{"use strict";Di.exports=Object});var Qi=b((nE,Hi)=>{"use strict";Hi.exports=Error});var Oi=b((oE,Fi)=>{"use strict";Fi.exports=EvalError});var Ni=b((iE,Mi)=>{"use strict";Mi.exports=RangeError});var Ki=b((pE,Vi)=>{"use strict";Vi.exports=ReferenceError});var wr=b((cE,ji)=>{"use strict";ji.exports=SyntaxError});var ze=b((uE,$i)=>{"use strict";$i.exports=TypeError});var Ji=b((dE,zi)=>{"use strict";zi.exports=URIError});var Xi=b((lE,Yi)=>{"use strict";Yi.exports=Math.abs});var ep=b((gE,Zi)=>{"use strict";Zi.exports=Math.floor});var sp=b((mE,tp)=>{"use strict";tp.exports=Math.max});var ap=b((yE,rp)=>{"use strict";rp.exports=Math.min});var op=b((RE,np)=>{"use strict";np.exports=Math.pow});var pp=b((hE,ip)=>{"use strict";ip.exports=Math.round});var up=b((qE,cp)=>{"use strict";cp.exports=Number.isNaN||function(t){return t!==t}});var lp=b((fE,dp)=>{"use strict";var bA=up();dp.exports=function(t){return bA(t)||t===0?t:t<0?-1:1}});var mp=b((bE,gp)=>{"use strict";gp.exports=Object.getOwnPropertyDescriptor});var Wt=b((xE,yp)=>{"use strict";var ds=mp();if(ds)try{ds([],"length")}catch{ds=null}yp.exports=ds});var _t=b((IE,Rp)=>{"use strict";var ls=Object.defineProperty||!1;if(ls)try{ls({},"a",{value:1})}catch{ls=!1}Rp.exports=ls});var Br=b((vE,hp)=>{"use strict";hp.exports=function(){if(typeof Symbol!="function"||typeof Object.getOwnPropertySymbols!="function")return!1;if(typeof Symbol.iterator=="symbol")return!0;var t={},s=Symbol("test"),r=Object(s);if(typeof s=="string"||Object.prototype.toString.call(s)!=="[object Symbol]"||Object.prototype.toString.call(r)!=="[object Symbol]")return!1;var n=42;t[s]=n;for(var o in t)return!1;if(typeof Object.keys=="function"&&Object.keys(t).length!==0||typeof Object.getOwnPropertyNames=="function"&&Object.getOwnPropertyNames(t).length!==0)return!1;var i=Object.getOwnPropertySymbols(t);if(i.length!==1||i[0]!==s||!Object.prototype.propertyIsEnumerable.call(t,s))return!1;if(typeof Object.getOwnPropertyDescriptor=="function"){var p=Object.getOwnPropertyDescriptor(t,s);if(p.value!==n||p.enumerable!==!0)return!1}return!0}});var bp=b((kE,fp)=>{"use strict";var qp=typeof Symbol<"u"&&Symbol,xA=Br();fp.exports=function(){return typeof qp!="function"||typeof Symbol!="function"||typeof qp("foo")!="symbol"||typeof Symbol("bar")!="symbol"?!1:xA()}});var Cr=b((PE,xp)=>{"use strict";xp.exports=typeof Reflect<"u"&&Reflect.getPrototypeOf||null});var Ur=b((AE,Ip)=>{"use strict";var IA=Tr();Ip.exports=IA.getPrototypeOf||null});var Pp=b((TE,kp)=>{"use strict";var vA="Function.prototype.bind called on incompatible ",kA=Object.prototype.toString,PA=Math.max,AA="[object Function]",vp=function(t,s){for(var r=[],n=0;n<t.length;n+=1)r[n]=t[n];for(var o=0;o<s.length;o+=1)r[o+t.length]=s[o];return r},TA=function(t,s){for(var r=[],n=s||0,o=0;n<t.length;n+=1,o+=1)r[o]=t[n];return r},wA=function(e,t){for(var s="",r=0;r<e.length;r+=1)s+=e[r],r+1<e.length&&(s+=t);return s};kp.exports=function(t){var s=this;if(typeof s!="function"||kA.apply(s)!==AA)throw new TypeError(vA+s);for(var r=TA(arguments,1),n,o=function(){if(this instanceof n){var u=s.apply(this,vp(r,arguments));return Object(u)===u?u:this}return s.apply(t,vp(r,arguments))},i=PA(0,s.length-r.length),p=[],c=0;c<i;c++)p[c]="$"+c;if(n=Function("binder","return function ("+wA(p,",")+"){ return binder.apply(this,arguments); }")(o),s.prototype){var a=function(){};a.prototype=s.prototype,n.prototype=new a,a.prototype=null}return n}});var mt=b((wE,Ap)=>{"use strict";var BA=Pp();Ap.exports=Function.prototype.bind||BA});var gs=b((BE,Tp)=>{"use strict";Tp.exports=Function.prototype.call});var Er=b((CE,wp)=>{"use strict";wp.exports=Function.prototype.apply});var Cp=b((UE,Bp)=>{"use strict";Bp.exports=typeof Reflect<"u"&&Reflect&&Reflect.apply});var Ep=b((EE,Up)=>{"use strict";var CA=mt(),UA=Er(),EA=gs(),LA=Cp();Up.exports=LA||CA.call(EA,UA)});var Gp=b((LE,Lp)=>{"use strict";var GA=mt(),SA=ze(),WA=gs(),_A=Ep();Lp.exports=function(t){if(t.length<1||typeof t[0]!="function")throw new SA("a function is required");return _A(GA,WA,t)}});var Qp=b((GE,Hp)=>{"use strict";var DA=Gp(),Sp=Wt(),_p;try{_p=[].__proto__===Array.prototype}catch(e){if(!e||typeof e!="object"||!("code"in e)||e.code!=="ERR_PROTO_ACCESS")throw e}var Lr=!!_p&&Sp&&Sp(Object.prototype,"__proto__"),Dp=Object,Wp=Dp.getPrototypeOf;Hp.exports=Lr&&typeof Lr.get=="function"?DA([Lr.get]):typeof Wp=="function"?function(t){return Wp(t==null?t:Dp(t))}:!1});var Vp=b((SE,Np)=>{"use strict";var Fp=Cr(),Op=Ur(),Mp=Qp();Np.exports=Fp?function(t){return Fp(t)}:Op?function(t){if(!t||typeof t!="object"&&typeof t!="function")throw new TypeError("getProto: not an object");return Op(t)}:Mp?function(t){return Mp(t)}:null});var Gr=b((WE,Kp)=>{"use strict";var HA=Function.prototype.call,QA=Object.prototype.hasOwnProperty,FA=mt();Kp.exports=FA.call(HA,QA)});var ft=b((_E,Xp)=>{"use strict";var C,OA=Tr(),MA=Qi(),NA=Oi(),VA=Ni(),KA=Ki(),qt=wr(),ht=ze(),jA=Ji(),$A=Xi(),zA=ep(),JA=sp(),YA=ap(),XA=op(),ZA=pp(),eT=lp(),Jp=Function,Sr=function(e){try{return Jp('"use strict"; return ('+e+").constructor;")()}catch{}},Dt=Wt(),tT=_t(),Wr=function(){throw new ht},sT=Dt?function(){try{return arguments.callee,Wr}catch{try{return Dt(arguments,"callee").get}catch{return Wr}}}():Wr,yt=bp()(),z=Vp(),rT=Ur(),aT=Cr(),Yp=Er(),Ht=gs(),Rt={},nT=typeof Uint8Array>"u"||!z?C:z(Uint8Array),Je={__proto__:null,"%AggregateError%":typeof AggregateError>"u"?C:AggregateError,"%Array%":Array,"%ArrayBuffer%":typeof ArrayBuffer>"u"?C:ArrayBuffer,"%ArrayIteratorPrototype%":yt&&z?z([][Symbol.iterator]()):C,"%AsyncFromSyncIteratorPrototype%":C,"%AsyncFunction%":Rt,"%AsyncGenerator%":Rt,"%AsyncGeneratorFunction%":Rt,"%AsyncIteratorPrototype%":Rt,"%Atomics%":typeof Atomics>"u"?C:Atomics,"%BigInt%":typeof BigInt>"u"?C:BigInt,"%BigInt64Array%":typeof BigInt64Array>"u"?C:BigInt64Array,"%BigUint64Array%":typeof BigUint64Array>"u"?C:BigUint64Array,"%Boolean%":Boolean,"%DataView%":typeof DataView>"u"?C:DataView,"%Date%":Date,"%decodeURI%":decodeURI,"%decodeURIComponent%":decodeURIComponent,"%encodeURI%":encodeURI,"%encodeURIComponent%":encodeURIComponent,"%Error%":MA,"%eval%":eval,"%EvalError%":NA,"%Float32Array%":typeof Float32Array>"u"?C:Float32Array,"%Float64Array%":typeof Float64Array>"u"?C:Float64Array,"%FinalizationRegistry%":typeof FinalizationRegistry>"u"?C:FinalizationRegistry,"%Function%":Jp,"%GeneratorFunction%":Rt,"%Int8Array%":typeof Int8Array>"u"?C:Int8Array,"%Int16Array%":typeof Int16Array>"u"?C:Int16Array,"%Int32Array%":typeof Int32Array>"u"?C:Int32Array,"%isFinite%":isFinite,"%isNaN%":isNaN,"%IteratorPrototype%":yt&&z?z(z([][Symbol.iterator]())):C,"%JSON%":typeof JSON=="object"?JSON:C,"%Map%":typeof Map>"u"?C:Map,"%MapIteratorPrototype%":typeof Map>"u"||!yt||!z?C:z(new Map()[Symbol.iterator]()),"%Math%":Math,"%Number%":Number,"%Object%":OA,"%Object.getOwnPropertyDescriptor%":Dt,"%parseFloat%":parseFloat,"%parseInt%":parseInt,"%Promise%":typeof Promise>"u"?C:Promise,"%Proxy%":typeof Proxy>"u"?C:Proxy,"%RangeError%":VA,"%ReferenceError%":KA,"%Reflect%":typeof Reflect>"u"?C:Reflect,"%RegExp%":RegExp,"%Set%":typeof Set>"u"?C:Set,"%SetIteratorPrototype%":typeof Set>"u"||!yt||!z?C:z(new Set()[Symbol.iterator]()),"%SharedArrayBuffer%":typeof SharedArrayBuffer>"u"?C:SharedArrayBuffer,"%String%":String,"%StringIteratorPrototype%":yt&&z?z(""[Symbol.iterator]()):C,"%Symbol%":yt?Symbol:C,"%SyntaxError%":qt,"%ThrowTypeError%":sT,"%TypedArray%":nT,"%TypeError%":ht,"%Uint8Array%":typeof Uint8Array>"u"?C:Uint8Array,"%Uint8ClampedArray%":typeof Uint8ClampedArray>"u"?C:Uint8ClampedArray,"%Uint16Array%":typeof Uint16Array>"u"?C:Uint16Array,"%Uint32Array%":typeof Uint32Array>"u"?C:Uint32Array,"%URIError%":jA,"%WeakMap%":typeof WeakMap>"u"?C:WeakMap,"%WeakRef%":typeof WeakRef>"u"?C:WeakRef,"%WeakSet%":typeof WeakSet>"u"?C:WeakSet,"%Function.prototype.call%":Ht,"%Function.prototype.apply%":Yp,"%Object.defineProperty%":tT,"%Object.getPrototypeOf%":rT,"%Math.abs%":$A,"%Math.floor%":zA,"%Math.max%":JA,"%Math.min%":YA,"%Math.pow%":XA,"%Math.round%":ZA,"%Math.sign%":eT,"%Reflect.getPrototypeOf%":aT};if(z)try{null.error}catch(e){jp=z(z(e)),Je["%Error.prototype%"]=jp}var jp,oT=function e(t){var s;if(t==="%AsyncFunction%")s=Sr("async function () {}");else if(t==="%GeneratorFunction%")s=Sr("function* () {}");else if(t==="%AsyncGeneratorFunction%")s=Sr("async function* () {}");else if(t==="%AsyncGenerator%"){var r=e("%AsyncGeneratorFunction%");r&&(s=r.prototype)}else if(t==="%AsyncIteratorPrototype%"){var n=e("%AsyncGenerator%");n&&z&&(s=z(n.prototype))}return Je[t]=s,s},$p={__proto__:null,"%ArrayBufferPrototype%":["ArrayBuffer","prototype"],"%ArrayPrototype%":["Array","prototype"],"%ArrayProto_entries%":["Array","prototype","entries"],"%ArrayProto_forEach%":["Array","prototype","forEach"],"%ArrayProto_keys%":["Array","prototype","keys"],"%ArrayProto_values%":["Array","prototype","values"],"%AsyncFunctionPrototype%":["AsyncFunction","prototype"],"%AsyncGenerator%":["AsyncGeneratorFunction","prototype"],"%AsyncGeneratorPrototype%":["AsyncGeneratorFunction","prototype","prototype"],"%BooleanPrototype%":["Boolean","prototype"],"%DataViewPrototype%":["DataView","prototype"],"%DatePrototype%":["Date","prototype"],"%ErrorPrototype%":["Error","prototype"],"%EvalErrorPrototype%":["EvalError","prototype"],"%Float32ArrayPrototype%":["Float32Array","prototype"],"%Float64ArrayPrototype%":["Float64Array","prototype"],"%FunctionPrototype%":["Function","prototype"],"%Generator%":["GeneratorFunction","prototype"],"%GeneratorPrototype%":["GeneratorFunction","prototype","prototype"],"%Int8ArrayPrototype%":["Int8Array","prototype"],"%Int16ArrayPrototype%":["Int16Array","prototype"],"%Int32ArrayPrototype%":["Int32Array","prototype"],"%JSONParse%":["JSON","parse"],"%JSONStringify%":["JSON","stringify"],"%MapPrototype%":["Map","prototype"],"%NumberPrototype%":["Number","prototype"],"%ObjectPrototype%":["Object","prototype"],"%ObjProto_toString%":["Object","prototype","toString"],"%ObjProto_valueOf%":["Object","prototype","valueOf"],"%PromisePrototype%":["Promise","prototype"],"%PromiseProto_then%":["Promise","prototype","then"],"%Promise_all%":["Promise","all"],"%Promise_reject%":["Promise","reject"],"%Promise_resolve%":["Promise","resolve"],"%RangeErrorPrototype%":["RangeError","prototype"],"%ReferenceErrorPrototype%":["ReferenceError","prototype"],"%RegExpPrototype%":["RegExp","prototype"],"%SetPrototype%":["Set","prototype"],"%SharedArrayBufferPrototype%":["SharedArrayBuffer","prototype"],"%StringPrototype%":["String","prototype"],"%SymbolPrototype%":["Symbol","prototype"],"%SyntaxErrorPrototype%":["SyntaxError","prototype"],"%TypedArrayPrototype%":["TypedArray","prototype"],"%TypeErrorPrototype%":["TypeError","prototype"],"%Uint8ArrayPrototype%":["Uint8Array","prototype"],"%Uint8ClampedArrayPrototype%":["Uint8ClampedArray","prototype"],"%Uint16ArrayPrototype%":["Uint16Array","prototype"],"%Uint32ArrayPrototype%":["Uint32Array","prototype"],"%URIErrorPrototype%":["URIError","prototype"],"%WeakMapPrototype%":["WeakMap","prototype"],"%WeakSetPrototype%":["WeakSet","prototype"]},Qt=mt(),ms=Gr(),iT=Qt.call(Ht,Array.prototype.concat),pT=Qt.call(Yp,Array.prototype.splice),zp=Qt.call(Ht,String.prototype.replace),ys=Qt.call(Ht,String.prototype.slice),cT=Qt.call(Ht,RegExp.prototype.exec),uT=/[^%.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|%$))/g,dT=/\\(\\)?/g,lT=function(t){var s=ys(t,0,1),r=ys(t,-1);if(s==="%"&&r!=="%")throw new qt("invalid intrinsic syntax, expected closing `%`");if(r==="%"&&s!=="%")throw new qt("invalid intrinsic syntax, expected opening `%`");var n=[];return zp(t,uT,function(o,i,p,c){n[n.length]=p?zp(c,dT,"$1"):i||o}),n},gT=function(t,s){var r=t,n;if(ms($p,r)&&(n=$p[r],r="%"+n[0]+"%"),ms(Je,r)){var o=Je[r];if(o===Rt&&(o=oT(r)),typeof o>"u"&&!s)throw new ht("intrinsic "+t+" exists, but is not available. Please file an issue!");return{alias:n,name:r,value:o}}throw new qt("intrinsic "+t+" does not exist!")};Xp.exports=function(t,s){if(typeof t!="string"||t.length===0)throw new ht("intrinsic name must be a non-empty string");if(arguments.length>1&&typeof s!="boolean")throw new ht('"allowMissing" argument must be a boolean');if(cT(/^%?[^%]*%?$/,t)===null)throw new qt("`%` may not be present anywhere but at the beginning and end of the intrinsic name");var r=lT(t),n=r.length>0?r[0]:"",o=gT("%"+n+"%",s),i=o.name,p=o.value,c=!1,a=o.alias;a&&(n=a[0],pT(r,iT([0,1],a)));for(var u=1,y=!0;u<r.length;u+=1){var R=r[u],x=ys(R,0,1),m=ys(R,-1);if((x==='"'||x==="'"||x==="`"||m==='"'||m==="'"||m==="`")&&x!==m)throw new qt("property names with quotes must have matching quotes");if((R==="constructor"||!y)&&(c=!0),n+="."+R,i="%"+n+"%",ms(Je,i))p=Je[i];else if(p!=null){if(!(R in p)){if(!s)throw new ht("base intrinsic for "+t+" exists, but the property is not available.");return}if(Dt&&u+1>=r.length){var f=Dt(p,R);y=!!f,y&&"get"in f&&!("originalValue"in f.get)?p=f.get:p=p[R]}else y=ms(p,R),p=p[R];y&&!c&&(Je[i]=p)}}return p}});var ec=b((DE,Zp)=>{"use strict";var mT=Br();Zp.exports=function(){return mT()&&!!Symbol.toStringTag}});var rc=b((HE,sc)=>{"use strict";var yT=ft(),tc=yT("%Object.defineProperty%",!0),RT=ec()(),hT=Gr(),qT=ze(),Rs=RT?Symbol.toStringTag:null;sc.exports=function(t,s){var r=arguments.length>2&&!!arguments[2]&&arguments[2].force,n=arguments.length>2&&!!arguments[2]&&arguments[2].nonConfigurable;if(typeof r<"u"&&typeof r!="boolean"||typeof n<"u"&&typeof n!="boolean")throw new qT("if provided, the `overrideIfSet` and `nonConfigurable` options must be booleans");Rs&&(r||!hT(t,Rs))&&(tc?tc(t,Rs,{configurable:!n,enumerable:!1,value:s,writable:!1}):t[Rs]=s)}});var nc=b((QE,ac)=>{ac.exports=function(e,t){return Object.keys(t).forEach(function(s){e[s]=e[s]||t[s]}),e}});var ic=b((FE,oc)=>{var Qr=mi(),fT=require("util"),_r=require("path"),bT=require("http"),xT=require("https"),IT=require("url").parse,vT=require("fs"),kT=require("stream").Stream,Dr=bi(),PT=_i(),AT=rc(),Hr=nc();oc.exports=L;fT.inherits(L,Qr);function L(e){if(!(this instanceof L))return new L(e);this._overheadLength=0,this._valueLength=0,this._valuesToMeasure=[],Qr.call(this),e=e||{};for(var t in e)this[t]=e[t]}L.LINE_BREAK=`\r
`;L.DEFAULT_CONTENT_TYPE="application/octet-stream";L.prototype.append=function(e,t,s){s=s||{},typeof s=="string"&&(s={filename:s});var r=Qr.prototype.append.bind(this);if(typeof t=="number"&&(t=""+t),Array.isArray(t)){this._error(new Error("Arrays are not supported."));return}var n=this._multiPartHeader(e,t,s),o=this._multiPartFooter();r(n),r(t),r(o),this._trackLength(n,t,s)};L.prototype._trackLength=function(e,t,s){var r=0;s.knownLength!=null?r+=+s.knownLength:Buffer.isBuffer(t)?r=t.length:typeof t=="string"&&(r=Buffer.byteLength(t)),this._valueLength+=r,this._overheadLength+=Buffer.byteLength(e)+L.LINE_BREAK.length,!(!t||!t.path&&!(t.readable&&Object.prototype.hasOwnProperty.call(t,"httpVersion"))&&!(t instanceof kT))&&(s.knownLength||this._valuesToMeasure.push(t))};L.prototype._lengthRetriever=function(e,t){Object.prototype.hasOwnProperty.call(e,"fd")?e.end!=null&&e.end!=1/0&&e.start!=null?t(null,e.end+1-(e.start?e.start:0)):vT.stat(e.path,function(s,r){var n;if(s){t(s);return}n=r.size-(e.start?e.start:0),t(null,n)}):Object.prototype.hasOwnProperty.call(e,"httpVersion")?t(null,+e.headers["content-length"]):Object.prototype.hasOwnProperty.call(e,"httpModule")?(e.on("response",function(s){e.pause(),t(null,+s.headers["content-length"])}),e.resume()):t("Unknown stream")};L.prototype._multiPartHeader=function(e,t,s){if(typeof s.header=="string")return s.header;var r=this._getContentDisposition(t,s),n=this._getContentType(t,s),o="",i={"Content-Disposition":["form-data",'name="'+e+'"'].concat(r||[]),"Content-Type":[].concat(n||[])};typeof s.header=="object"&&Hr(i,s.header);var p;for(var c in i)if(Object.prototype.hasOwnProperty.call(i,c)){if(p=i[c],p==null)continue;Array.isArray(p)||(p=[p]),p.length&&(o+=c+": "+p.join("; ")+L.LINE_BREAK)}return"--"+this.getBoundary()+L.LINE_BREAK+o+L.LINE_BREAK};L.prototype._getContentDisposition=function(e,t){var s,r;return typeof t.filepath=="string"?s=_r.normalize(t.filepath).replace(/\\/g,"/"):t.filename||e.name||e.path?s=_r.basename(t.filename||e.name||e.path):e.readable&&Object.prototype.hasOwnProperty.call(e,"httpVersion")&&(s=_r.basename(e.client._httpMessage.path||"")),s&&(r='filename="'+s+'"'),r};L.prototype._getContentType=function(e,t){var s=t.contentType;return!s&&e.name&&(s=Dr.lookup(e.name)),!s&&e.path&&(s=Dr.lookup(e.path)),!s&&e.readable&&Object.prototype.hasOwnProperty.call(e,"httpVersion")&&(s=e.headers["content-type"]),!s&&(t.filepath||t.filename)&&(s=Dr.lookup(t.filepath||t.filename)),!s&&typeof e=="object"&&(s=L.DEFAULT_CONTENT_TYPE),s};L.prototype._multiPartFooter=function(){return function(e){var t=L.LINE_BREAK,s=this._streams.length===0;s&&(t+=this._lastBoundary()),e(t)}.bind(this)};L.prototype._lastBoundary=function(){return"--"+this.getBoundary()+"--"+L.LINE_BREAK};L.prototype.getHeaders=function(e){var t,s={"content-type":"multipart/form-data; boundary="+this.getBoundary()};for(t in e)Object.prototype.hasOwnProperty.call(e,t)&&(s[t.toLowerCase()]=e[t]);return s};L.prototype.setBoundary=function(e){this._boundary=e};L.prototype.getBoundary=function(){return this._boundary||this._generateBoundary(),this._boundary};L.prototype.getBuffer=function(){for(var e=new Buffer.alloc(0),t=this.getBoundary(),s=0,r=this._streams.length;s<r;s++)typeof this._streams[s]!="function"&&(Buffer.isBuffer(this._streams[s])?e=Buffer.concat([e,this._streams[s]]):e=Buffer.concat([e,Buffer.from(this._streams[s])]),(typeof this._streams[s]!="string"||this._streams[s].substring(2,t.length+2)!==t)&&(e=Buffer.concat([e,Buffer.from(L.LINE_BREAK)])));return Buffer.concat([e,Buffer.from(this._lastBoundary())])};L.prototype._generateBoundary=function(){for(var e="--------------------------",t=0;t<24;t++)e+=Math.floor(Math.random()*10).toString(16);this._boundary=e};L.prototype.getLengthSync=function(){var e=this._overheadLength+this._valueLength;return this._streams.length&&(e+=this._lastBoundary().length),this.hasKnownLength()||this._error(new Error("Cannot calculate proper length in synchronous way.")),e};L.prototype.hasKnownLength=function(){var e=!0;return this._valuesToMeasure.length&&(e=!1),e};L.prototype.getLength=function(e){var t=this._overheadLength+this._valueLength;if(this._streams.length&&(t+=this._lastBoundary().length),!this._valuesToMeasure.length){process.nextTick(e.bind(this,null,t));return}PT.parallel(this._valuesToMeasure,this._lengthRetriever,function(s,r){if(s){e(s);return}r.forEach(function(n){t+=n}),e(null,t)})};L.prototype.submit=function(e,t){var s,r,n={method:"post"};return typeof e=="string"?(e=IT(e),r=Hr({port:e.port,path:e.pathname,host:e.hostname,protocol:e.protocol},n)):(r=Hr(e,n),r.port||(r.port=r.protocol=="https:"?443:80)),r.headers=this.getHeaders(e.headers),r.protocol=="https:"?s=xT.request(r):s=bT.request(r),this.getLength(function(o,i){if(o&&o!=="Unknown stream"){this._error(o);return}if(i&&s.setHeader("Content-Length",i),this.pipe(s),t){var p,c=function(a,u){return s.removeListener("error",c),s.removeListener("response",p),t.call(this,a,u)};p=c.bind(this,null),s.on("error",c),s.on("response",p)}}.bind(this)),s};L.prototype._error=function(e){this.error||(this.error=e,this.pause(),this.emit("error",e))};L.prototype.toString=function(){return"[object FormData]"};AT(L,"FormData")});var vc=b(Ic=>{"use strict";var OT=require("url").parse,MT={ftp:21,gopher:70,http:80,https:443,ws:80,wss:443},NT=String.prototype.endsWith||function(e){return e.length<=this.length&&this.indexOf(e,this.length-e.length)!==-1};function VT(e){var t=typeof e=="string"?OT(e):e||{},s=t.protocol,r=t.host,n=t.port;if(typeof r!="string"||!r||typeof s!="string"||(s=s.split(":",1)[0],r=r.replace(/:\d*$/,""),n=parseInt(n)||MT[s]||0,!KT(r,n)))return"";var o=vt("npm_config_"+s+"_proxy")||vt(s+"_proxy")||vt("npm_config_proxy")||vt("all_proxy");return o&&o.indexOf("://")===-1&&(o=s+"://"+o),o}function KT(e,t){var s=(vt("npm_config_no_proxy")||vt("no_proxy")).toLowerCase();return s?s==="*"?!1:s.split(/[,\s]/).every(function(r){if(!r)return!0;var n=r.match(/^(.+):(\d+)$/),o=n?n[1]:r,i=n?parseInt(n[2]):0;return i&&i!==t?!0:/^[.*]/.test(o)?(o.charAt(0)==="*"&&(o=o.slice(1)),!NT.call(e,o)):e!==o}):!0}function vt(e){return process.env[e.toLowerCase()]||process.env[e.toUpperCase()]||""}Ic.getProxyForUrl=VT});var Pc=b((OL,kc)=>{var kt=1e3,Pt=kt*60,At=Pt*60,Ze=At*24,jT=Ze*7,$T=Ze*365.25;kc.exports=function(e,t){t=t||{};var s=typeof e;if(s==="string"&&e.length>0)return zT(e);if(s==="number"&&isFinite(e))return t.long?YT(e):JT(e);throw new Error("val is not a non-empty string or a valid number. val="+JSON.stringify(e))};function zT(e){if(e=String(e),!(e.length>100)){var t=/^(-?(?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(e);if(t){var s=parseFloat(t[1]),r=(t[2]||"ms").toLowerCase();switch(r){case"years":case"year":case"yrs":case"yr":case"y":return s*$T;case"weeks":case"week":case"w":return s*jT;case"days":case"day":case"d":return s*Ze;case"hours":case"hour":case"hrs":case"hr":case"h":return s*At;case"minutes":case"minute":case"mins":case"min":case"m":return s*Pt;case"seconds":case"second":case"secs":case"sec":case"s":return s*kt;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return s;default:return}}}}function JT(e){var t=Math.abs(e);return t>=Ze?Math.round(e/Ze)+"d":t>=At?Math.round(e/At)+"h":t>=Pt?Math.round(e/Pt)+"m":t>=kt?Math.round(e/kt)+"s":e+"ms"}function YT(e){var t=Math.abs(e);return t>=Ze?bs(e,t,Ze,"day"):t>=At?bs(e,t,At,"hour"):t>=Pt?bs(e,t,Pt,"minute"):t>=kt?bs(e,t,kt,"second"):e+" ms"}function bs(e,t,s,r){var n=t>=s*1.5;return Math.round(e/s)+" "+r+(n?"s":"")}});var Jr=b((ML,Ac)=>{function XT(e){s.debug=s,s.default=s,s.coerce=c,s.disable=i,s.enable=n,s.enabled=p,s.humanize=Pc(),s.destroy=a,Object.keys(e).forEach(u=>{s[u]=e[u]}),s.names=[],s.skips=[],s.formatters={};function t(u){let y=0;for(let R=0;R<u.length;R++)y=(y<<5)-y+u.charCodeAt(R),y|=0;return s.colors[Math.abs(y)%s.colors.length]}s.selectColor=t;function s(u){let y,R=null,x,m;function f(...k){if(!f.enabled)return;let I=f,U=Number(new Date),E=U-(y||U);I.diff=E,I.prev=y,I.curr=U,y=U,k[0]=s.coerce(k[0]),typeof k[0]!="string"&&k.unshift("%O");let F=0;k[0]=k[0].replace(/%([a-zA-Z%])/g,(ue,de)=>{if(ue==="%%")return"%";F++;let le=s.formatters[de];if(typeof le=="function"){let ae=k[F];ue=le.call(I,ae),k.splice(F,1),F--}return ue}),s.formatArgs.call(I,k),(I.log||s.log).apply(I,k)}return f.namespace=u,f.useColors=s.useColors(),f.color=s.selectColor(u),f.extend=r,f.destroy=s.destroy,Object.defineProperty(f,"enabled",{enumerable:!0,configurable:!1,get:()=>R!==null?R:(x!==s.namespaces&&(x=s.namespaces,m=s.enabled(u)),m),set:k=>{R=k}}),typeof s.init=="function"&&s.init(f),f}function r(u,y){let R=s(this.namespace+(typeof y>"u"?":":y)+u);return R.log=this.log,R}function n(u){s.save(u),s.namespaces=u,s.names=[],s.skips=[];let y=(typeof u=="string"?u:"").trim().replace(" ",",").split(",").filter(Boolean);for(let R of y)R[0]==="-"?s.skips.push(R.slice(1)):s.names.push(R)}function o(u,y){let R=0,x=0,m=-1,f=0;for(;R<u.length;)if(x<y.length&&(y[x]===u[R]||y[x]==="*"))y[x]==="*"?(m=x,f=R,x++):(R++,x++);else if(m!==-1)x=m+1,f++,R=f;else return!1;for(;x<y.length&&y[x]==="*";)x++;return x===y.length}function i(){let u=[...s.names,...s.skips.map(y=>"-"+y)].join(",");return s.enable(""),u}function p(u){for(let y of s.skips)if(o(u,y))return!1;for(let y of s.names)if(o(u,y))return!0;return!1}function c(u){return u instanceof Error?u.stack||u.message:u}function a(){console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.")}return s.enable(s.load()),s}Ac.exports=XT});var Tc=b((me,xs)=>{me.formatArgs=ew;me.save=tw;me.load=sw;me.useColors=ZT;me.storage=rw();me.destroy=(()=>{let e=!1;return()=>{e||(e=!0,console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."))}})();me.colors=["#0000CC","#0000FF","#0033CC","#0033FF","#0066CC","#0066FF","#0099CC","#0099FF","#00CC00","#00CC33","#00CC66","#00CC99","#00CCCC","#00CCFF","#3300CC","#3300FF","#3333CC","#3333FF","#3366CC","#3366FF","#3399CC","#3399FF","#33CC00","#33CC33","#33CC66","#33CC99","#33CCCC","#33CCFF","#6600CC","#6600FF","#6633CC","#6633FF","#66CC00","#66CC33","#9900CC","#9900FF","#9933CC","#9933FF","#99CC00","#99CC33","#CC0000","#CC0033","#CC0066","#CC0099","#CC00CC","#CC00FF","#CC3300","#CC3333","#CC3366","#CC3399","#CC33CC","#CC33FF","#CC6600","#CC6633","#CC9900","#CC9933","#CCCC00","#CCCC33","#FF0000","#FF0033","#FF0066","#FF0099","#FF00CC","#FF00FF","#FF3300","#FF3333","#FF3366","#FF3399","#FF33CC","#FF33FF","#FF6600","#FF6633","#FF9900","#FF9933","#FFCC00","#FFCC33"];function ZT(){if(typeof window<"u"&&window.process&&(window.process.type==="renderer"||window.process.__nwjs))return!0;if(typeof navigator<"u"&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/))return!1;let e;return typeof document<"u"&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||typeof window<"u"&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||typeof navigator<"u"&&navigator.userAgent&&(e=navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/))&&parseInt(e[1],10)>=31||typeof navigator<"u"&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/)}function ew(e){if(e[0]=(this.useColors?"%c":"")+this.namespace+(this.useColors?" %c":" ")+e[0]+(this.useColors?"%c ":" ")+"+"+xs.exports.humanize(this.diff),!this.useColors)return;let t="color: "+this.color;e.splice(1,0,t,"color: inherit");let s=0,r=0;e[0].replace(/%[a-zA-Z%]/g,n=>{n!=="%%"&&(s++,n==="%c"&&(r=s))}),e.splice(r,0,t)}me.log=console.debug||console.log||(()=>{});function tw(e){try{e?me.storage.setItem("debug",e):me.storage.removeItem("debug")}catch{}}function sw(){let e;try{e=me.storage.getItem("debug")}catch{}return!e&&typeof process<"u"&&"env"in process&&(e=process.env.DEBUG),e}function rw(){try{return localStorage}catch{}}xs.exports=Jr()(me);var{formatters:aw}=xs.exports;aw.j=function(e){try{return JSON.stringify(e)}catch(t){return"[UnexpectedJSONParseError]: "+t.message}}});var Bc=b((NL,wc)=>{"use strict";wc.exports=(e,t=process.argv)=>{let s=e.startsWith("-")?"":e.length===1?"-":"--",r=t.indexOf(s+e),n=t.indexOf("--");return r!==-1&&(n===-1||r<n)}});var Ec=b((VL,Uc)=>{"use strict";var nw=require("os"),Cc=require("tty"),fe=Bc(),{env:J}=process,Oe;fe("no-color")||fe("no-colors")||fe("color=false")||fe("color=never")?Oe=0:(fe("color")||fe("colors")||fe("color=true")||fe("color=always"))&&(Oe=1);"FORCE_COLOR"in J&&(J.FORCE_COLOR==="true"?Oe=1:J.FORCE_COLOR==="false"?Oe=0:Oe=J.FORCE_COLOR.length===0?1:Math.min(parseInt(J.FORCE_COLOR,10),3));function Yr(e){return e===0?!1:{level:e,hasBasic:!0,has256:e>=2,has16m:e>=3}}function Xr(e,t){if(Oe===0)return 0;if(fe("color=16m")||fe("color=full")||fe("color=truecolor"))return 3;if(fe("color=256"))return 2;if(e&&!t&&Oe===void 0)return 0;let s=Oe||0;if(J.TERM==="dumb")return s;if(process.platform==="win32"){let r=nw.release().split(".");return Number(r[0])>=10&&Number(r[2])>=10586?Number(r[2])>=14931?3:2:1}if("CI"in J)return["TRAVIS","CIRCLECI","APPVEYOR","GITLAB_CI","GITHUB_ACTIONS","BUILDKITE"].some(r=>r in J)||J.CI_NAME==="codeship"?1:s;if("TEAMCITY_VERSION"in J)return/^(9\.(0*[1-9]\d*)\.|\d{2,}\.)/.test(J.TEAMCITY_VERSION)?1:0;if(J.COLORTERM==="truecolor")return 3;if("TERM_PROGRAM"in J){let r=parseInt((J.TERM_PROGRAM_VERSION||"").split(".")[0],10);switch(J.TERM_PROGRAM){case"iTerm.app":return r>=3?3:2;case"Apple_Terminal":return 2}}return/-256(color)?$/i.test(J.TERM)?2:/^screen|^xterm|^vt100|^vt220|^rxvt|color|ansi|cygwin|linux/i.test(J.TERM)||"COLORTERM"in J?1:s}function ow(e){let t=Xr(e,e&&e.isTTY);return Yr(t)}Uc.exports={supportsColor:ow,stdout:Yr(Xr(!0,Cc.isatty(1))),stderr:Yr(Xr(!0,Cc.isatty(2)))}});var Gc=b((Y,vs)=>{var iw=require("tty"),Is=require("util");Y.init=mw;Y.log=dw;Y.formatArgs=cw;Y.save=lw;Y.load=gw;Y.useColors=pw;Y.destroy=Is.deprecate(()=>{},"Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.");Y.colors=[6,2,3,4,5,1];try{let e=Ec();e&&(e.stderr||e).level>=2&&(Y.colors=[20,21,26,27,32,33,38,39,40,41,42,43,44,45,56,57,62,63,68,69,74,75,76,77,78,79,80,81,92,93,98,99,112,113,128,129,134,135,148,149,160,161,162,163,164,165,166,167,168,169,170,171,172,173,178,179,184,185,196,197,198,199,200,201,202,203,204,205,206,207,208,209,214,215,220,221])}catch{}Y.inspectOpts=Object.keys(process.env).filter(e=>/^debug_/i.test(e)).reduce((e,t)=>{let s=t.substring(6).toLowerCase().replace(/_([a-z])/g,(n,o)=>o.toUpperCase()),r=process.env[t];return/^(yes|on|true|enabled)$/i.test(r)?r=!0:/^(no|off|false|disabled)$/i.test(r)?r=!1:r==="null"?r=null:r=Number(r),e[s]=r,e},{});function pw(){return"colors"in Y.inspectOpts?Boolean(Y.inspectOpts.colors):iw.isatty(process.stderr.fd)}function cw(e){let{namespace:t,useColors:s}=this;if(s){let r=this.color,n="\x1B[3"+(r<8?r:"8;5;"+r),o=`  ${n};1m${t} \x1B[0m`;e[0]=o+e[0].split(`
`).join(`
`+o),e.push(n+"m+"+vs.exports.humanize(this.diff)+"\x1B[0m")}else e[0]=uw()+t+" "+e[0]}function uw(){return Y.inspectOpts.hideDate?"":new Date().toISOString()+" "}function dw(...e){return process.stderr.write(Is.formatWithOptions(Y.inspectOpts,...e)+`
`)}function lw(e){e?process.env.DEBUG=e:delete process.env.DEBUG}function gw(){return process.env.DEBUG}function mw(e){e.inspectOpts={};let t=Object.keys(Y.inspectOpts);for(let s=0;s<t.length;s++)e.inspectOpts[t[s]]=Y.inspectOpts[t[s]]}vs.exports=Jr()(Y);var{formatters:Lc}=vs.exports;Lc.o=function(e){return this.inspectOpts.colors=this.useColors,Is.inspect(e,this.inspectOpts).split(`
`).map(t=>t.trim()).join(" ")};Lc.O=function(e){return this.inspectOpts.colors=this.useColors,Is.inspect(e,this.inspectOpts)}});var Sc=b((KL,Zr)=>{typeof process>"u"||process.type==="renderer"||process.browser===!0||process.__nwjs?Zr.exports=Tc():Zr.exports=Gc()});var _c=b((jL,Wc)=>{var Nt;Wc.exports=function(){if(!Nt){try{Nt=Sc()("follow-redirects")}catch{}typeof Nt!="function"&&(Nt=function(){})}Nt.apply(null,arguments)}});var Vc=b(($L,aa)=>{var et=require("url"),ea=et.URL,yw=require("http"),Rw=require("https"),Qc=require("stream").Writable,Fc=require("assert"),Oc=_c(),sa=["abort","aborted","connect","error","socket","timeout"],ra=Object.create(null);sa.forEach(function(e){ra[e]=function(t,s,r){this._redirectable.emit(e,t,s,r)}});var hw=Kt("ERR_INVALID_URL","Invalid URL",TypeError),Dc=Kt("ERR_FR_REDIRECTION_FAILURE","Redirected request failed"),qw=Kt("ERR_FR_TOO_MANY_REDIRECTS","Maximum number of redirects exceeded"),fw=Kt("ERR_FR_MAX_BODY_LENGTH_EXCEEDED","Request body larger than maxBodyLength limit"),bw=Kt("ERR_STREAM_WRITE_AFTER_END","write after end");function ye(e,t){Qc.call(this),this._sanitizeOptions(e),this._options=e,this._ended=!1,this._ending=!1,this._redirectCount=0,this._redirects=[],this._requestBodyLength=0,this._requestBodyBuffers=[],t&&this.on("response",t);var s=this;this._onNativeResponse=function(r){s._processResponse(r)},this._performRequest()}ye.prototype=Object.create(Qc.prototype);ye.prototype.abort=function(){Nc(this._currentRequest),this.emit("abort")};ye.prototype.write=function(e,t,s){if(this._ending)throw new bw;if(!tt(e)&&!vw(e))throw new TypeError("data should be a string, Buffer or Uint8Array");if(Vt(t)&&(s=t,t=null),e.length===0){s&&s();return}this._requestBodyLength+e.length<=this._options.maxBodyLength?(this._requestBodyLength+=e.length,this._requestBodyBuffers.push({data:e,encoding:t}),this._currentRequest.write(e,t,s)):(this.emit("error",new fw),this.abort())};ye.prototype.end=function(e,t,s){if(Vt(e)?(s=e,e=t=null):Vt(t)&&(s=t,t=null),!e)this._ended=this._ending=!0,this._currentRequest.end(null,null,s);else{var r=this,n=this._currentRequest;this.write(e,t,function(){r._ended=!0,n.end(null,null,s)}),this._ending=!0}};ye.prototype.setHeader=function(e,t){this._options.headers[e]=t,this._currentRequest.setHeader(e,t)};ye.prototype.removeHeader=function(e){delete this._options.headers[e],this._currentRequest.removeHeader(e)};ye.prototype.setTimeout=function(e,t){var s=this;function r(i){i.setTimeout(e),i.removeListener("timeout",i.destroy),i.addListener("timeout",i.destroy)}function n(i){s._timeout&&clearTimeout(s._timeout),s._timeout=setTimeout(function(){s.emit("timeout"),o()},e),r(i)}function o(){s._timeout&&(clearTimeout(s._timeout),s._timeout=null),s.removeListener("abort",o),s.removeListener("error",o),s.removeListener("response",o),t&&s.removeListener("timeout",t),s.socket||s._currentRequest.removeListener("socket",n)}return t&&this.on("timeout",t),this.socket?n(this.socket):this._currentRequest.once("socket",n),this.on("socket",r),this.on("abort",o),this.on("error",o),this.on("response",o),this};["flushHeaders","getHeader","setNoDelay","setSocketKeepAlive"].forEach(function(e){ye.prototype[e]=function(t,s){return this._currentRequest[e](t,s)}});["aborted","connection","socket"].forEach(function(e){Object.defineProperty(ye.prototype,e,{get:function(){return this._currentRequest[e]}})});ye.prototype._sanitizeOptions=function(e){if(e.headers||(e.headers={}),e.host&&(e.hostname||(e.hostname=e.host),delete e.host),!e.pathname&&e.path){var t=e.path.indexOf("?");t<0?e.pathname=e.path:(e.pathname=e.path.substring(0,t),e.search=e.path.substring(t))}};ye.prototype._performRequest=function(){var e=this._options.protocol,t=this._options.nativeProtocols[e];if(!t){this.emit("error",new TypeError("Unsupported protocol "+e));return}if(this._options.agents){var s=e.slice(0,-1);this._options.agent=this._options.agents[s]}var r=this._currentRequest=t.request(this._options,this._onNativeResponse);r._redirectable=this;for(var n of sa)r.on(n,ra[n]);if(this._currentUrl=/^\//.test(this._options.path)?et.format(this._options):this._options.path,this._isRedirect){var o=0,i=this,p=this._requestBodyBuffers;(function c(a){if(r===i._currentRequest)if(a)i.emit("error",a);else if(o<p.length){var u=p[o++];r.finished||r.write(u.data,u.encoding,c)}else i._ended&&r.end()})()}};ye.prototype._processResponse=function(e){var t=e.statusCode;this._options.trackRedirects&&this._redirects.push({url:this._currentUrl,headers:e.headers,statusCode:t});var s=e.headers.location;if(!s||this._options.followRedirects===!1||t<300||t>=400){e.responseUrl=this._currentUrl,e.redirects=this._redirects,this.emit("response",e),this._requestBodyBuffers=[];return}if(Nc(this._currentRequest),e.destroy(),++this._redirectCount>this._options.maxRedirects){this.emit("error",new qw);return}var r,n=this._options.beforeRedirect;n&&(r=Object.assign({Host:e.req.getHeader("host")},this._options.headers));var o=this._options.method;((t===301||t===302)&&this._options.method==="POST"||t===303&&!/^(?:GET|HEAD)$/.test(this._options.method))&&(this._options.method="GET",this._requestBodyBuffers=[],ta(/^content-/i,this._options.headers));var i=ta(/^host$/i,this._options.headers),p=et.parse(this._currentUrl),c=i||p.host,a=/^\w+:/.test(s)?this._currentUrl:et.format(Object.assign(p,{host:c})),u;try{u=et.resolve(a,s)}catch(m){this.emit("error",new Dc({cause:m}));return}Oc("redirecting to",u),this._isRedirect=!0;var y=et.parse(u);if(Object.assign(this._options,y),(y.protocol!==p.protocol&&y.protocol!=="https:"||y.host!==c&&!Iw(y.host,c))&&ta(/^(?:authorization|cookie)$/i,this._options.headers),Vt(n)){var R={headers:e.headers,statusCode:t},x={url:a,method:o,headers:r};try{n(this._options,R,x)}catch(m){this.emit("error",m);return}this._sanitizeOptions(this._options)}try{this._performRequest()}catch(m){this.emit("error",new Dc({cause:m}))}};function Mc(e){var t={maxRedirects:21,maxBodyLength:10485760},s={};return Object.keys(e).forEach(function(r){var n=r+":",o=s[n]=e[r],i=t[r]=Object.create(o);function p(a,u,y){if(tt(a)){var R;try{R=Hc(new ea(a))}catch{R=et.parse(a)}if(!tt(R.protocol))throw new hw({input:a});a=R}else ea&&a instanceof ea?a=Hc(a):(y=u,u=a,a={protocol:n});return Vt(u)&&(y=u,u=null),u=Object.assign({maxRedirects:t.maxRedirects,maxBodyLength:t.maxBodyLength},a,u),u.nativeProtocols=s,!tt(u.host)&&!tt(u.hostname)&&(u.hostname="::1"),Fc.equal(u.protocol,n,"protocol mismatch"),Oc("options",u),new ye(u,y)}function c(a,u,y){var R=i.request(a,u,y);return R.end(),R}Object.defineProperties(i,{request:{value:p,configurable:!0,enumerable:!0,writable:!0},get:{value:c,configurable:!0,enumerable:!0,writable:!0}})}),t}function xw(){}function Hc(e){var t={protocol:e.protocol,hostname:e.hostname.startsWith("[")?e.hostname.slice(1,-1):e.hostname,hash:e.hash,search:e.search,pathname:e.pathname,path:e.pathname+e.search,href:e.href};return e.port!==""&&(t.port=Number(e.port)),t}function ta(e,t){var s;for(var r in t)e.test(r)&&(s=t[r],delete t[r]);return s===null||typeof s>"u"?void 0:String(s).trim()}function Kt(e,t,s){function r(n){Error.captureStackTrace(this,this.constructor),Object.assign(this,n||{}),this.code=e,this.message=this.cause?t+": "+this.cause.message:t}return r.prototype=new(s||Error),r.prototype.constructor=r,r.prototype.name="Error ["+e+"]",r}function Nc(e){for(var t of sa)e.removeListener(t,ra[t]);e.on("error",xw),e.abort()}function Iw(e,t){Fc(tt(e)&&tt(t));var s=e.length-t.length-1;return s>0&&e[s]==="."&&e.endsWith(t)}function tt(e){return typeof e=="string"||e instanceof String}function Vt(e){return typeof e=="function"}function vw(e){return typeof e=="object"&&"length"in e}aa.exports=Mc({http:yw,https:Rw});aa.exports.wrap=Mc});var ku=b((zS,vu)=>{"use strict";var oB=new Set(["ENOTFOUND","ENETUNREACH","UNABLE_TO_GET_ISSUER_CERT","UNABLE_TO_GET_CRL","UNABLE_TO_DECRYPT_CERT_SIGNATURE","UNABLE_TO_DECRYPT_CRL_SIGNATURE","UNABLE_TO_DECODE_ISSUER_PUBLIC_KEY","CERT_SIGNATURE_FAILURE","CRL_SIGNATURE_FAILURE","CERT_NOT_YET_VALID","CERT_HAS_EXPIRED","CRL_NOT_YET_VALID","CRL_HAS_EXPIRED","ERROR_IN_CERT_NOT_BEFORE_FIELD","ERROR_IN_CERT_NOT_AFTER_FIELD","ERROR_IN_CRL_LAST_UPDATE_FIELD","ERROR_IN_CRL_NEXT_UPDATE_FIELD","OUT_OF_MEM","DEPTH_ZERO_SELF_SIGNED_CERT","SELF_SIGNED_CERT_IN_CHAIN","UNABLE_TO_GET_ISSUER_CERT_LOCALLY","UNABLE_TO_VERIFY_LEAF_SIGNATURE","CERT_CHAIN_TOO_LONG","CERT_REVOKED","INVALID_CA","PATH_LENGTH_EXCEEDED","INVALID_PURPOSE","CERT_UNTRUSTED","CERT_REJECTED","HOSTNAME_MISMATCH"]);vu.exports=e=>!oB.has(e&&e.code)});var xa=b(at=>{"use strict";Object.defineProperty(at,"__esModule",{value:!0});var mB=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},yB=typeof window<"u"&&typeof window.document<"u",RB=typeof process<"u"&&process.versions!=null&&process.versions.node!=null,hB=(typeof self>"u"?"undefined":mB(self))==="object"&&self.constructor&&self.constructor.name==="DedicatedWorkerGlobalScope",qB=typeof window<"u"&&window.name==="nodejs"||typeof navigator<"u"&&(navigator.userAgent.includes("Node.js")||navigator.userAgent.includes("jsdom")),fB=typeof Deno<"u"&&typeof Deno.version<"u"&&typeof Deno.version.deno<"u";at.isBrowser=yB;at.isWebWorker=hB;at.isNode=RB;at.isJsDom=qB;at.isDeno=fB});var ju=b((rW,Ku)=>{"use strict";var Nu=_t(),FB=wr(),wt=ze(),Vu=Wt();Ku.exports=function(t,s,r){if(!t||typeof t!="object"&&typeof t!="function")throw new wt("`obj` must be an object or a function`");if(typeof s!="string"&&typeof s!="symbol")throw new wt("`property` must be a string or a symbol`");if(arguments.length>3&&typeof arguments[3]!="boolean"&&arguments[3]!==null)throw new wt("`nonEnumerable`, if provided, must be a boolean or null");if(arguments.length>4&&typeof arguments[4]!="boolean"&&arguments[4]!==null)throw new wt("`nonWritable`, if provided, must be a boolean or null");if(arguments.length>5&&typeof arguments[5]!="boolean"&&arguments[5]!==null)throw new wt("`nonConfigurable`, if provided, must be a boolean or null");if(arguments.length>6&&typeof arguments[6]!="boolean")throw new wt("`loose`, if provided, must be a boolean");var n=arguments.length>3?arguments[3]:null,o=arguments.length>4?arguments[4]:null,i=arguments.length>5?arguments[5]:null,p=arguments.length>6?arguments[6]:!1,c=!!Vu&&Vu(t,s);if(Nu)Nu(t,s,{configurable:i===null&&c?c.configurable:!i,enumerable:n===null&&c?c.enumerable:!n,value:r,writable:o===null&&c?c.writable:!o});else if(p||!n&&!o&&!i)t[s]=r;else throw new FB("This environment does not support defining a property as non-configurable, non-writable, or non-enumerable.")}});var Ju=b((aW,zu)=>{"use strict";var Ja=_t(),$u=function(){return!!Ja};$u.hasArrayLengthDefineBug=function(){if(!Ja)return null;try{return Ja([],"length",{value:1}).length!==1}catch{return!0}};zu.exports=$u});var td=b((nW,ed)=>{"use strict";var OB=ft(),Yu=ju(),MB=Ju()(),Xu=Wt(),Zu=ze(),NB=OB("%Math.floor%");ed.exports=function(t,s){if(typeof t!="function")throw new Zu("`fn` is not a function");if(typeof s!="number"||s<0||s>4294967295||NB(s)!==s)throw new Zu("`length` must be a positive 32-bit integer");var r=arguments.length>2&&!!arguments[2],n=!0,o=!0;if("length"in t&&Xu){var i=Xu(t,"length");i&&!i.configurable&&(n=!1),i&&!i.writable&&(o=!1)}return(n||o||!r)&&(MB?Yu(t,"length",s,!0,!0):Yu(t,"length",s)),t}});var id=b((oW,sr)=>{"use strict";var Ya=mt(),rr=ft(),VB=td(),KB=ze(),ad=rr("%Function.prototype.apply%"),nd=rr("%Function.prototype.call%"),od=rr("%Reflect.apply%",!0)||Ya.call(nd,ad),sd=_t(),jB=rr("%Math.max%");sr.exports=function(t){if(typeof t!="function")throw new KB("a function is required");var s=od(Ya,nd,arguments);return VB(s,1+jB(0,t.length-(arguments.length-1)),!0)};var rd=function(){return od(Ya,ad,arguments)};sd?sd(sr.exports,"apply",{value:rd}):sr.exports.apply=rd});var dd=b((iW,ud)=>{"use strict";var pd=ft(),cd=id(),$B=cd(pd("String.prototype.indexOf"));ud.exports=function(t,s){var r=pd(t,!!s);return typeof r=="function"&&$B(t,".prototype.")>-1?cd(r):r}});var gd=b((pW,ld)=>{ld.exports=require("util").inspect});var Gd=b((cW,Ld)=>{var pn=typeof Map=="function"&&Map.prototype,Xa=Object.getOwnPropertyDescriptor&&pn?Object.getOwnPropertyDescriptor(Map.prototype,"size"):null,nr=pn&&Xa&&typeof Xa.get=="function"?Xa.get:null,md=pn&&Map.prototype.forEach,cn=typeof Set=="function"&&Set.prototype,Za=Object.getOwnPropertyDescriptor&&cn?Object.getOwnPropertyDescriptor(Set.prototype,"size"):null,or=cn&&Za&&typeof Za.get=="function"?Za.get:null,yd=cn&&Set.prototype.forEach,zB=typeof WeakMap=="function"&&WeakMap.prototype,es=zB?WeakMap.prototype.has:null,JB=typeof WeakSet=="function"&&WeakSet.prototype,ts=JB?WeakSet.prototype.has:null,YB=typeof WeakRef=="function"&&WeakRef.prototype,Rd=YB?WeakRef.prototype.deref:null,XB=Boolean.prototype.valueOf,ZB=Object.prototype.toString,eC=Function.prototype.toString,tC=String.prototype.match,un=String.prototype.slice,je=String.prototype.replace,sC=String.prototype.toUpperCase,hd=String.prototype.toLowerCase,Ad=RegExp.prototype.test,qd=Array.prototype.concat,Ce=Array.prototype.join,rC=Array.prototype.slice,fd=Math.floor,sn=typeof BigInt=="function"?BigInt.prototype.valueOf:null,en=Object.getOwnPropertySymbols,rn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?Symbol.prototype.toString:null,Bt=typeof Symbol=="function"&&typeof Symbol.iterator=="object",se=typeof Symbol=="function"&&Symbol.toStringTag&&(typeof Symbol.toStringTag===Bt||"symbol")?Symbol.toStringTag:null,Td=Object.prototype.propertyIsEnumerable,bd=(typeof Reflect=="function"?Reflect.getPrototypeOf:Object.getPrototypeOf)||([].__proto__===Array.prototype?function(e){return e.__proto__}:null);function xd(e,t){if(e===1/0||e===-1/0||e!==e||e&&e>-1e3&&e<1e3||Ad.call(/e/,t))return t;var s=/[0-9](?=(?:[0-9]{3})+(?![0-9]))/g;if(typeof e=="number"){var r=e<0?-fd(-e):fd(e);if(r!==e){var n=String(r),o=un.call(t,n.length+1);return je.call(n,s,"$&_")+"."+je.call(je.call(o,/([0-9]{3})/g,"$&_"),/_$/,"")}}return je.call(t,s,"$&_")}var an=gd(),Id=an.custom,vd=Cd(Id)?Id:null,wd={__proto__:null,double:'"',single:"'"},aC={__proto__:null,double:/(["\\])/g,single:/(['\\])/g};Ld.exports=function e(t,s,r,n){var o=s||{};if(_e(o,"quoteStyle")&&!_e(wd,o.quoteStyle))throw new TypeError('option "quoteStyle" must be "single" or "double"');if(_e(o,"maxStringLength")&&(typeof o.maxStringLength=="number"?o.maxStringLength<0&&o.maxStringLength!==1/0:o.maxStringLength!==null))throw new TypeError('option "maxStringLength", if provided, must be a positive integer, Infinity, or `null`');var i=_e(o,"customInspect")?o.customInspect:!0;if(typeof i!="boolean"&&i!=="symbol")throw new TypeError("option \"customInspect\", if provided, must be `true`, `false`, or `'symbol'`");if(_e(o,"indent")&&o.indent!==null&&o.indent!=="	"&&!(parseInt(o.indent,10)===o.indent&&o.indent>0))throw new TypeError('option "indent" must be "\\t", an integer > 0, or `null`');if(_e(o,"numericSeparator")&&typeof o.numericSeparator!="boolean")throw new TypeError('option "numericSeparator", if provided, must be `true` or `false`');var p=o.numericSeparator;if(typeof t>"u")return"undefined";if(t===null)return"null";if(typeof t=="boolean")return t?"true":"false";if(typeof t=="string")return Ed(t,o);if(typeof t=="number"){if(t===0)return 1/0/t>0?"0":"-0";var c=String(t);return p?xd(t,c):c}if(typeof t=="bigint"){var a=String(t)+"n";return p?xd(t,a):a}var u=typeof o.depth>"u"?5:o.depth;if(typeof r>"u"&&(r=0),r>=u&&u>0&&typeof t=="object")return nn(t)?"[Array]":"[Object]";var y=IC(o,r);if(typeof n>"u")n=[];else if(Ud(n,t)>=0)return"[Circular]";function R(ge,M,pe){if(M&&(n=rC.call(n),n.push(M)),pe){var Le={depth:o.depth};return _e(o,"quoteStyle")&&(Le.quoteStyle=o.quoteStyle),e(ge,Le,r+1,n)}return e(ge,o,r+1,n)}if(typeof t=="function"&&!kd(t)){var x=gC(t),m=ar(t,R);return"[Function"+(x?": "+x:" (anonymous)")+"]"+(m.length>0?" { "+Ce.call(m,", ")+" }":"")}if(Cd(t)){var f=Bt?je.call(String(t),/^(Symbol\(.*\))_[^)]*$/,"$1"):rn.call(t);return typeof t=="object"&&!Bt?Zt(f):f}if(fC(t)){for(var k="<"+hd.call(String(t.nodeName)),I=t.attributes||[],U=0;U<I.length;U++)k+=" "+I[U].name+"="+Bd(nC(I[U].value),"double",o);return k+=">",t.childNodes&&t.childNodes.length&&(k+="..."),k+="</"+hd.call(String(t.nodeName))+">",k}if(nn(t)){if(t.length===0)return"[]";var E=ar(t,R);return y&&!xC(E)?"["+on(E,y)+"]":"[ "+Ce.call(E,", ")+" ]"}if(iC(t)){var F=ar(t,R);return!("cause"in Error.prototype)&&"cause"in t&&!Td.call(t,"cause")?"{ ["+String(t)+"] "+Ce.call(qd.call("[cause]: "+R(t.cause),F),", ")+" }":F.length===0?"["+String(t)+"]":"{ ["+String(t)+"] "+Ce.call(F,", ")+" }"}if(typeof t=="object"&&i){if(vd&&typeof t[vd]=="function"&&an)return an(t,{depth:u-r});if(i!=="symbol"&&typeof t.inspect=="function")return t.inspect()}if(mC(t)){var O=[];return md&&md.call(t,function(ge,M){O.push(R(M,t,!0)+" => "+R(ge,t))}),Pd("Map",nr.call(t),O,y)}if(hC(t)){var ue=[];return yd&&yd.call(t,function(ge){ue.push(R(ge,t))}),Pd("Set",or.call(t),ue,y)}if(yC(t))return tn("WeakMap");if(qC(t))return tn("WeakSet");if(RC(t))return tn("WeakRef");if(cC(t))return Zt(R(Number(t)));if(dC(t))return Zt(R(sn.call(t)));if(uC(t))return Zt(XB.call(t));if(pC(t))return Zt(R(String(t)));if(typeof window<"u"&&t===window)return"{ [object Window] }";if(typeof globalThis<"u"&&t===globalThis||typeof global<"u"&&t===global)return"{ [object globalThis] }";if(!oC(t)&&!kd(t)){var de=ar(t,R),le=bd?bd(t)===Object.prototype:t instanceof Object||t.constructor===Object,ae=t instanceof Object?"":"null prototype",Te=!le&&se&&Object(t)===t&&se in t?un.call($e(t),8,-1):ae?"Object":"",Ee=le||typeof t.constructor!="function"?"":t.constructor.name?t.constructor.name+" ":"",Re=Ee+(Te||ae?"["+Ce.call(qd.call([],Te||[],ae||[]),": ")+"] ":"");return de.length===0?Re+"{}":y?Re+"{"+on(de,y)+"}":Re+"{ "+Ce.call(de,", ")+" }"}return String(t)};function Bd(e,t,s){var r=s.quoteStyle||t,n=wd[r];return n+e+n}function nC(e){return je.call(String(e),/"/g,"&quot;")}function nn(e){return $e(e)==="[object Array]"&&(!se||!(typeof e=="object"&&se in e))}function oC(e){return $e(e)==="[object Date]"&&(!se||!(typeof e=="object"&&se in e))}function kd(e){return $e(e)==="[object RegExp]"&&(!se||!(typeof e=="object"&&se in e))}function iC(e){return $e(e)==="[object Error]"&&(!se||!(typeof e=="object"&&se in e))}function pC(e){return $e(e)==="[object String]"&&(!se||!(typeof e=="object"&&se in e))}function cC(e){return $e(e)==="[object Number]"&&(!se||!(typeof e=="object"&&se in e))}function uC(e){return $e(e)==="[object Boolean]"&&(!se||!(typeof e=="object"&&se in e))}function Cd(e){if(Bt)return e&&typeof e=="object"&&e instanceof Symbol;if(typeof e=="symbol")return!0;if(!e||typeof e!="object"||!rn)return!1;try{return rn.call(e),!0}catch{}return!1}function dC(e){if(!e||typeof e!="object"||!sn)return!1;try{return sn.call(e),!0}catch{}return!1}var lC=Object.prototype.hasOwnProperty||function(e){return e in this};function _e(e,t){return lC.call(e,t)}function $e(e){return ZB.call(e)}function gC(e){if(e.name)return e.name;var t=tC.call(eC.call(e),/^function\s*([\w$]+)/);return t?t[1]:null}function Ud(e,t){if(e.indexOf)return e.indexOf(t);for(var s=0,r=e.length;s<r;s++)if(e[s]===t)return s;return-1}function mC(e){if(!nr||!e||typeof e!="object")return!1;try{nr.call(e);try{or.call(e)}catch{return!0}return e instanceof Map}catch{}return!1}function yC(e){if(!es||!e||typeof e!="object")return!1;try{es.call(e,es);try{ts.call(e,ts)}catch{return!0}return e instanceof WeakMap}catch{}return!1}function RC(e){if(!Rd||!e||typeof e!="object")return!1;try{return Rd.call(e),!0}catch{}return!1}function hC(e){if(!or||!e||typeof e!="object")return!1;try{or.call(e);try{nr.call(e)}catch{return!0}return e instanceof Set}catch{}return!1}function qC(e){if(!ts||!e||typeof e!="object")return!1;try{ts.call(e,ts);try{es.call(e,es)}catch{return!0}return e instanceof WeakSet}catch{}return!1}function fC(e){return!e||typeof e!="object"?!1:typeof HTMLElement<"u"&&e instanceof HTMLElement?!0:typeof e.nodeName=="string"&&typeof e.getAttribute=="function"}function Ed(e,t){if(e.length>t.maxStringLength){var s=e.length-t.maxStringLength,r="... "+s+" more character"+(s>1?"s":"");return Ed(un.call(e,0,t.maxStringLength),t)+r}var n=aC[t.quoteStyle||"single"];n.lastIndex=0;var o=je.call(je.call(e,n,"\\$1"),/[\x00-\x1f]/g,bC);return Bd(o,"single",t)}function bC(e){var t=e.charCodeAt(0),s={8:"b",9:"t",10:"n",12:"f",13:"r"}[t];return s?"\\"+s:"\\x"+(t<16?"0":"")+sC.call(t.toString(16))}function Zt(e){return"Object("+e+")"}function tn(e){return e+" { ? }"}function Pd(e,t,s,r){var n=r?on(s,r):Ce.call(s,", ");return e+" ("+t+") {"+n+"}"}function xC(e){for(var t=0;t<e.length;t++)if(Ud(e[t],`
`)>=0)return!1;return!0}function IC(e,t){var s;if(e.indent==="	")s="	";else if(typeof e.indent=="number"&&e.indent>0)s=Ce.call(Array(e.indent+1)," ");else return null;return{base:s,prev:Ce.call(Array(t+1),s)}}function on(e,t){if(e.length===0)return"";var s=`
`+t.prev+t.base;return s+Ce.call(e,","+s)+`
`+t.prev}function ar(e,t){var s=nn(e),r=[];if(s){r.length=e.length;for(var n=0;n<e.length;n++)r[n]=_e(e,n)?t(e[n],e):""}var o=typeof en=="function"?en(e):[],i;if(Bt){i={};for(var p=0;p<o.length;p++)i["$"+o[p]]=o[p]}for(var c in e)_e(e,c)&&(s&&String(Number(c))===c&&c<e.length||Bt&&i["$"+c]instanceof Symbol||(Ad.call(/[^\w$]/,c)?r.push(t(c,e)+": "+t(e[c],e)):r.push(c+": "+t(e[c],e))));if(typeof en=="function")for(var a=0;a<o.length;a++)Td.call(e,o[a])&&r.push("["+t(o[a])+"]: "+t(e[o[a]],e));return r}});var Wd=b((uW,Sd)=>{"use strict";var dn=ft(),Ct=dd(),vC=Gd(),kC=dn("%TypeError%"),ir=dn("%WeakMap%",!0),pr=dn("%Map%",!0),PC=Ct("WeakMap.prototype.get",!0),AC=Ct("WeakMap.prototype.set",!0),TC=Ct("WeakMap.prototype.has",!0),wC=Ct("Map.prototype.get",!0),BC=Ct("Map.prototype.set",!0),CC=Ct("Map.prototype.has",!0),ln=function(e,t){for(var s=e,r;(r=s.next)!==null;s=r)if(r.key===t)return s.next=r.next,r.next=e.next,e.next=r,r},UC=function(e,t){var s=ln(e,t);return s&&s.value},EC=function(e,t,s){var r=ln(e,t);r?r.value=s:e.next={key:t,next:e.next,value:s}},LC=function(e,t){return!!ln(e,t)};Sd.exports=function(){var t,s,r,n={assert:function(o){if(!n.has(o))throw new kC("Side channel does not contain "+vC(o))},get:function(o){if(ir&&o&&(typeof o=="object"||typeof o=="function")){if(t)return PC(t,o)}else if(pr){if(s)return wC(s,o)}else if(r)return UC(r,o)},has:function(o){if(ir&&o&&(typeof o=="object"||typeof o=="function")){if(t)return TC(t,o)}else if(pr){if(s)return CC(s,o)}else if(r)return LC(r,o);return!1},set:function(o,i){ir&&o&&(typeof o=="object"||typeof o=="function")?(t||(t=new ir),AC(t,o,i)):pr?(s||(s=new pr),BC(s,o,i)):(r||(r={key:{},next:null}),EC(r,o,i))}};return n}});var cr=b((dW,_d)=>{"use strict";var GC=String.prototype.replace,SC=/%20/g,gn={RFC1738:"RFC1738",RFC3986:"RFC3986"};_d.exports={default:gn.RFC3986,formatters:{RFC1738:function(e){return GC.call(e,SC,"+")},RFC3986:function(e){return String(e)}},RFC1738:gn.RFC1738,RFC3986:gn.RFC3986}});var yn=b((lW,Hd)=>{"use strict";var WC=cr(),mn=Object.prototype.hasOwnProperty,it=Array.isArray,Ue=function(){for(var e=[],t=0;t<256;++t)e.push("%"+((t<16?"0":"")+t.toString(16)).toUpperCase());return e}(),_C=function(t){for(;t.length>1;){var s=t.pop(),r=s.obj[s.prop];if(it(r)){for(var n=[],o=0;o<r.length;++o)typeof r[o]<"u"&&n.push(r[o]);s.obj[s.prop]=n}}},Dd=function(t,s){for(var r=s&&s.plainObjects?Object.create(null):{},n=0;n<t.length;++n)typeof t[n]<"u"&&(r[n]=t[n]);return r},DC=function e(t,s,r){if(!s)return t;if(typeof s!="object"){if(it(t))t.push(s);else if(t&&typeof t=="object")(r&&(r.plainObjects||r.allowPrototypes)||!mn.call(Object.prototype,s))&&(t[s]=!0);else return[t,s];return t}if(!t||typeof t!="object")return[t].concat(s);var n=t;return it(t)&&!it(s)&&(n=Dd(t,r)),it(t)&&it(s)?(s.forEach(function(o,i){if(mn.call(t,i)){var p=t[i];p&&typeof p=="object"&&o&&typeof o=="object"?t[i]=e(p,o,r):t.push(o)}else t[i]=o}),t):Object.keys(s).reduce(function(o,i){var p=s[i];return mn.call(o,i)?o[i]=e(o[i],p,r):o[i]=p,o},n)},HC=function(t,s){return Object.keys(s).reduce(function(r,n){return r[n]=s[n],r},t)},QC=function(e,t,s){var r=e.replace(/\+/g," ");if(s==="iso-8859-1")return r.replace(/%[0-9a-f]{2}/gi,unescape);try{return decodeURIComponent(r)}catch{return r}},FC=function(t,s,r,n,o){if(t.length===0)return t;var i=t;if(typeof t=="symbol"?i=Symbol.prototype.toString.call(t):typeof t!="string"&&(i=String(t)),r==="iso-8859-1")return escape(i).replace(/%u[0-9a-f]{4}/gi,function(u){return"%26%23"+parseInt(u.slice(2),16)+"%3B"});for(var p="",c=0;c<i.length;++c){var a=i.charCodeAt(c);if(a===45||a===46||a===95||a===126||a>=48&&a<=57||a>=65&&a<=90||a>=97&&a<=122||o===WC.RFC1738&&(a===40||a===41)){p+=i.charAt(c);continue}if(a<128){p=p+Ue[a];continue}if(a<2048){p=p+(Ue[192|a>>6]+Ue[128|a&63]);continue}if(a<55296||a>=57344){p=p+(Ue[224|a>>12]+Ue[128|a>>6&63]+Ue[128|a&63]);continue}c+=1,a=65536+((a&1023)<<10|i.charCodeAt(c)&1023),p+=Ue[240|a>>18]+Ue[128|a>>12&63]+Ue[128|a>>6&63]+Ue[128|a&63]}return p},OC=function(t){for(var s=[{obj:{o:t},prop:"o"}],r=[],n=0;n<s.length;++n)for(var o=s[n],i=o.obj[o.prop],p=Object.keys(i),c=0;c<p.length;++c){var a=p[c],u=i[a];typeof u=="object"&&u!==null&&r.indexOf(u)===-1&&(s.push({obj:i,prop:a}),r.push(u))}return _C(s),t},MC=function(t){return Object.prototype.toString.call(t)==="[object RegExp]"},NC=function(t){return!t||typeof t!="object"?!1:!!(t.constructor&&t.constructor.isBuffer&&t.constructor.isBuffer(t))},VC=function(t,s){return[].concat(t,s)},KC=function(t,s){if(it(t)){for(var r=[],n=0;n<t.length;n+=1)r.push(s(t[n]));return r}return s(t)};Hd.exports={arrayToObject:Dd,assign:HC,combine:VC,compact:OC,decode:QC,encode:FC,isBuffer:NC,isRegExp:MC,maybeMap:KC,merge:DC}});var Vd=b((gW,Nd)=>{"use strict";var Od=Wd(),hn=yn(),ss=cr(),jC=Object.prototype.hasOwnProperty,Qd={brackets:function(t){return t+"[]"},comma:"comma",indices:function(t,s){return t+"["+s+"]"},repeat:function(t){return t}},De=Array.isArray,$C=String.prototype.split,zC=Array.prototype.push,Md=function(e,t){zC.apply(e,De(t)?t:[t])},JC=Date.prototype.toISOString,Fd=ss.default,ee={addQueryPrefix:!1,allowDots:!1,charset:"utf-8",charsetSentinel:!1,delimiter:"&",encode:!0,encoder:hn.encode,encodeValuesOnly:!1,format:Fd,formatter:ss.formatters[Fd],indices:!1,serializeDate:function(t){return JC.call(t)},skipNulls:!1,strictNullHandling:!1},YC=function(t){return typeof t=="string"||typeof t=="number"||typeof t=="boolean"||typeof t=="symbol"||typeof t=="bigint"},Rn={},XC=function e(t,s,r,n,o,i,p,c,a,u,y,R,x,m,f,k){for(var I=t,U=k,E=0,F=!1;(U=U.get(Rn))!==void 0&&!F;){var O=U.get(t);if(E+=1,typeof O<"u"){if(O===E)throw new RangeError("Cyclic object value");F=!0}typeof U.get(Rn)>"u"&&(E=0)}if(typeof c=="function"?I=c(s,I):I instanceof Date?I=y(I):r==="comma"&&De(I)&&(I=hn.maybeMap(I,function(V){return V instanceof Date?y(V):V})),I===null){if(o)return p&&!m?p(s,ee.encoder,f,"key",R):s;I=""}if(YC(I)||hn.isBuffer(I)){if(p){var ue=m?s:p(s,ee.encoder,f,"key",R);if(r==="comma"&&m){for(var de=$C.call(String(I),","),le="",ae=0;ae<de.length;++ae)le+=(ae===0?"":",")+x(p(de[ae],ee.encoder,f,"value",R));return[x(ue)+(n&&De(I)&&de.length===1?"[]":"")+"="+le]}return[x(ue)+"="+x(p(I,ee.encoder,f,"value",R))]}return[x(s)+"="+x(String(I))]}var Te=[];if(typeof I>"u")return Te;var Ee;if(r==="comma"&&De(I))Ee=[{value:I.length>0?I.join(",")||null:void 0}];else if(De(c))Ee=c;else{var Re=Object.keys(I);Ee=a?Re.sort(a):Re}for(var ge=n&&De(I)&&I.length===1?s+"[]":s,M=0;M<Ee.length;++M){var pe=Ee[M],Le=typeof pe=="object"&&typeof pe.value<"u"?pe.value:I[pe];if(!(i&&Le===null)){var T=De(I)?typeof r=="function"?r(ge,pe):ge:ge+(u?"."+pe:"["+pe+"]");k.set(t,E);var P=Od();P.set(Rn,k),Md(Te,e(Le,T,r,n,o,i,p,c,a,u,y,R,x,m,f,P))}}return Te},ZC=function(t){if(!t)return ee;if(t.encoder!==null&&typeof t.encoder<"u"&&typeof t.encoder!="function")throw new TypeError("Encoder has to be a function.");var s=t.charset||ee.charset;if(typeof t.charset<"u"&&t.charset!=="utf-8"&&t.charset!=="iso-8859-1")throw new TypeError("The charset option must be either utf-8, iso-8859-1, or undefined");var r=ss.default;if(typeof t.format<"u"){if(!jC.call(ss.formatters,t.format))throw new TypeError("Unknown format option provided.");r=t.format}var n=ss.formatters[r],o=ee.filter;return(typeof t.filter=="function"||De(t.filter))&&(o=t.filter),{addQueryPrefix:typeof t.addQueryPrefix=="boolean"?t.addQueryPrefix:ee.addQueryPrefix,allowDots:typeof t.allowDots>"u"?ee.allowDots:!!t.allowDots,charset:s,charsetSentinel:typeof t.charsetSentinel=="boolean"?t.charsetSentinel:ee.charsetSentinel,delimiter:typeof t.delimiter>"u"?ee.delimiter:t.delimiter,encode:typeof t.encode=="boolean"?t.encode:ee.encode,encoder:typeof t.encoder=="function"?t.encoder:ee.encoder,encodeValuesOnly:typeof t.encodeValuesOnly=="boolean"?t.encodeValuesOnly:ee.encodeValuesOnly,filter:o,format:r,formatter:n,serializeDate:typeof t.serializeDate=="function"?t.serializeDate:ee.serializeDate,skipNulls:typeof t.skipNulls=="boolean"?t.skipNulls:ee.skipNulls,sort:typeof t.sort=="function"?t.sort:null,strictNullHandling:typeof t.strictNullHandling=="boolean"?t.strictNullHandling:ee.strictNullHandling}};Nd.exports=function(e,t){var s=e,r=ZC(t),n,o;typeof r.filter=="function"?(o=r.filter,s=o("",s)):De(r.filter)&&(o=r.filter,n=o);var i=[];if(typeof s!="object"||s===null)return"";var p;t&&t.arrayFormat in Qd?p=t.arrayFormat:t&&"indices"in t?p=t.indices?"indices":"repeat":p="indices";var c=Qd[p];if(t&&"commaRoundTrip"in t&&typeof t.commaRoundTrip!="boolean")throw new TypeError("`commaRoundTrip` must be a boolean, or absent");var a=c==="comma"&&t&&t.commaRoundTrip;n||(n=Object.keys(s)),r.sort&&n.sort(r.sort);for(var u=Od(),y=0;y<n.length;++y){var R=n[y];r.skipNulls&&s[R]===null||Md(i,XC(s[R],R,c,a,r.strictNullHandling,r.skipNulls,r.encode?r.encoder:null,r.filter,r.sort,r.allowDots,r.serializeDate,r.format,r.formatter,r.encodeValuesOnly,r.charset,u))}var x=i.join(r.delimiter),m=r.addQueryPrefix===!0?"?":"";return r.charsetSentinel&&(r.charset==="iso-8859-1"?m+="utf8=%26%2310003%3B&":m+="utf8=%E2%9C%93&"),x.length>0?m+x:""}});var $d=b((mW,jd)=>{"use strict";var Ut=yn(),qn=Object.prototype.hasOwnProperty,eU=Array.isArray,X={allowDots:!1,allowPrototypes:!1,allowSparse:!1,arrayLimit:20,charset:"utf-8",charsetSentinel:!1,comma:!1,decoder:Ut.decode,delimiter:"&",depth:5,ignoreQueryPrefix:!1,interpretNumericEntities:!1,parameterLimit:1e3,parseArrays:!0,plainObjects:!1,strictNullHandling:!1},tU=function(e){return e.replace(/&#(\d+);/g,function(t,s){return String.fromCharCode(parseInt(s,10))})},Kd=function(e,t){return e&&typeof e=="string"&&t.comma&&e.indexOf(",")>-1?e.split(","):e},sU="utf8=%26%2310003%3B",rU="utf8=%E2%9C%93",aU=function(t,s){var r={},n=s.ignoreQueryPrefix?t.replace(/^\?/,""):t,o=s.parameterLimit===1/0?void 0:s.parameterLimit,i=n.split(s.delimiter,o),p=-1,c,a=s.charset;if(s.charsetSentinel)for(c=0;c<i.length;++c)i[c].indexOf("utf8=")===0&&(i[c]===rU?a="utf-8":i[c]===sU&&(a="iso-8859-1"),p=c,c=i.length);for(c=0;c<i.length;++c)if(c!==p){var u=i[c],y=u.indexOf("]="),R=y===-1?u.indexOf("="):y+1,x,m;R===-1?(x=s.decoder(u,X.decoder,a,"key"),m=s.strictNullHandling?null:""):(x=s.decoder(u.slice(0,R),X.decoder,a,"key"),m=Ut.maybeMap(Kd(u.slice(R+1),s),function(f){return s.decoder(f,X.decoder,a,"value")})),m&&s.interpretNumericEntities&&a==="iso-8859-1"&&(m=tU(m)),u.indexOf("[]=")>-1&&(m=eU(m)?[m]:m),qn.call(r,x)?r[x]=Ut.combine(r[x],m):r[x]=m}return r},nU=function(e,t,s,r){for(var n=r?t:Kd(t,s),o=e.length-1;o>=0;--o){var i,p=e[o];if(p==="[]"&&s.parseArrays)i=[].concat(n);else{i=s.plainObjects?Object.create(null):{};var c=p.charAt(0)==="["&&p.charAt(p.length-1)==="]"?p.slice(1,-1):p,a=parseInt(c,10);!s.parseArrays&&c===""?i={0:n}:!isNaN(a)&&p!==c&&String(a)===c&&a>=0&&s.parseArrays&&a<=s.arrayLimit?(i=[],i[a]=n):c!=="__proto__"&&(i[c]=n)}n=i}return n},oU=function(t,s,r,n){if(t){var o=r.allowDots?t.replace(/\.([^.[]+)/g,"[$1]"):t,i=/(\[[^[\]]*])/,p=/(\[[^[\]]*])/g,c=r.depth>0&&i.exec(o),a=c?o.slice(0,c.index):o,u=[];if(a){if(!r.plainObjects&&qn.call(Object.prototype,a)&&!r.allowPrototypes)return;u.push(a)}for(var y=0;r.depth>0&&(c=p.exec(o))!==null&&y<r.depth;){if(y+=1,!r.plainObjects&&qn.call(Object.prototype,c[1].slice(1,-1))&&!r.allowPrototypes)return;u.push(c[1])}return c&&u.push("["+o.slice(c.index)+"]"),nU(u,s,r,n)}},iU=function(t){if(!t)return X;if(t.decoder!==null&&t.decoder!==void 0&&typeof t.decoder!="function")throw new TypeError("Decoder has to be a function.");if(typeof t.charset<"u"&&t.charset!=="utf-8"&&t.charset!=="iso-8859-1")throw new TypeError("The charset option must be either utf-8, iso-8859-1, or undefined");var s=typeof t.charset>"u"?X.charset:t.charset;return{allowDots:typeof t.allowDots>"u"?X.allowDots:!!t.allowDots,allowPrototypes:typeof t.allowPrototypes=="boolean"?t.allowPrototypes:X.allowPrototypes,allowSparse:typeof t.allowSparse=="boolean"?t.allowSparse:X.allowSparse,arrayLimit:typeof t.arrayLimit=="number"?t.arrayLimit:X.arrayLimit,charset:s,charsetSentinel:typeof t.charsetSentinel=="boolean"?t.charsetSentinel:X.charsetSentinel,comma:typeof t.comma=="boolean"?t.comma:X.comma,decoder:typeof t.decoder=="function"?t.decoder:X.decoder,delimiter:typeof t.delimiter=="string"||Ut.isRegExp(t.delimiter)?t.delimiter:X.delimiter,depth:typeof t.depth=="number"||t.depth===!1?+t.depth:X.depth,ignoreQueryPrefix:t.ignoreQueryPrefix===!0,interpretNumericEntities:typeof t.interpretNumericEntities=="boolean"?t.interpretNumericEntities:X.interpretNumericEntities,parameterLimit:typeof t.parameterLimit=="number"?t.parameterLimit:X.parameterLimit,parseArrays:t.parseArrays!==!1,plainObjects:typeof t.plainObjects=="boolean"?t.plainObjects:X.plainObjects,strictNullHandling:typeof t.strictNullHandling=="boolean"?t.strictNullHandling:X.strictNullHandling}};jd.exports=function(e,t){var s=iU(t);if(e===""||e===null||typeof e>"u")return s.plainObjects?Object.create(null):{};for(var r=typeof e=="string"?aU(e,s):e,n=s.plainObjects?Object.create(null):{},o=Object.keys(r),i=0;i<o.length;++i){var p=o[i],c=oU(p,r[p],s,typeof e=="string");n=Ut.merge(n,c,s)}return s.allowSparse===!0?n:Ut.compact(n)}});var Et=b((yW,zd)=>{"use strict";var pU=Vd(),cU=$d(),uU=cr();zd.exports={formats:uU,parse:cU,stringify:pU}});var WU={};he(WU,{AlreadyExistsError:()=>Js,BreakingChangesError:()=>tr,Client:()=>zo,ForbiddenError:()=>Ss,InternalError:()=>Ls,InvalidDataFormatError:()=>Os,InvalidIdentifierError:()=>Ms,InvalidJsonSchemaError:()=>Fs,InvalidPayloadError:()=>_s,InvalidQueryError:()=>$s,LimitExceededError:()=>er,MethodNotFoundError:()=>Hs,PayloadTooLargeError:()=>Ws,PaymentRequiredError:()=>Xs,QuotaExceededError:()=>Zs,RateLimitedError:()=>Ys,ReferenceConstraintError:()=>Vs,ReferenceNotFoundError:()=>js,RelationConflictError:()=>Ns,ResourceLockedConflictError:()=>Ks,ResourceNotFoundError:()=>Qs,RuntimeError:()=>zs,UnauthorizedError:()=>Gs,UnknownError:()=>Ke,UnsupportedMediaTypeError:()=>Ds,UploadFileError:()=>We,admin:()=>jn,axios:()=>Ra,axiosRetry:()=>ba,errorFrom:()=>nt,files:()=>xo,isApiError:()=>Fu,runtime:()=>bn,tables:()=>$o});module.exports=cP(WU);var Ra={};he(Ra,{Axios:()=>Nw,AxiosError:()=>Vw,AxiosHeaders:()=>tB,Cancel:()=>Yw,CancelToken:()=>$w,CanceledError:()=>Kw,HttpStatusCode:()=>sB,VERSION:()=>zw,all:()=>Jw,default:()=>H,formToJSON:()=>rB,getAdapter:()=>aB,isAxiosError:()=>Xw,isCancel:()=>jw,mergeConfig:()=>nB,spread:()=>Zw,toFormData:()=>eB});function Lt(e,t){return function(){return e.apply(t,arguments)}}var{toString:uP}=Object.prototype,{getPrototypeOf:br}=Object,os=(e=>t=>{let s=uP.call(t);return e[s]||(e[s]=s.slice(8,-1).toLowerCase())})(Object.create(null)),Be=e=>(e=e.toLowerCase(),t=>os(t)===e),is=e=>t=>typeof t===e,{isArray:lt}=Array,Gt=is("undefined");function dP(e){return e!==null&&!Gt(e)&&e.constructor!==null&&!Gt(e.constructor)&&qe(e.constructor.isBuffer)&&e.constructor.isBuffer(e)}var Zo=Be("ArrayBuffer");function lP(e){let t;return typeof ArrayBuffer<"u"&&ArrayBuffer.isView?t=ArrayBuffer.isView(e):t=e&&e.buffer&&Zo(e.buffer),t}var gP=is("string"),qe=is("function"),ei=is("number"),ps=e=>e!==null&&typeof e=="object",mP=e=>e===!0||e===!1,ns=e=>{if(os(e)!=="object")return!1;let t=br(e);return(t===null||t===Object.prototype||Object.getPrototypeOf(t)===null)&&!(Symbol.toStringTag in e)&&!(Symbol.iterator in e)},yP=Be("Date"),RP=Be("File"),hP=Be("Blob"),qP=Be("FileList"),fP=e=>ps(e)&&qe(e.pipe),bP=e=>{let t;return e&&(typeof FormData=="function"&&e instanceof FormData||qe(e.append)&&((t=os(e))==="formdata"||t==="object"&&qe(e.toString)&&e.toString()==="[object FormData]"))},xP=Be("URLSearchParams"),IP=e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function St(e,t,{allOwnKeys:s=!1}={}){if(e===null||typeof e>"u")return;let r,n;if(typeof e!="object"&&(e=[e]),lt(e))for(r=0,n=e.length;r<n;r++)t.call(null,e[r],r,e);else{let o=s?Object.getOwnPropertyNames(e):Object.keys(e),i=o.length,p;for(r=0;r<i;r++)p=o[r],t.call(null,e[p],p,e)}}function ti(e,t){t=t.toLowerCase();let s=Object.keys(e),r=s.length,n;for(;r-- >0;)if(n=s[r],t===n.toLowerCase())return n;return null}var si=(()=>typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:global)(),ri=e=>!Gt(e)&&e!==si;function fr(){let{caseless:e}=ri(this)&&this||{},t={},s=(r,n)=>{let o=e&&ti(t,n)||n;ns(t[o])&&ns(r)?t[o]=fr(t[o],r):ns(r)?t[o]=fr({},r):lt(r)?t[o]=r.slice():t[o]=r};for(let r=0,n=arguments.length;r<n;r++)arguments[r]&&St(arguments[r],s);return t}var vP=(e,t,s,{allOwnKeys:r}={})=>(St(t,(n,o)=>{s&&qe(n)?e[o]=Lt(n,s):e[o]=n},{allOwnKeys:r}),e),kP=e=>(e.charCodeAt(0)===65279&&(e=e.slice(1)),e),PP=(e,t,s,r)=>{e.prototype=Object.create(t.prototype,r),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:t.prototype}),s&&Object.assign(e.prototype,s)},AP=(e,t,s,r)=>{let n,o,i,p={};if(t=t||{},e==null)return t;do{for(n=Object.getOwnPropertyNames(e),o=n.length;o-- >0;)i=n[o],(!r||r(i,e,t))&&!p[i]&&(t[i]=e[i],p[i]=!0);e=s!==!1&&br(e)}while(e&&(!s||s(e,t))&&e!==Object.prototype);return t},TP=(e,t,s)=>{e=String(e),(s===void 0||s>e.length)&&(s=e.length),s-=t.length;let r=e.indexOf(t,s);return r!==-1&&r===s},wP=e=>{if(!e)return null;if(lt(e))return e;let t=e.length;if(!ei(t))return null;let s=new Array(t);for(;t-- >0;)s[t]=e[t];return s},BP=(e=>t=>e&&t instanceof e)(typeof Uint8Array<"u"&&br(Uint8Array)),CP=(e,t)=>{let r=(e&&e[Symbol.iterator]).call(e),n;for(;(n=r.next())&&!n.done;){let o=n.value;t.call(e,o[0],o[1])}},UP=(e,t)=>{let s,r=[];for(;(s=e.exec(t))!==null;)r.push(s);return r},EP=Be("HTMLFormElement"),LP=e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(s,r,n){return r.toUpperCase()+n}),Yo=(({hasOwnProperty:e})=>(t,s)=>e.call(t,s))(Object.prototype),GP=Be("RegExp"),ai=(e,t)=>{let s=Object.getOwnPropertyDescriptors(e),r={};St(s,(n,o)=>{let i;(i=t(n,o,e))!==!1&&(r[o]=i||n)}),Object.defineProperties(e,r)},SP=e=>{ai(e,(t,s)=>{if(qe(e)&&["arguments","caller","callee"].indexOf(s)!==-1)return!1;let r=e[s];if(qe(r)){if(t.enumerable=!1,"writable"in t){t.writable=!1;return}t.set||(t.set=()=>{throw Error("Can not rewrite read-only method '"+s+"'")})}})},WP=(e,t)=>{let s={},r=n=>{n.forEach(o=>{s[o]=!0})};return lt(e)?r(e):r(String(e).split(t)),s},_P=()=>{},DP=(e,t)=>(e=+e,Number.isFinite(e)?e:t),qr="abcdefghijklmnopqrstuvwxyz",Xo="0123456789",ni={DIGIT:Xo,ALPHA:qr,ALPHA_DIGIT:qr+qr.toUpperCase()+Xo},HP=(e=16,t=ni.ALPHA_DIGIT)=>{let s="",{length:r}=t;for(;e--;)s+=t[Math.random()*r|0];return s};function QP(e){return!!(e&&qe(e.append)&&e[Symbol.toStringTag]==="FormData"&&e[Symbol.iterator])}var FP=e=>{let t=new Array(10),s=(r,n)=>{if(ps(r)){if(t.indexOf(r)>=0)return;if(!("toJSON"in r)){t[n]=r;let o=lt(r)?[]:{};return St(r,(i,p)=>{let c=s(i,n+1);!Gt(c)&&(o[p]=c)}),t[n]=void 0,o}}return r};return s(e,0)},OP=Be("AsyncFunction"),MP=e=>e&&(ps(e)||qe(e))&&qe(e.then)&&qe(e.catch),g={isArray:lt,isArrayBuffer:Zo,isBuffer:dP,isFormData:bP,isArrayBufferView:lP,isString:gP,isNumber:ei,isBoolean:mP,isObject:ps,isPlainObject:ns,isUndefined:Gt,isDate:yP,isFile:RP,isBlob:hP,isRegExp:GP,isFunction:qe,isStream:fP,isURLSearchParams:xP,isTypedArray:BP,isFileList:qP,forEach:St,merge:fr,extend:vP,trim:IP,stripBOM:kP,inherits:PP,toFlatObject:AP,kindOf:os,kindOfTest:Be,endsWith:TP,toArray:wP,forEachEntry:CP,matchAll:UP,isHTMLForm:EP,hasOwnProperty:Yo,hasOwnProp:Yo,reduceDescriptors:ai,freezeMethods:SP,toObjectSet:WP,toCamelCase:LP,noop:_P,toFiniteNumber:DP,findKey:ti,global:si,isContextDefined:ri,ALPHABET:ni,generateString:HP,isSpecCompliantForm:QP,toJSONObject:FP,isAsyncFn:OP,isThenable:MP};function gt(e,t,s,r,n){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack,this.message=e,this.name="AxiosError",t&&(this.code=t),s&&(this.config=s),r&&(this.request=r),n&&(this.response=n)}g.inherits(gt,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:g.toJSONObject(this.config),code:this.code,status:this.response&&this.response.status?this.response.status:null}}});var oi=gt.prototype,ii={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(e=>{ii[e]={value:e}});Object.defineProperties(gt,ii);Object.defineProperty(oi,"isAxiosError",{value:!0});gt.from=(e,t,s,r,n,o)=>{let i=Object.create(oi);return g.toFlatObject(e,i,function(c){return c!==Error.prototype},p=>p!=="isAxiosError"),gt.call(i,e.message,t,s,r,n),i.cause=e,i.name=e.name,o&&Object.assign(i,o),i};var v=gt;var pc=Q(ic(),1),hs=pc.default;function Fr(e){return g.isPlainObject(e)||g.isArray(e)}function uc(e){return g.endsWith(e,"[]")?e.slice(0,-2):e}function cc(e,t,s){return e?e.concat(t).map(function(n,o){return n=uc(n),!s&&o?"["+n+"]":n}).join(s?".":""):t}function TT(e){return g.isArray(e)&&!e.some(Fr)}var wT=g.toFlatObject(g,{},null,function(t){return/^is[A-Z]/.test(t)});function BT(e,t,s){if(!g.isObject(e))throw new TypeError("target must be an object");t=t||new(hs||FormData),s=g.toFlatObject(s,{metaTokens:!0,dots:!1,indexes:!1},!1,function(f,k){return!g.isUndefined(k[f])});let r=s.metaTokens,n=s.visitor||u,o=s.dots,i=s.indexes,c=(s.Blob||typeof Blob<"u"&&Blob)&&g.isSpecCompliantForm(t);if(!g.isFunction(n))throw new TypeError("visitor must be a function");function a(m){if(m===null)return"";if(g.isDate(m))return m.toISOString();if(!c&&g.isBlob(m))throw new v("Blob is not supported. Use a Buffer instead.");return g.isArrayBuffer(m)||g.isTypedArray(m)?c&&typeof Blob=="function"?new Blob([m]):Buffer.from(m):m}function u(m,f,k){let I=m;if(m&&!k&&typeof m=="object"){if(g.endsWith(f,"{}"))f=r?f:f.slice(0,-2),m=JSON.stringify(m);else if(g.isArray(m)&&TT(m)||(g.isFileList(m)||g.endsWith(f,"[]"))&&(I=g.toArray(m)))return f=uc(f),I.forEach(function(E,F){!(g.isUndefined(E)||E===null)&&t.append(i===!0?cc([f],F,o):i===null?f:f+"[]",a(E))}),!1}return Fr(m)?!0:(t.append(cc(k,f,o),a(m)),!1)}let y=[],R=Object.assign(wT,{defaultVisitor:u,convertValue:a,isVisitable:Fr});function x(m,f){if(!g.isUndefined(m)){if(y.indexOf(m)!==-1)throw Error("Circular reference detected in "+f.join("."));y.push(m),g.forEach(m,function(I,U){(!(g.isUndefined(I)||I===null)&&n.call(t,I,g.isString(U)?U.trim():U,f,R))===!0&&x(I,f?f.concat(U):[U])}),y.pop()}}if(!g.isObject(e))throw new TypeError("data must be an object");return x(e),t}var Qe=BT;function dc(e){let t={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,function(r){return t[r]})}function lc(e,t){this._pairs=[],e&&Qe(e,this,t)}var gc=lc.prototype;gc.append=function(t,s){this._pairs.push([t,s])};gc.toString=function(t){let s=t?function(r){return t.call(this,r,dc)}:dc;return this._pairs.map(function(n){return s(n[0])+"="+s(n[1])},"").join("&")};var mc=lc;function CT(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function Ye(e,t,s){if(!t)return e;let r=s&&s.encode||CT,n=s&&s.serialize,o;if(n?o=n(t,s):o=g.isURLSearchParams(t)?t.toString():new mc(t,s).toString(r),o){let i=e.indexOf("#");i!==-1&&(e=e.slice(0,i)),e+=(e.indexOf("?")===-1?"?":"&")+o}return e}var Or=class{constructor(){this.handlers=[]}use(t,s,r){return this.handlers.push({fulfilled:t,rejected:s,synchronous:r?r.synchronous:!1,runWhen:r?r.runWhen:null}),this.handlers.length-1}eject(t){this.handlers[t]&&(this.handlers[t]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(t){g.forEach(this.handlers,function(r){r!==null&&t(r)})}},Mr=Or;var bt={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1};var yc=Q(require("url"),1),Rc=yc.default.URLSearchParams;var hc={isNode:!0,classes:{URLSearchParams:Rc,FormData:hs,Blob:typeof Blob<"u"&&Blob||null},protocols:["http","https","file","data"]};var Nr={};he(Nr,{hasBrowserEnv:()=>qc,hasStandardBrowserEnv:()=>UT,hasStandardBrowserWebWorkerEnv:()=>ET});var qc=typeof window<"u"&&typeof document<"u",UT=(e=>qc&&["ReactNative","NativeScript","NS"].indexOf(e)<0)(typeof navigator<"u"&&navigator.product),ET=(()=>typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope&&typeof self.importScripts=="function")();var j={...Nr,...hc};function Vr(e,t){return Qe(e,new j.classes.URLSearchParams,Object.assign({visitor:function(s,r,n,o){return j.isNode&&g.isBuffer(s)?(this.append(r,s.toString("base64")),!1):o.defaultVisitor.apply(this,arguments)}},t))}function LT(e){return g.matchAll(/\w+|\[(\w*)]/g,e).map(t=>t[0]==="[]"?"":t[1]||t[0])}function GT(e){let t={},s=Object.keys(e),r,n=s.length,o;for(r=0;r<n;r++)o=s[r],t[o]=e[o];return t}function ST(e){function t(s,r,n,o){let i=s[o++],p=Number.isFinite(+i),c=o>=s.length;return i=!i&&g.isArray(n)?n.length:i,c?(g.hasOwnProp(n,i)?n[i]=[n[i],r]:n[i]=r,!p):((!n[i]||!g.isObject(n[i]))&&(n[i]=[]),t(s,r,n[i],o)&&g.isArray(n[i])&&(n[i]=GT(n[i])),!p)}if(g.isFormData(e)&&g.isFunction(e.entries)){let s={};return g.forEachEntry(e,(r,n)=>{t(LT(r),n,s,0)}),s}return null}var qs=ST;function WT(e,t,s){if(g.isString(e))try{return(t||JSON.parse)(e),g.trim(e)}catch(r){if(r.name!=="SyntaxError")throw r}return(s||JSON.stringify)(e)}var Kr={transitional:bt,adapter:["xhr","http"],transformRequest:[function(t,s){let r=s.getContentType()||"",n=r.indexOf("application/json")>-1,o=g.isObject(t);if(o&&g.isHTMLForm(t)&&(t=new FormData(t)),g.isFormData(t))return n&&n?JSON.stringify(qs(t)):t;if(g.isArrayBuffer(t)||g.isBuffer(t)||g.isStream(t)||g.isFile(t)||g.isBlob(t))return t;if(g.isArrayBufferView(t))return t.buffer;if(g.isURLSearchParams(t))return s.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),t.toString();let p;if(o){if(r.indexOf("application/x-www-form-urlencoded")>-1)return Vr(t,this.formSerializer).toString();if((p=g.isFileList(t))||r.indexOf("multipart/form-data")>-1){let c=this.env&&this.env.FormData;return Qe(p?{"files[]":t}:t,c&&new c,this.formSerializer)}}return o||n?(s.setContentType("application/json",!1),WT(t)):t}],transformResponse:[function(t){let s=this.transitional||Kr.transitional,r=s&&s.forcedJSONParsing,n=this.responseType==="json";if(t&&g.isString(t)&&(r&&!this.responseType||n)){let i=!(s&&s.silentJSONParsing)&&n;try{return JSON.parse(t)}catch(p){if(i)throw p.name==="SyntaxError"?v.from(p,v.ERR_BAD_RESPONSE,this,null,this.response):p}}return t}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:j.classes.FormData,Blob:j.classes.Blob},validateStatus:function(t){return t>=200&&t<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};g.forEach(["delete","get","head","post","put","patch"],e=>{Kr.headers[e]={}});var xt=Kr;var _T=g.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),fc=e=>{let t={},s,r,n;return e&&e.split(`
`).forEach(function(i){n=i.indexOf(":"),s=i.substring(0,n).trim().toLowerCase(),r=i.substring(n+1).trim(),!(!s||t[s]&&_T[s])&&(s==="set-cookie"?t[s]?t[s].push(r):t[s]=[r]:t[s]=t[s]?t[s]+", "+r:r)}),t};var bc=Symbol("internals");function Ft(e){return e&&String(e).trim().toLowerCase()}function fs(e){return e===!1||e==null?e:g.isArray(e)?e.map(fs):String(e)}function DT(e){let t=Object.create(null),s=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g,r;for(;r=s.exec(e);)t[r[1]]=r[2];return t}var HT=e=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim());function jr(e,t,s,r,n){if(g.isFunction(r))return r.call(this,t,s);if(n&&(t=s),!!g.isString(t)){if(g.isString(r))return t.indexOf(r)!==-1;if(g.isRegExp(r))return r.test(t)}}function QT(e){return e.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(t,s,r)=>s.toUpperCase()+r)}function FT(e,t){let s=g.toCamelCase(" "+t);["get","set","has"].forEach(r=>{Object.defineProperty(e,r+s,{value:function(n,o,i){return this[r].call(this,t,n,o,i)},configurable:!0})})}var It=class{constructor(t){t&&this.set(t)}set(t,s,r){let n=this;function o(p,c,a){let u=Ft(c);if(!u)throw new Error("header name must be a non-empty string");let y=g.findKey(n,u);(!y||n[y]===void 0||a===!0||a===void 0&&n[y]!==!1)&&(n[y||c]=fs(p))}let i=(p,c)=>g.forEach(p,(a,u)=>o(a,u,c));return g.isPlainObject(t)||t instanceof this.constructor?i(t,s):g.isString(t)&&(t=t.trim())&&!HT(t)?i(fc(t),s):t!=null&&o(s,t,r),this}get(t,s){if(t=Ft(t),t){let r=g.findKey(this,t);if(r){let n=this[r];if(!s)return n;if(s===!0)return DT(n);if(g.isFunction(s))return s.call(this,n,r);if(g.isRegExp(s))return s.exec(n);throw new TypeError("parser must be boolean|regexp|function")}}}has(t,s){if(t=Ft(t),t){let r=g.findKey(this,t);return!!(r&&this[r]!==void 0&&(!s||jr(this,this[r],r,s)))}return!1}delete(t,s){let r=this,n=!1;function o(i){if(i=Ft(i),i){let p=g.findKey(r,i);p&&(!s||jr(r,r[p],p,s))&&(delete r[p],n=!0)}}return g.isArray(t)?t.forEach(o):o(t),n}clear(t){let s=Object.keys(this),r=s.length,n=!1;for(;r--;){let o=s[r];(!t||jr(this,this[o],o,t,!0))&&(delete this[o],n=!0)}return n}normalize(t){let s=this,r={};return g.forEach(this,(n,o)=>{let i=g.findKey(r,o);if(i){s[i]=fs(n),delete s[o];return}let p=t?QT(o):String(o).trim();p!==o&&delete s[o],s[p]=fs(n),r[p]=!0}),this}concat(...t){return this.constructor.concat(this,...t)}toJSON(t){let s=Object.create(null);return g.forEach(this,(r,n)=>{r!=null&&r!==!1&&(s[n]=t&&g.isArray(r)?r.join(", "):r)}),s}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([t,s])=>t+": "+s).join(`
`)}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(t){return t instanceof this?t:new this(t)}static concat(t,...s){let r=new this(t);return s.forEach(n=>r.set(n)),r}static accessor(t){let r=(this[bc]=this[bc]={accessors:{}}).accessors,n=this.prototype;function o(i){let p=Ft(i);r[p]||(FT(n,i),r[p]=!0)}return g.isArray(t)?t.forEach(o):o(t),this}};It.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]);g.reduceDescriptors(It.prototype,({value:e},t)=>{let s=t[0].toUpperCase()+t.slice(1);return{get:()=>e,set(r){this[s]=r}}});g.freezeMethods(It);var $=It;function Ot(e,t){let s=this||xt,r=t||s,n=$.from(r.headers),o=r.data;return g.forEach(e,function(p){o=p.call(s,o,n.normalize(),t?t.status:void 0)}),n.normalize(),o}function Mt(e){return!!(e&&e.__CANCEL__)}function xc(e,t,s){v.call(this,e??"canceled",v.ERR_CANCELED,t,s),this.name="CanceledError"}g.inherits(xc,v,{__CANCEL__:!0});var Pe=xc;function Fe(e,t,s){let r=s.config.validateStatus;!s.status||!r||r(s.status)?e(s):t(new v("Request failed with status code "+s.status,[v.ERR_BAD_REQUEST,v.ERR_BAD_RESPONSE][Math.floor(s.status/100)-4],s.config,s.request,s))}function $r(e){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(e)}function zr(e,t){return t?e.replace(/\/+$/,"")+"/"+t.replace(/^\/+/,""):e}function Xe(e,t){return e&&!$r(t)?zr(e,t):t}var nu=Q(vc(),1),ou=Q(require("http"),1),iu=Q(require("https"),1),pu=Q(require("util"),1),cu=Q(Vc(),1),Ge=Q(require("zlib"),1);var st="1.6.1";function jt(e){let t=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return t&&t[1]||""}var kw=/^(?:([^;]+);)?(?:[^;]+;)?(base64|),([\s\S]*)$/;function na(e,t,s){let r=s&&s.Blob||j.classes.Blob,n=jt(e);if(t===void 0&&r&&(t=!0),n==="data"){e=n.length?e.slice(n.length+1):e;let o=kw.exec(e);if(!o)throw new v("Invalid URL",v.ERR_INVALID_URL);let i=o[1],p=o[2],c=o[3],a=Buffer.from(decodeURIComponent(c),p?"base64":"utf8");if(t){if(!r)throw new v("Blob is not supported",v.ERR_NOT_SUPPORT);return new r([a],{type:i})}return a}throw new v("Unsupported protocol "+n,v.ERR_NOT_SUPPORT)}var rt=Q(require("stream"),1);var jc=Q(require("stream"),1);function Pw(e,t){let s=0,r=1e3/t,n=null;return function(i,p){let c=Date.now();if(i||c-s>r)return n&&(clearTimeout(n),n=null),s=c,e.apply(null,p);n||(n=setTimeout(()=>(n=null,s=Date.now(),e.apply(null,p)),r-(c-s)))}}var Kc=Pw;function Aw(e,t){e=e||10;let s=new Array(e),r=new Array(e),n=0,o=0,i;return t=t!==void 0?t:1e3,function(c){let a=Date.now(),u=r[o];i||(i=a),s[n]=c,r[n]=a;let y=o,R=0;for(;y!==n;)R+=s[y++],y=y%e;if(n=(n+1)%e,n===o&&(o=(o+1)%e),a-i<t)return;let x=u&&a-u;return x?Math.round(R*1e3/x):void 0}}var ks=Aw;var Ps=Symbol("internals"),oa=class extends jc.default.Transform{constructor(t){t=g.toFlatObject(t,{maxRate:0,chunkSize:64*1024,minChunkSize:100,timeWindow:500,ticksRate:2,samplesCount:15},null,(p,c)=>!g.isUndefined(c[p])),super({readableHighWaterMark:t.chunkSize});let s=this,r=this[Ps]={length:t.length,timeWindow:t.timeWindow,ticksRate:t.ticksRate,chunkSize:t.chunkSize,maxRate:t.maxRate,minChunkSize:t.minChunkSize,bytesSeen:0,isCaptured:!1,notifiedBytesLoaded:0,ts:Date.now(),bytes:0,onReadCallback:null},n=ks(r.ticksRate*t.samplesCount,r.timeWindow);this.on("newListener",p=>{p==="progress"&&(r.isCaptured||(r.isCaptured=!0))});let o=0;r.updateProgress=Kc(function(){let c=r.length,a=r.bytesSeen,u=a-o;if(!u||s.destroyed)return;let y=n(u);o=a,process.nextTick(()=>{s.emit("progress",{loaded:a,total:c,progress:c?a/c:void 0,bytes:u,rate:y||void 0,estimated:y&&c&&a<=c?(c-a)/y:void 0})})},r.ticksRate);let i=()=>{r.updateProgress(!0)};this.once("end",i),this.once("error",i)}_read(t){let s=this[Ps];return s.onReadCallback&&s.onReadCallback(),super._read(t)}_transform(t,s,r){let n=this,o=this[Ps],i=o.maxRate,p=this.readableHighWaterMark,c=o.timeWindow,a=1e3/c,u=i/a,y=o.minChunkSize!==!1?Math.max(o.minChunkSize,u*.01):0;function R(m,f){let k=Buffer.byteLength(m);o.bytesSeen+=k,o.bytes+=k,o.isCaptured&&o.updateProgress(),n.push(m)?process.nextTick(f):o.onReadCallback=()=>{o.onReadCallback=null,process.nextTick(f)}}let x=(m,f)=>{let k=Buffer.byteLength(m),I=null,U=p,E,F=0;if(i){let O=Date.now();(!o.ts||(F=O-o.ts)>=c)&&(o.ts=O,E=u-o.bytes,o.bytes=E<0?-E:0,F=0),E=u-o.bytes}if(i){if(E<=0)return setTimeout(()=>{f(null,m)},c-F);E<U&&(U=E)}U&&k>U&&k-U>y&&(I=m.subarray(U),m=m.subarray(0,U)),R(m,I?()=>{process.nextTick(f,null,I)}:f)};x(t,function m(f,k){if(f)return r(f);k?x(k,m):r(null)})}setLength(t){return this[Ps].length=+t,this}},ia=oa;var uu=Q(require("events"),1);var zc=require("util"),Jc=require("stream");var{asyncIterator:$c}=Symbol,Tw=async function*(e){e.stream?yield*e.stream():e.arrayBuffer?yield await e.arrayBuffer():e[$c]?yield*e[$c]():yield e},As=Tw;var ww=g.ALPHABET.ALPHA_DIGIT+"-_",$t=new zc.TextEncoder,Me=`\r
`,Bw=$t.encode(Me),Cw=2,pa=class{constructor(t,s){let{escapeName:r}=this.constructor,n=g.isString(s),o=`Content-Disposition: form-data; name="${r(t)}"${!n&&s.name?`; filename="${r(s.name)}"`:""}${Me}`;n?s=$t.encode(String(s).replace(/\r?\n|\r\n?/g,Me)):o+=`Content-Type: ${s.type||"application/octet-stream"}${Me}`,this.headers=$t.encode(o+Me),this.contentLength=n?s.byteLength:s.size,this.size=this.headers.byteLength+this.contentLength+Cw,this.name=t,this.value=s}async*encode(){yield this.headers;let{value:t}=this;g.isTypedArray(t)?yield t:yield*As(t),yield Bw}static escapeName(t){return String(t).replace(/[\r\n"]/g,s=>({"\r":"%0D","\n":"%0A",'"':"%22"})[s])}},Uw=(e,t,s)=>{let{tag:r="form-data-boundary",size:n=25,boundary:o=r+"-"+g.generateString(n,ww)}=s||{};if(!g.isFormData(e))throw TypeError("FormData instance required");if(o.length<1||o.length>70)throw Error("boundary must be 10-70 characters long");let i=$t.encode("--"+o+Me),p=$t.encode("--"+o+"--"+Me+Me),c=p.byteLength,a=Array.from(e.entries()).map(([y,R])=>{let x=new pa(y,R);return c+=x.size,x});c+=i.byteLength*a.length,c=g.toFiniteNumber(c);let u={"Content-Type":`multipart/form-data; boundary=${o}`};return Number.isFinite(c)&&(u["Content-Length"]=c),t&&t(u),Jc.Readable.from(async function*(){for(let y of a)yield i,yield*y.encode();yield p}())},Yc=Uw;var Xc=Q(require("stream"),1),ca=class extends Xc.default.Transform{__transform(t,s,r){this.push(t),r()}_transform(t,s,r){if(t.length!==0&&(this._transform=this.__transform,t[0]!==120)){let n=Buffer.alloc(2);n[0]=120,n[1]=156,this.push(n,s)}this.__transform(t,s,r)}},Zc=ca;var Ew=(e,t)=>g.isAsyncFn(e)?function(...s){let r=s.pop();e.apply(this,s).then(n=>{try{t?r(null,...t(n)):r(null,n)}catch(o){r(o)}},r)}:e,eu=Ew;var tu={flush:Ge.default.constants.Z_SYNC_FLUSH,finishFlush:Ge.default.constants.Z_SYNC_FLUSH},Lw={flush:Ge.default.constants.BROTLI_OPERATION_FLUSH,finishFlush:Ge.default.constants.BROTLI_OPERATION_FLUSH},su=g.isFunction(Ge.default.createBrotliDecompress),{http:Gw,https:Sw}=cu.default,Ww=/https:?/,ru=j.protocols.map(e=>e+":");function _w(e){e.beforeRedirects.proxy&&e.beforeRedirects.proxy(e),e.beforeRedirects.config&&e.beforeRedirects.config(e)}function du(e,t,s){let r=t;if(!r&&r!==!1){let n=(0,nu.getProxyForUrl)(s);n&&(r=new URL(n))}if(r){if(r.username&&(r.auth=(r.username||"")+":"+(r.password||"")),r.auth){(r.auth.username||r.auth.password)&&(r.auth=(r.auth.username||"")+":"+(r.auth.password||""));let o=Buffer.from(r.auth,"utf8").toString("base64");e.headers["Proxy-Authorization"]="Basic "+o}e.headers.host=e.hostname+(e.port?":"+e.port:"");let n=r.hostname||r.host;e.hostname=n,e.host=n,e.port=r.port,e.path=s,r.protocol&&(e.protocol=r.protocol.includes(":")?r.protocol:`${r.protocol}:`)}e.beforeRedirects.proxy=function(o){du(o,t,o.href)}}var Dw=typeof process<"u"&&g.kindOf(process)==="process",Hw=e=>new Promise((t,s)=>{let r,n,o=(c,a)=>{n||(n=!0,r&&r(c,a))},i=c=>{o(c),t(c)},p=c=>{o(c,!0),s(c)};e(i,p,c=>r=c).catch(p)}),Qw=({address:e,family:t})=>{if(!g.isString(e))throw TypeError("address must be a string");return{address:e,family:t||(e.indexOf(".")<0?6:4)}},au=(e,t)=>Qw(g.isObject(e)?e:{address:e,family:t}),lu=Dw&&function(t){return Hw(async function(r,n,o){let{data:i,lookup:p,family:c}=t,{responseType:a,responseEncoding:u}=t,y=t.method.toUpperCase(),R,x=!1,m;if(p){let T=eu(p,P=>g.isArray(P)?P:[P]);p=(P,V,dt)=>{T(P,V,(ne,He,Rr)=>{let we=g.isArray(He)?He.map(ve=>au(ve)):[au(He,Rr)];V.all?dt(ne,we):dt(ne,we[0].address,we[0].family)})}}let f=new uu.default,k=()=>{t.cancelToken&&t.cancelToken.unsubscribe(I),t.signal&&t.signal.removeEventListener("abort",I),f.removeAllListeners()};o((T,P)=>{R=!0,P&&(x=!0,k())});function I(T){f.emit("abort",!T||T.type?new Pe(null,t,m):T)}f.once("abort",n),(t.cancelToken||t.signal)&&(t.cancelToken&&t.cancelToken.subscribe(I),t.signal&&(t.signal.aborted?I():t.signal.addEventListener("abort",I)));let U=Xe(t.baseURL,t.url),E=new URL(U,"http://localhost"),F=E.protocol||ru[0];if(F==="data:"){let T;if(y!=="GET")return Fe(r,n,{status:405,statusText:"method not allowed",headers:{},config:t});try{T=na(t.url,a==="blob",{Blob:t.env&&t.env.Blob})}catch(P){throw v.from(P,v.ERR_BAD_REQUEST,t)}return a==="text"?(T=T.toString(u),(!u||u==="utf8")&&(T=g.stripBOM(T))):a==="stream"&&(T=rt.default.Readable.from(T)),Fe(r,n,{data:T,status:200,statusText:"OK",headers:new $,config:t})}if(ru.indexOf(F)===-1)return n(new v("Unsupported protocol "+F,v.ERR_BAD_REQUEST,t));let O=$.from(t.headers).normalize();O.set("User-Agent","axios/"+st,!1);let ue=t.onDownloadProgress,de=t.onUploadProgress,le=t.maxRate,ae,Te;if(g.isSpecCompliantForm(i)){let T=O.getContentType(/boundary=([-_\w\d]{10,70})/i);i=Yc(i,P=>{O.set(P)},{tag:`axios-${st}-boundary`,boundary:T&&T[1]||void 0})}else if(g.isFormData(i)&&g.isFunction(i.getHeaders)){if(O.set(i.getHeaders()),!O.hasContentLength())try{let T=await pu.default.promisify(i.getLength).call(i);Number.isFinite(T)&&T>=0&&O.setContentLength(T)}catch{}}else if(g.isBlob(i))i.size&&O.setContentType(i.type||"application/octet-stream"),O.setContentLength(i.size||0),i=rt.default.Readable.from(As(i));else if(i&&!g.isStream(i)){if(!Buffer.isBuffer(i))if(g.isArrayBuffer(i))i=Buffer.from(new Uint8Array(i));else if(g.isString(i))i=Buffer.from(i,"utf-8");else return n(new v("Data after transformation must be a string, an ArrayBuffer, a Buffer, or a Stream",v.ERR_BAD_REQUEST,t));if(O.setContentLength(i.length,!1),t.maxBodyLength>-1&&i.length>t.maxBodyLength)return n(new v("Request body larger than maxBodyLength limit",v.ERR_BAD_REQUEST,t))}let Ee=g.toFiniteNumber(O.getContentLength());g.isArray(le)?(ae=le[0],Te=le[1]):ae=Te=le,i&&(de||ae)&&(g.isStream(i)||(i=rt.default.Readable.from(i,{objectMode:!1})),i=rt.default.pipeline([i,new ia({length:Ee,maxRate:g.toFiniteNumber(ae)})],g.noop),de&&i.on("progress",T=>{de(Object.assign(T,{upload:!0}))}));let Re;if(t.auth){let T=t.auth.username||"",P=t.auth.password||"";Re=T+":"+P}if(!Re&&E.username){let T=E.username,P=E.password;Re=T+":"+P}Re&&O.delete("authorization");let ge;try{ge=Ye(E.pathname+E.search,t.params,t.paramsSerializer).replace(/^\?/,"")}catch(T){let P=new Error(T.message);return P.config=t,P.url=t.url,P.exists=!0,n(P)}O.set("Accept-Encoding","gzip, compress, deflate"+(su?", br":""),!1);let M={path:ge,method:y,headers:O.toJSON(),agents:{http:t.httpAgent,https:t.httpsAgent},auth:Re,protocol:F,family:c,beforeRedirect:_w,beforeRedirects:{}};!g.isUndefined(p)&&(M.lookup=p),t.socketPath?M.socketPath=t.socketPath:(M.hostname=E.hostname,M.port=E.port,du(M,t.proxy,F+"//"+E.hostname+(E.port?":"+E.port:"")+M.path));let pe,Le=Ww.test(M.protocol);if(M.agent=Le?t.httpsAgent:t.httpAgent,t.transport?pe=t.transport:t.maxRedirects===0?pe=Le?iu.default:ou.default:(t.maxRedirects&&(M.maxRedirects=t.maxRedirects),t.beforeRedirect&&(M.beforeRedirects.config=t.beforeRedirect),pe=Le?Sw:Gw),t.maxBodyLength>-1?M.maxBodyLength=t.maxBodyLength:M.maxBodyLength=1/0,t.insecureHTTPParser&&(M.insecureHTTPParser=t.insecureHTTPParser),m=pe.request(M,function(P){if(m.destroyed)return;let V=[P],dt=+P.headers["content-length"];if(ue){let ve=new ia({length:g.toFiniteNumber(dt),maxRate:g.toFiniteNumber(Te)});ue&&ve.on("progress",rs=>{ue(Object.assign(rs,{download:!0}))}),V.push(ve)}let ne=P,He=P.req||m;if(t.decompress!==!1&&P.headers["content-encoding"])switch((y==="HEAD"||P.statusCode===204)&&delete P.headers["content-encoding"],(P.headers["content-encoding"]||"").toLowerCase()){case"gzip":case"x-gzip":case"compress":case"x-compress":V.push(Ge.default.createUnzip(tu)),delete P.headers["content-encoding"];break;case"deflate":V.push(new Zc),V.push(Ge.default.createUnzip(tu)),delete P.headers["content-encoding"];break;case"br":su&&(V.push(Ge.default.createBrotliDecompress(Lw)),delete P.headers["content-encoding"])}ne=V.length>1?rt.default.pipeline(V,g.noop):V[0];let Rr=rt.default.finished(ne,()=>{Rr(),k()}),we={status:P.statusCode,statusText:P.statusMessage,headers:new $(P.headers),config:t,request:He};if(a==="stream")we.data=ne,Fe(r,n,we);else{let ve=[],rs=0;ne.on("data",function(oe){ve.push(oe),rs+=oe.length,t.maxContentLength>-1&&rs>t.maxContentLength&&(x=!0,ne.destroy(),n(new v("maxContentLength size of "+t.maxContentLength+" exceeded",v.ERR_BAD_RESPONSE,t,He)))}),ne.on("aborted",function(){if(x)return;let oe=new v("maxContentLength size of "+t.maxContentLength+" exceeded",v.ERR_BAD_RESPONSE,t,He);ne.destroy(oe),n(oe)}),ne.on("error",function(oe){m.destroyed||n(v.from(oe,null,t,He))}),ne.on("end",function(){try{let oe=ve.length===1?ve[0]:Buffer.concat(ve);a!=="arraybuffer"&&(oe=oe.toString(u),(!u||u==="utf8")&&(oe=g.stripBOM(oe))),we.data=oe}catch(oe){return n(v.from(oe,null,t,we.request,we))}Fe(r,n,we)})}f.once("abort",ve=>{ne.destroyed||(ne.emit("error",ve),ne.destroy())})}),f.once("abort",T=>{n(T),m.destroy(T)}),m.on("error",function(P){n(v.from(P,null,t,m))}),m.on("socket",function(P){P.setKeepAlive(!0,1e3*60)}),t.timeout){let T=parseInt(t.timeout,10);if(Number.isNaN(T)){n(new v("error trying to parse `config.timeout` to int",v.ERR_BAD_OPTION_VALUE,t,m));return}m.setTimeout(T,function(){if(R)return;let V=t.timeout?"timeout of "+t.timeout+"ms exceeded":"timeout exceeded",dt=t.transitional||bt;t.timeoutErrorMessage&&(V=t.timeoutErrorMessage),n(new v(V,dt.clarifyTimeoutError?v.ETIMEDOUT:v.ECONNABORTED,t,m)),I()})}if(g.isStream(i)){let T=!1,P=!1;i.on("end",()=>{T=!0}),i.once("error",V=>{P=!0,m.destroy(V)}),i.on("close",()=>{!T&&!P&&I(new Pe("Request stream has been aborted",t,m))}),i.pipe(m)}else m.end(i)})};var gu=j.hasStandardBrowserEnv?function(){return{write:function(s,r,n,o,i,p){let c=[];c.push(s+"="+encodeURIComponent(r)),g.isNumber(n)&&c.push("expires="+new Date(n).toGMTString()),g.isString(o)&&c.push("path="+o),g.isString(i)&&c.push("domain="+i),p===!0&&c.push("secure"),document.cookie=c.join("; ")},read:function(s){let r=document.cookie.match(new RegExp("(^|;\\s*)("+s+")=([^;]*)"));return r?decodeURIComponent(r[3]):null},remove:function(s){this.write(s,"",Date.now()-864e5)}}}():function(){return{write:function(){},read:function(){return null},remove:function(){}}}();var mu=j.hasStandardBrowserEnv?function(){let t=/(msie|trident)/i.test(navigator.userAgent),s=document.createElement("a"),r;function n(o){let i=o;return t&&(s.setAttribute("href",i),i=s.href),s.setAttribute("href",i),{href:s.href,protocol:s.protocol?s.protocol.replace(/:$/,""):"",host:s.host,search:s.search?s.search.replace(/^\?/,""):"",hash:s.hash?s.hash.replace(/^#/,""):"",hostname:s.hostname,port:s.port,pathname:s.pathname.charAt(0)==="/"?s.pathname:"/"+s.pathname}}return r=n(window.location.href),function(i){let p=g.isString(i)?n(i):i;return p.protocol===r.protocol&&p.host===r.host}}():function(){return function(){return!0}}();function yu(e,t){let s=0,r=ks(50,250);return n=>{let o=n.loaded,i=n.lengthComputable?n.total:void 0,p=o-s,c=r(p),a=o<=i;s=o;let u={loaded:o,total:i,progress:i?o/i:void 0,bytes:p,rate:c||void 0,estimated:c&&i&&a?(i-o)/c:void 0,event:n};u[t?"download":"upload"]=!0,e(u)}}var Fw=typeof XMLHttpRequest<"u",Ru=Fw&&function(e){return new Promise(function(s,r){let n=e.data,o=$.from(e.headers).normalize(),i=e.responseType,p;function c(){e.cancelToken&&e.cancelToken.unsubscribe(p),e.signal&&e.signal.removeEventListener("abort",p)}let a;if(g.isFormData(n)){if(j.hasStandardBrowserEnv||j.hasStandardBrowserWebWorkerEnv)o.setContentType(!1);else if((a=o.getContentType())!==!1){let[m,...f]=a?a.split(";").map(k=>k.trim()).filter(Boolean):[];o.setContentType([m||"multipart/form-data",...f].join("; "))}}let u=new XMLHttpRequest;if(e.auth){let m=e.auth.username||"",f=e.auth.password?unescape(encodeURIComponent(e.auth.password)):"";o.set("Authorization","Basic "+btoa(m+":"+f))}let y=Xe(e.baseURL,e.url);u.open(e.method.toUpperCase(),Ye(y,e.params,e.paramsSerializer),!0),u.timeout=e.timeout;function R(){if(!u)return;let m=$.from("getAllResponseHeaders"in u&&u.getAllResponseHeaders()),k={data:!i||i==="text"||i==="json"?u.responseText:u.response,status:u.status,statusText:u.statusText,headers:m,config:e,request:u};Fe(function(U){s(U),c()},function(U){r(U),c()},k),u=null}if("onloadend"in u?u.onloadend=R:u.onreadystatechange=function(){!u||u.readyState!==4||u.status===0&&!(u.responseURL&&u.responseURL.indexOf("file:")===0)||setTimeout(R)},u.onabort=function(){u&&(r(new v("Request aborted",v.ECONNABORTED,e,u)),u=null)},u.onerror=function(){r(new v("Network Error",v.ERR_NETWORK,e,u)),u=null},u.ontimeout=function(){let f=e.timeout?"timeout of "+e.timeout+"ms exceeded":"timeout exceeded",k=e.transitional||bt;e.timeoutErrorMessage&&(f=e.timeoutErrorMessage),r(new v(f,k.clarifyTimeoutError?v.ETIMEDOUT:v.ECONNABORTED,e,u)),u=null},j.hasStandardBrowserEnv){let m=mu(y)&&e.xsrfCookieName&&gu.read(e.xsrfCookieName);m&&o.set(e.xsrfHeaderName,m)}n===void 0&&o.setContentType(null),"setRequestHeader"in u&&g.forEach(o.toJSON(),function(f,k){u.setRequestHeader(k,f)}),g.isUndefined(e.withCredentials)||(u.withCredentials=!!e.withCredentials),i&&i!=="json"&&(u.responseType=e.responseType),typeof e.onDownloadProgress=="function"&&u.addEventListener("progress",yu(e.onDownloadProgress,!0)),typeof e.onUploadProgress=="function"&&u.upload&&u.upload.addEventListener("progress",yu(e.onUploadProgress)),(e.cancelToken||e.signal)&&(p=m=>{u&&(r(!m||m.type?new Pe(null,e,u):m),u.abort(),u=null)},e.cancelToken&&e.cancelToken.subscribe(p),e.signal&&(e.signal.aborted?p():e.signal.addEventListener("abort",p)));let x=jt(y);if(x&&j.protocols.indexOf(x)===-1){r(new v("Unsupported protocol "+x+":",v.ERR_BAD_REQUEST,e));return}u.send(n||null)})};var ua={http:lu,xhr:Ru};g.forEach(ua,(e,t)=>{if(e){try{Object.defineProperty(e,"name",{value:t})}catch{}Object.defineProperty(e,"adapterName",{value:t})}});var hu=e=>`- ${e}`,Ow=e=>g.isFunction(e)||e===null||e===!1,Ts={getAdapter:e=>{e=g.isArray(e)?e:[e];let{length:t}=e,s,r,n={};for(let o=0;o<t;o++){s=e[o];let i;if(r=s,!Ow(s)&&(r=ua[(i=String(s)).toLowerCase()],r===void 0))throw new v(`Unknown adapter '${i}'`);if(r)break;n[i||"#"+o]=r}if(!r){let o=Object.entries(n).map(([p,c])=>`adapter ${p} `+(c===!1?"is not supported by the environment":"is not available in the build")),i=t?o.length>1?`since :
`+o.map(hu).join(`
`):" "+hu(o[0]):"as no adapter specified";throw new v("There is no suitable adapter to dispatch the request "+i,"ERR_NOT_SUPPORT")}return r},adapters:ua};function da(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new Pe(null,e)}function ws(e){return da(e),e.headers=$.from(e.headers),e.data=Ot.call(e,e.transformRequest),["post","put","patch"].indexOf(e.method)!==-1&&e.headers.setContentType("application/x-www-form-urlencoded",!1),Ts.getAdapter(e.adapter||xt.adapter)(e).then(function(r){return da(e),r.data=Ot.call(e,e.transformResponse,r),r.headers=$.from(r.headers),r},function(r){return Mt(r)||(da(e),r&&r.response&&(r.response.data=Ot.call(e,e.transformResponse,r.response),r.response.headers=$.from(r.response.headers))),Promise.reject(r)})}var qu=e=>e instanceof $?e.toJSON():e;function Se(e,t){t=t||{};let s={};function r(a,u,y){return g.isPlainObject(a)&&g.isPlainObject(u)?g.merge.call({caseless:y},a,u):g.isPlainObject(u)?g.merge({},u):g.isArray(u)?u.slice():u}function n(a,u,y){if(g.isUndefined(u)){if(!g.isUndefined(a))return r(void 0,a,y)}else return r(a,u,y)}function o(a,u){if(!g.isUndefined(u))return r(void 0,u)}function i(a,u){if(g.isUndefined(u)){if(!g.isUndefined(a))return r(void 0,a)}else return r(void 0,u)}function p(a,u,y){if(y in t)return r(a,u);if(y in e)return r(void 0,a)}let c={url:o,method:o,data:o,baseURL:i,transformRequest:i,transformResponse:i,paramsSerializer:i,timeout:i,timeoutMessage:i,withCredentials:i,adapter:i,responseType:i,xsrfCookieName:i,xsrfHeaderName:i,onUploadProgress:i,onDownloadProgress:i,decompress:i,maxContentLength:i,maxBodyLength:i,beforeRedirect:i,transport:i,httpAgent:i,httpsAgent:i,cancelToken:i,socketPath:i,responseEncoding:i,validateStatus:p,headers:(a,u)=>n(qu(a),qu(u),!0)};return g.forEach(Object.keys(Object.assign({},e,t)),function(u){let y=c[u]||n,R=y(e[u],t[u],u);g.isUndefined(R)&&y!==p||(s[u]=R)}),s}var la={};["object","boolean","number","function","string","symbol"].forEach((e,t)=>{la[e]=function(r){return typeof r===e||"a"+(t<1?"n ":" ")+e}});var fu={};la.transitional=function(t,s,r){function n(o,i){return"[Axios v"+st+"] Transitional option '"+o+"'"+i+(r?". "+r:"")}return(o,i,p)=>{if(t===!1)throw new v(n(i," has been removed"+(s?" in "+s:"")),v.ERR_DEPRECATED);return s&&!fu[i]&&(fu[i]=!0,console.warn(n(i," has been deprecated since v"+s+" and will be removed in the near future"))),t?t(o,i,p):!0}};function Mw(e,t,s){if(typeof e!="object")throw new v("options must be an object",v.ERR_BAD_OPTION_VALUE);let r=Object.keys(e),n=r.length;for(;n-- >0;){let o=r[n],i=t[o];if(i){let p=e[o],c=p===void 0||i(p,o,e);if(c!==!0)throw new v("option "+o+" must be "+c,v.ERR_BAD_OPTION_VALUE);continue}if(s!==!0)throw new v("Unknown option "+o,v.ERR_BAD_OPTION)}}var Bs={assertOptions:Mw,validators:la};var Ne=Bs.validators,Tt=class{constructor(t){this.defaults=t,this.interceptors={request:new Mr,response:new Mr}}request(t,s){typeof t=="string"?(s=s||{},s.url=t):s=t||{},s=Se(this.defaults,s);let{transitional:r,paramsSerializer:n,headers:o}=s;r!==void 0&&Bs.assertOptions(r,{silentJSONParsing:Ne.transitional(Ne.boolean),forcedJSONParsing:Ne.transitional(Ne.boolean),clarifyTimeoutError:Ne.transitional(Ne.boolean)},!1),n!=null&&(g.isFunction(n)?s.paramsSerializer={serialize:n}:Bs.assertOptions(n,{encode:Ne.function,serialize:Ne.function},!0)),s.method=(s.method||this.defaults.method||"get").toLowerCase();let i=o&&g.merge(o.common,o[s.method]);o&&g.forEach(["delete","get","head","post","put","patch","common"],m=>{delete o[m]}),s.headers=$.concat(i,o);let p=[],c=!0;this.interceptors.request.forEach(function(f){typeof f.runWhen=="function"&&f.runWhen(s)===!1||(c=c&&f.synchronous,p.unshift(f.fulfilled,f.rejected))});let a=[];this.interceptors.response.forEach(function(f){a.push(f.fulfilled,f.rejected)});let u,y=0,R;if(!c){let m=[ws.bind(this),void 0];for(m.unshift.apply(m,p),m.push.apply(m,a),R=m.length,u=Promise.resolve(s);y<R;)u=u.then(m[y++],m[y++]);return u}R=p.length;let x=s;for(y=0;y<R;){let m=p[y++],f=p[y++];try{x=m(x)}catch(k){f.call(this,k);break}}try{u=ws.call(this,x)}catch(m){return Promise.reject(m)}for(y=0,R=a.length;y<R;)u=u.then(a[y++],a[y++]);return u}getUri(t){t=Se(this.defaults,t);let s=Xe(t.baseURL,t.url);return Ye(s,t.params,t.paramsSerializer)}};g.forEach(["delete","get","head","options"],function(t){Tt.prototype[t]=function(s,r){return this.request(Se(r||{},{method:t,url:s,data:(r||{}).data}))}});g.forEach(["post","put","patch"],function(t){function s(r){return function(o,i,p){return this.request(Se(p||{},{method:t,headers:r?{"Content-Type":"multipart/form-data"}:{},url:o,data:i}))}}Tt.prototype[t]=s(),Tt.prototype[t+"Form"]=s(!0)});var zt=Tt;var Jt=class{constructor(t){if(typeof t!="function")throw new TypeError("executor must be a function.");let s;this.promise=new Promise(function(o){s=o});let r=this;this.promise.then(n=>{if(!r._listeners)return;let o=r._listeners.length;for(;o-- >0;)r._listeners[o](n);r._listeners=null}),this.promise.then=n=>{let o,i=new Promise(p=>{r.subscribe(p),o=p}).then(n);return i.cancel=function(){r.unsubscribe(o)},i},t(function(o,i,p){r.reason||(r.reason=new Pe(o,i,p),s(r.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(t){if(this.reason){t(this.reason);return}this._listeners?this._listeners.push(t):this._listeners=[t]}unsubscribe(t){if(!this._listeners)return;let s=this._listeners.indexOf(t);s!==-1&&this._listeners.splice(s,1)}static source(){let t;return{token:new Jt(function(n){t=n}),cancel:t}}},bu=Jt;function ga(e){return function(s){return e.apply(null,s)}}function ma(e){return g.isObject(e)&&e.isAxiosError===!0}var ya={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(ya).forEach(([e,t])=>{ya[t]=e});var xu=ya;function Iu(e){let t=new zt(e),s=Lt(zt.prototype.request,t);return g.extend(s,zt.prototype,t,{allOwnKeys:!0}),g.extend(s,t,null,{allOwnKeys:!0}),s.create=function(n){return Iu(Se(e,n))},s}var K=Iu(xt);K.Axios=zt;K.CanceledError=Pe;K.CancelToken=bu;K.isCancel=Mt;K.VERSION=st;K.toFormData=Qe;K.AxiosError=v;K.Cancel=K.CanceledError;K.all=function(t){return Promise.all(t)};K.spread=ga;K.isAxiosError=ma;K.mergeConfig=Se;K.AxiosHeaders=$;K.formToJSON=e=>qs(g.isHTMLForm(e)?new FormData(e):e);K.getAdapter=Ts.getAdapter;K.HttpStatusCode=xu;K.default=K;var H=K;var{Axios:Nw,AxiosError:Vw,CanceledError:Kw,isCancel:jw,CancelToken:$w,VERSION:zw,all:Jw,Cancel:Yw,isAxiosError:Xw,spread:Zw,toFormData:eB,AxiosHeaders:tB,HttpStatusCode:sB,formToJSON:rB,getAdapter:aB,mergeConfig:nB}=H;var ba={};he(ba,{DEFAULT_OPTIONS:()=>Uu,default:()=>Ae,exponentialDelay:()=>Bu,isIdempotentRequestError:()=>qa,isNetworkError:()=>ha,isNetworkOrIdempotentRequestError:()=>fa,isRetryableError:()=>Us,isSafeRequestError:()=>wu,linearDelay:()=>Cu,namespace:()=>Cs,retryAfter:()=>Es});var Au=Q(ku(),1),Cs="axios-retry";function ha(e){let t=["ERR_CANCELED","ECONNABORTED"];return e.response||!e.code||t.includes(e.code)?!1:(0,Au.default)(e)}var Tu=["get","head","options"],iB=Tu.concat(["put","delete"]);function Us(e){return e.code!=="ECONNABORTED"&&(!e.response||e.response.status===429||e.response.status>=500&&e.response.status<=599)}function wu(e){return e.config?.method?Us(e)&&Tu.indexOf(e.config.method)!==-1:!1}function qa(e){return e.config?.method?Us(e)&&iB.indexOf(e.config.method)!==-1:!1}function fa(e){return ha(e)||qa(e)}function Es(e=void 0){let t=e?.response?.headers["retry-after"];if(!t)return 0;let s=(Number(t)||0)*1e3;return s===0&&(s=(new Date(t).valueOf()||0)-Date.now()),Math.max(0,s)}function pB(e=0,t=void 0){return Math.max(0,Es(t))}function Bu(e=0,t=void 0,s=100){let r=2**e*s,n=Math.max(r,Es(t)),o=n*.2*Math.random();return n+o}function Cu(e=100){return(t=0,s=void 0)=>{let r=t*e;return Math.max(r,Es(s))}}var Uu={retries:3,retryCondition:fa,retryDelay:pB,shouldResetTimeout:!1,onRetry:()=>{},onMaxRetryTimesExceeded:()=>{},validateResponse:null};function cB(e,t){return{...Uu,...t,...e[Cs]}}function Pu(e,t,s=!1){let r=cB(e,t||{});return r.retryCount=r.retryCount||0,(!r.lastRequestTime||s)&&(r.lastRequestTime=Date.now()),e[Cs]=r,r}function uB(e,t){e.defaults.agent===t.agent&&delete t.agent,e.defaults.httpAgent===t.httpAgent&&delete t.httpAgent,e.defaults.httpsAgent===t.httpsAgent&&delete t.httpsAgent}async function dB(e,t){let{retries:s,retryCondition:r}=e,n=(e.retryCount||0)<s&&r(t);if(typeof n=="object")try{return await n!==!1}catch{return!1}return n}async function lB(e,t,s,r){t.retryCount+=1;let{retryDelay:n,shouldResetTimeout:o,onRetry:i}=t,p=n(t.retryCount,s);if(uB(e,r),!o&&r.timeout&&t.lastRequestTime){let c=Date.now()-t.lastRequestTime,a=r.timeout-c-p;if(a<=0)return Promise.reject(s);r.timeout=a}return r.transformRequest=[c=>c],await i(t.retryCount,s,r),r.signal?.aborted?Promise.resolve(e(r)):new Promise(c=>{let a=()=>{clearTimeout(u),c(e(r))},u=setTimeout(()=>{c(e(r)),r.signal?.removeEventListener&&r.signal.removeEventListener("abort",a)},p);r.signal?.addEventListener&&r.signal.addEventListener("abort",a,{once:!0})})}async function gB(e,t){e.retryCount>=e.retries&&await e.onMaxRetryTimesExceeded(t,e.retryCount)}var Ve=(e,t)=>{let s=e.interceptors.request.use(n=>(Pu(n,t,!0),n[Cs]?.validateResponse&&(n.validateStatus=()=>!1),n)),r=e.interceptors.response.use(null,async n=>{let{config:o}=n;if(!o)return Promise.reject(n);let i=Pu(o,t);return n.response&&i.validateResponse?.(n.response)?n.response:await dB(i,n)?lB(e,i,n,o):(await gB(i,n),Promise.reject(n))});return{requestInterceptorId:s,responseInterceptorId:r}};Ve.isNetworkError=ha;Ve.isSafeRequestError=wu;Ve.isIdempotentRequestError=qa;Ve.isNetworkOrIdempotentRequestError=fa;Ve.exponentialDelay=Bu;Ve.linearDelay=Cu;Ve.isRetryableError=Us;var Ae=Ve;var bn={};he(bn,{Client:()=>fn});var Ia=Q(xa()),Eu=Q(require("http")),Lu=Q(require("https")),Gu=100*1024*1024,Su=Gu,Wu=Gu,_u=Ia.isNode?new Eu.default.Agent({keepAlive:!0}):void 0,Du=Ia.isNode?new Lu.default.Agent({keepAlive:!0}):void 0;var be={};he(be,{getClientConfig:()=>TB});var Yt=Q(xa()),bB="https://api.botpress.cloud",xB=6e4,IB="BP_API_URL",vB="BP_BOT_ID",kB="BP_INTEGRATION_ID",PB="BP_WORKSPACE_ID",AB="BP_TOKEN";function TB(e){let t=wB(e),s={};t.workspaceId&&(s["x-workspace-id"]=t.workspaceId),t.botId&&(s["x-bot-id"]=t.botId),t.integrationId&&(s["x-integration-id"]=t.integrationId),t.token&&(s.Authorization=`Bearer ${t.token}`),s={...s,...t.headers};let r=t.apiUrl??bB,n=t.timeout??xB;return{apiUrl:r,timeout:n,withCredentials:Yt.isBrowser,headers:s}}function wB(e){return Yt.isBrowser?e:Yt.isNode?BB(e):e}function BB(e){let t={...e,apiUrl:e.apiUrl??process.env[IB],botId:e.botId??process.env[vB],integrationId:e.integrationId??process.env[kB],workspaceId:e.workspaceId??process.env[PB]},s=t.token??process.env[AB];return s&&(t.token=s),t}var B={};he(B,{AsyncCollection:()=>va});var va=class{constructor(t){this._list=t}async*[Symbol.asyncIterator](){let t;do{let{items:s,meta:r}=await this._list({nextToken:t});t=r.nextToken;for(let n of s)yield n}while(t)}async collect(t={}){let s=t.limit??Number.POSITIVE_INFINITY,r=[],n=0;for await(let o of this)if(r.push(o),n++,n>=s)break;return r}};var xe={};he(xe,{createAxios:()=>CB});var CB=e=>({baseURL:e.apiUrl,headers:e.headers,withCredentials:e.withCredentials,timeout:e.timeout,maxBodyLength:Su,maxContentLength:Wu,httpAgent:_u,httpsAgent:Du});var Ie={};he(Ie,{toApiError:()=>SB});var Qu=Q(require("crypto"));var UB={getRandomValues:e=>new Uint8Array(e.map(()=>Math.floor(Math.random()*256)))},ka=typeof window<"u"&&typeof window.document<"u"?window.crypto:Qu.default;ka.getRandomValues||(ka=UB);var G=class extends Error{constructor(s,r,n,o,i,p,c){super(o);this.code=s;this.description=r;this.type=n;this.message=o;this.error=i;this.id=p;this.metadata=c;this.id||(this.id=G.generateId())}isApiError=!0;format(){return`[${this.type}] ${this.message} (Error ID: ${this.id})`}toJSON(){return{id:this.id,code:this.code,type:this.type,message:this.message,metadata:this.metadata}}static generateId(){let s=this.getPrefix(),r=new Date().toISOString().replace(/[\-:TZ]/g,"").split(".")[0],n=4,o=Array.from(ka.getRandomValues(new Uint8Array(n))).map(i=>i.toString(16).padStart(2,"0")).join("").toUpperCase();return`${s}_${r}x${o}`}static getPrefix(){return typeof window<"u"&&typeof window.document<"u"?"err_bwsr":"err"}},EB=e=>typeof e=="object"&&!Array.isArray(e)&&e!==null,Fu=e=>e instanceof G||EB(e)&&e.isApiError===!0,Ke=class extends G{constructor(t,s,r,n){super(500,"An unknown error occurred","Unknown",t,s,r,n)}},Ls=class extends G{constructor(t,s,r,n){super(500,"An internal error occurred","Internal",t,s,r,n)}},Gs=class extends G{constructor(t,s,r,n){super(401,"The request requires to be authenticated.","Unauthorized",t,s,r,n)}},Ss=class extends G{constructor(t,s,r,n){super(403,"The requested action can't be peform by this resource.","Forbidden",t,s,r,n)}},Ws=class extends G{constructor(t,s,r,n){super(413,"The request payload is too large.","PayloadTooLarge",t,s,r,n)}},_s=class extends G{constructor(t,s,r,n){super(400,"The request payload is invalid.","InvalidPayload",t,s,r,n)}},Ds=class extends G{constructor(t,s,r,n){super(415,"The request is invalid because the content-type is not supported.","UnsupportedMediaType",t,s,r,n)}},Hs=class extends G{constructor(t,s,r,n){super(405,"The requested method does not exist.","MethodNotFound",t,s,r,n)}},Qs=class extends G{constructor(t,s,r,n){super(404,"The requested resource does not exist.","ResourceNotFound",t,s,r,n)}},Fs=class extends G{constructor(t,s,r,n){super(400,"The provided JSON schema is invalid.","InvalidJsonSchema",t,s,r,n)}},Os=class extends G{constructor(t,s,r,n){super(400,"The provided data doesn't respect the provided JSON schema.","InvalidDataFormat",t,s,r,n)}},Ms=class extends G{constructor(t,s,r,n){super(400,"The provided identifier is not valid. An identifier must start with a lowercase letter, be between 2 and 100 characters long and use only alphanumeric characters.","InvalidIdentifier",t,s,r,n)}},Ns=class extends G{constructor(t,s,r,n){super(409,"The resource is related with a different resource that the one referenced in the request. This is usually caused when providing two resource identifiers that aren't linked together.","RelationConflict",t,s,r,n)}},Vs=class extends G{constructor(t,s,r,n){super(409,"The resource cannot be deleted because it's referenced by another resource","ReferenceConstraint",t,s,r,n)}},Ks=class extends G{constructor(t,s,r,n){super(409,"The resource is current locked and cannot be operated on until the lock is released.","ResourceLockedConflict",t,s,r,n)}},js=class extends G{constructor(t,s,r,n){super(400,"The provided resource reference is missing. This is usually caused when providing an invalid id inside the payload of a request.","ReferenceNotFound",t,s,r,n)}},$s=class extends G{constructor(t,s,r,n){super(400,"The provided query is invalid. This is usually caused when providing an invalid parameter for querying a resource.","InvalidQuery",t,s,r,n)}},zs=class extends G{constructor(t,s,r,n){super(400,"An error happened during the execution of a runtime (bot or integration).","Runtime",t,s,r,n)}},Js=class extends G{constructor(t,s,r,n){super(409,"The record attempted to be created already exists.","AlreadyExists",t,s,r,n)}},Ys=class extends G{constructor(t,s,r,n){super(429,"The request has been rate limited.","RateLimited",t,s,r,n)}},Xs=class extends G{constructor(t,s,r,n){super(402,"A payment is required to perform this request.","PaymentRequired",t,s,r,n)}},Zs=class extends G{constructor(t,s,r,n){super(403,"The request exceeds the allowed quota. Quotas are a soft limit that can be increased.","QuotaExceeded",t,s,r,n)}},er=class extends G{constructor(t,s,r,n){super(413,"The request exceeds the allowed limit. Limits are a hard limit that cannot be increased.","LimitExceeded",t,s,r,n)}},tr=class extends G{constructor(t,s,r,n){super(400,"Request payload contains breaking changes which is not allowed for this resource without a version increment.","BreakingChanges",t,s,r,n)}},LB={Unknown:Ke,Internal:Ls,Unauthorized:Gs,Forbidden:Ss,PayloadTooLarge:Ws,InvalidPayload:_s,UnsupportedMediaType:Ds,MethodNotFound:Hs,ResourceNotFound:Qs,InvalidJsonSchema:Fs,InvalidDataFormat:Os,InvalidIdentifier:Ms,RelationConflict:Ns,ReferenceConstraint:Vs,ResourceLockedConflict:Ks,ReferenceNotFound:js,InvalidQuery:$s,Runtime:zs,AlreadyExists:Js,RateLimited:Ys,PaymentRequired:Xs,QuotaExceeded:Zs,LimitExceeded:er,BreakingChanges:tr},nt=e=>Fu(e)?e:e instanceof Error?new Ke(e.message,e):typeof e=="string"?new Ke(e):GB(e);function GB(e){if(typeof e=="object"&&"code"in e&&"type"in e&&"id"in e&&"message"in e&&typeof e.type=="string"&&typeof e.message=="string"){let t=LB[e.type];return t?new t(e.message,void 0,e.id||"UNKNOWN",e.metadata):new Ke(`An unclassified API error occurred: ${e.message} (Type: ${e.type}, Code: ${e.code})`)}return new Ke("An invalid error occurred: "+JSON.stringify(e))}var We=class extends Error{constructor(s,r,n){super(s);this.innerError=r;this.file=n;this.name="FileUploadError"}};var SB=e=>H.isAxiosError(e)&&e.response?.data?nt(e.response.data):nt(e);var Mu=Q(require("crypto"));var WB={getRandomValues:e=>new Uint8Array(e.map(()=>Math.floor(Math.random()*256)))},Pa=typeof window<"u"&&typeof window.document<"u"?window.crypto:Mu.default;Pa.getRandomValues||(Pa=WB);var S=class extends Error{constructor(s,r,n,o,i,p,c){super(o);this.code=s;this.description=r;this.type=n;this.message=o;this.error=i;this.id=p;this.metadata=c;this.id||(this.id=S.generateId())}isApiError=!0;format(){return`[${this.type}] ${this.message} (Error ID: ${this.id})`}toJSON(){return{id:this.id,code:this.code,type:this.type,message:this.message,metadata:this.metadata}}static generateId(){let s=this.getPrefix(),r=new Date().toISOString().replace(/[\-:TZ]/g,"").split(".")[0],n=4,o=Array.from(Pa.getRandomValues(new Uint8Array(n))).map(i=>i.toString(16).padStart(2,"0")).join("").toUpperCase();return`${s}_${r}x${o}`}static getPrefix(){return typeof window<"u"&&typeof window.document<"u"?"err_bwsr":"err"}},_B=e=>typeof e=="object"&&!Array.isArray(e)&&e!==null,DB=e=>e instanceof S||_B(e)&&e.isApiError===!0,ot=class extends S{constructor(t,s,r,n){super(500,"An unknown error occurred","Unknown",t,s,r,n)}},Aa=class extends S{constructor(t,s,r,n){super(500,"An internal error occurred","Internal",t,s,r,n)}},Ta=class extends S{constructor(t,s,r,n){super(401,"The request requires to be authenticated.","Unauthorized",t,s,r,n)}},wa=class extends S{constructor(t,s,r,n){super(403,"The requested action can't be peform by this resource.","Forbidden",t,s,r,n)}},Ba=class extends S{constructor(t,s,r,n){super(413,"The request payload is too large.","PayloadTooLarge",t,s,r,n)}},Ca=class extends S{constructor(t,s,r,n){super(400,"The request payload is invalid.","InvalidPayload",t,s,r,n)}},Ua=class extends S{constructor(t,s,r,n){super(415,"The request is invalid because the content-type is not supported.","UnsupportedMediaType",t,s,r,n)}},Ea=class extends S{constructor(t,s,r,n){super(405,"The requested method does not exist.","MethodNotFound",t,s,r,n)}},La=class extends S{constructor(t,s,r,n){super(404,"The requested resource does not exist.","ResourceNotFound",t,s,r,n)}},Ga=class extends S{constructor(t,s,r,n){super(400,"The provided JSON schema is invalid.","InvalidJsonSchema",t,s,r,n)}},Sa=class extends S{constructor(t,s,r,n){super(400,"The provided data doesn't respect the provided JSON schema.","InvalidDataFormat",t,s,r,n)}},Wa=class extends S{constructor(t,s,r,n){super(400,"The provided identifier is not valid. An identifier must start with a lowercase letter, be between 2 and 100 characters long and use only alphanumeric characters.","InvalidIdentifier",t,s,r,n)}},_a=class extends S{constructor(t,s,r,n){super(409,"The resource is related with a different resource that the one referenced in the request. This is usually caused when providing two resource identifiers that aren't linked together.","RelationConflict",t,s,r,n)}},Da=class extends S{constructor(t,s,r,n){super(409,"The resource cannot be deleted because it's referenced by another resource","ReferenceConstraint",t,s,r,n)}},Ha=class extends S{constructor(t,s,r,n){super(409,"The resource is current locked and cannot be operated on until the lock is released.","ResourceLockedConflict",t,s,r,n)}},Qa=class extends S{constructor(t,s,r,n){super(400,"The provided resource reference is missing. This is usually caused when providing an invalid id inside the payload of a request.","ReferenceNotFound",t,s,r,n)}},Fa=class extends S{constructor(t,s,r,n){super(400,"The provided query is invalid. This is usually caused when providing an invalid parameter for querying a resource.","InvalidQuery",t,s,r,n)}},Oa=class extends S{constructor(t,s,r,n){super(400,"An error happened during the execution of a runtime (bot or integration).","Runtime",t,s,r,n)}},Ma=class extends S{constructor(t,s,r,n){super(409,"The record attempted to be created already exists.","AlreadyExists",t,s,r,n)}},Na=class extends S{constructor(t,s,r,n){super(429,"The request has been rate limited.","RateLimited",t,s,r,n)}},Va=class extends S{constructor(t,s,r,n){super(402,"A payment is required to perform this request.","PaymentRequired",t,s,r,n)}},Ka=class extends S{constructor(t,s,r,n){super(403,"The request exceeds the allowed quota. Quotas are a soft limit that can be increased.","QuotaExceeded",t,s,r,n)}},ja=class extends S{constructor(t,s,r,n){super(413,"The request exceeds the allowed limit. Limits are a hard limit that cannot be increased.","LimitExceeded",t,s,r,n)}},$a=class extends S{constructor(t,s,r,n){super(400,"Request payload contains breaking changes which is not allowed for this resource without a version increment.","BreakingChanges",t,s,r,n)}},HB={Unknown:ot,Internal:Aa,Unauthorized:Ta,Forbidden:wa,PayloadTooLarge:Ba,InvalidPayload:Ca,UnsupportedMediaType:Ua,MethodNotFound:Ea,ResourceNotFound:La,InvalidJsonSchema:Ga,InvalidDataFormat:Sa,InvalidIdentifier:Wa,RelationConflict:_a,ReferenceConstraint:Da,ResourceLockedConflict:Ha,ReferenceNotFound:Qa,InvalidQuery:Fa,Runtime:Oa,AlreadyExists:Ma,RateLimited:Na,PaymentRequired:Va,QuotaExceeded:Ka,LimitExceeded:ja,BreakingChanges:$a},za=e=>DB(e)?e:e instanceof Error?new ot(e.message,e):typeof e=="string"?new ot(e):QB(e);function QB(e){if(typeof e=="object"&&"code"in e&&"type"in e&&"id"in e&&"message"in e&&typeof e.type=="string"&&typeof e.message=="string"){let t=HB[e.type];return t?new t(e.message,void 0,e.id||"UNKNOWN",e.metadata):new ot(`An unclassified API error occurred: ${e.message} (Type: ${e.type}, Code: ${e.code})`)}return new ot("An invalid error occurred: "+JSON.stringify(e))}var Jd=Q(Et()),dU=e=>e[1]!==void 0,A=e=>{let{method:t,path:s,query:r,headers:n,body:o}=e,i=Object.entries(n).filter(dU),p=Object.fromEntries(i),c=Jd.default.stringify(r,{encode:!0,arrayFormat:"repeat",allowDots:!0}),a=c?[s,c].join("?"):s,u=["put","post","delete","patch"].includes(t.toLowerCase())?o:void 0;return{method:t,url:a,headers:p,data:u}};var Yd=e=>({path:"/v1/chat/conversations",headers:{},query:{},params:{},body:{channel:e.channel,tags:e.tags,integrationName:e.integrationName}});var Zd=e=>({path:`/v1/chat/conversations/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var tl=e=>({path:"/v1/chat/conversations",headers:{},query:{nextToken:e.nextToken,sortField:e.sortField,sortDirection:e.sortDirection,tags:e.tags,participantIds:e.participantIds,integrationName:e.integrationName,channel:e.channel},params:{},body:{}});var rl=e=>({path:"/v1/chat/conversations/get-or-create",headers:{},query:{},params:{},body:{channel:e.channel,tags:e.tags,integrationName:e.integrationName,discriminateByTags:e.discriminateByTags}});var nl=e=>({path:`/v1/chat/conversations/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{currentTaskId:e.currentTaskId,tags:e.tags}});var il=e=>({path:`/v1/chat/conversations/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var cl=e=>({path:`/v1/chat/conversations/${encodeURIComponent(e.id)}/participants`,headers:{},query:{nextToken:e.nextToken},params:{id:e.id},body:{}});var dl=e=>({path:`/v1/chat/conversations/${encodeURIComponent(e.id)}/participants`,headers:{},query:{},params:{id:e.id},body:{userId:e.userId}});var gl=e=>({path:`/v1/chat/conversations/${encodeURIComponent(e.id)}/participants/${encodeURIComponent(e.userId)}`,headers:{},query:{},params:{id:e.id,userId:e.userId},body:{}});var yl=e=>({path:`/v1/chat/conversations/${encodeURIComponent(e.id)}/participants/${encodeURIComponent(e.userId)}`,headers:{},query:{},params:{id:e.id,userId:e.userId},body:{}});var hl=e=>({path:"/v1/chat/events",headers:{},query:{},params:{},body:{type:e.type,payload:e.payload,schedule:e.schedule,conversationId:e.conversationId,userId:e.userId,messageId:e.messageId}});var fl=e=>({path:`/v1/chat/events/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var xl=e=>({path:"/v1/chat/events",headers:{},query:{nextToken:e.nextToken,type:e.type,conversationId:e.conversationId,userId:e.userId,messageId:e.messageId,status:e.status},params:{},body:{}});var vl=e=>({path:"/v1/chat/messages",headers:{},query:{},params:{},body:{payload:e.payload,userId:e.userId,conversationId:e.conversationId,type:e.type,tags:e.tags,schedule:e.schedule}});var Pl=e=>({path:"/v1/chat/messages/get-or-create",headers:{},query:{},params:{},body:{payload:e.payload,userId:e.userId,conversationId:e.conversationId,type:e.type,tags:e.tags,schedule:e.schedule,discriminateByTags:e.discriminateByTags}});var Tl=e=>({path:`/v1/chat/messages/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var Bl=e=>({path:`/v1/chat/messages/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{tags:e.tags,payload:e.payload}});var Ul=e=>({path:"/v1/chat/messages",headers:{},query:{nextToken:e.nextToken,conversationId:e.conversationId,tags:e.tags},params:{},body:{}});var Ll=e=>({path:`/v1/chat/messages/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var Sl=e=>({path:"/v1/chat/users",headers:{},query:{},params:{},body:{tags:e.tags,integrationName:e.integrationName,name:e.name,pictureUrl:e.pictureUrl}});var _l=e=>({path:`/v1/chat/users/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var Hl=e=>({path:"/v1/chat/users",headers:{},query:{nextToken:e.nextToken,conversationId:e.conversationId,tags:e.tags},params:{},body:{}});var Fl=e=>({path:"/v1/chat/users/get-or-create",headers:{},query:{},params:{},body:{tags:e.tags,integrationName:e.integrationName,name:e.name,pictureUrl:e.pictureUrl,discriminateByTags:e.discriminateByTags}});var Ml=e=>({path:`/v1/chat/users/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{tags:e.tags,name:e.name,pictureUrl:e.pictureUrl}});var Vl=e=>({path:`/v1/chat/users/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var jl=e=>({path:`/v1/chat/states/${encodeURIComponent(e.type)}/${encodeURIComponent(e.id)}/${encodeURIComponent(e.name)}/expiry`,headers:{},query:{},params:{type:e.type,id:e.id,name:e.name},body:{expiry:e.expiry}});var zl=e=>({path:`/v1/chat/states/${encodeURIComponent(e.type)}/${encodeURIComponent(e.id)}/${encodeURIComponent(e.name)}`,headers:{},query:{},params:{type:e.type,id:e.id,name:e.name},body:{}});var Yl=e=>({path:`/v1/chat/states/${encodeURIComponent(e.type)}/${encodeURIComponent(e.id)}/${encodeURIComponent(e.name)}`,headers:{},query:{},params:{type:e.type,id:e.id,name:e.name},body:{payload:e.payload,expiry:e.expiry}});var Zl=e=>({path:`/v1/chat/states/${encodeURIComponent(e.type)}/${encodeURIComponent(e.id)}/${encodeURIComponent(e.name)}/get-or-set`,headers:{},query:{},params:{type:e.type,id:e.id,name:e.name},body:{payload:e.payload,expiry:e.expiry}});var tg=e=>({path:`/v1/chat/states/${encodeURIComponent(e.type)}/${encodeURIComponent(e.id)}/${encodeURIComponent(e.name)}`,headers:{},query:{},params:{type:e.type,id:e.id,name:e.name},body:{payload:e.payload}});var rg=e=>({path:"/v1/chat/actions",headers:{},query:{},params:{},body:{type:e.type,input:e.input}});var ng=e=>({path:"/v1/chat/integrations/configure",headers:{},query:{},params:{},body:{identifier:e.identifier,scheduleRegisterCall:e.scheduleRegisterCall,sandboxIdentifiers:e.sandboxIdentifiers}});var ig=e=>({path:`/v1/chat/tasks/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var cg=e=>({path:"/v1/chat/tasks",headers:{},query:{},params:{},body:{title:e.title,description:e.description,type:e.type,data:e.data,parentTaskId:e.parentTaskId,conversationId:e.conversationId,userId:e.userId,timeoutAt:e.timeoutAt,tags:e.tags}});var dg=e=>({path:`/v1/chat/tasks/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{title:e.title,description:e.description,data:e.data,timeoutAt:e.timeoutAt,status:e.status,tags:e.tags}});var gg=e=>({path:`/v1/chat/tasks/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var yg=e=>({path:"/v1/chat/tasks",headers:{},query:{nextToken:e.nextToken,tags:e.tags,conversationId:e.conversationId,userId:e.userId,parentTaskId:e.parentTaskId,status:e.status,type:e.type},params:{},body:{}});var hg=e=>({path:"/v1/chat/workflows",headers:{},query:{},params:{},body:{name:e.name,input:e.input,parentWorkflowId:e.parentWorkflowId,conversationId:e.conversationId,userId:e.userId,timeoutAt:e.timeoutAt,tags:e.tags,status:e.status,eventId:e.eventId}});var fg=e=>({path:`/v1/chat/workflows/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var xg=e=>({path:`/v1/chat/workflows/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{output:e.output,timeoutAt:e.timeoutAt,status:e.status,failureReason:e.failureReason,tags:e.tags,userId:e.userId,eventId:e.eventId}});var vg=e=>({path:`/v1/chat/workflows/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var Pg=e=>({path:"/v1/chat/workflows",headers:{},query:{nextToken:e.nextToken,tags:e.tags,conversationId:e.conversationId,userId:e.userId,parentWorkflowId:e.parentWorkflowId,statuses:e.statuses,name:e.name},params:{},body:{}});var Tg=e=>({path:"/v1/chat/workflows/get-or-create",headers:{},query:{},params:{},body:{name:e.name,input:e.input,parentWorkflowId:e.parentWorkflowId,conversationId:e.conversationId,userId:e.userId,timeoutAt:e.timeoutAt,tags:e.tags,status:e.status,eventId:e.eventId}});var Bg=e=>({path:`/v1/chat/tags/${encodeURIComponent(e.key)}/values`,headers:{},query:{nextToken:e.nextToken,type:e.type},params:{key:e.key},body:{}});var Ug=e=>({path:"/v1/chat/analytics",headers:{},query:{},params:{},body:{name:e.name,count:e.count}});var ur=class{constructor(t,s={}){this.axiosInstance=t;this.props=s}createConversation=async t=>{let{path:s,headers:r,query:n,body:o}=Yd(t),i=this.props.toAxiosRequest??A,p=this.props.toApiError??w,c=i({method:"post",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};getConversation=async t=>{let{path:s,headers:r,query:n,body:o}=Zd(t),i=this.props.toAxiosRequest??A,p=this.props.toApiError??w,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};listConversations=async t=>{let{path:s,headers:r,query:n,body:o}=tl(t),i=this.props.toAxiosRequest??A,p=this.props.toApiError??w,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};getOrCreateConversation=async t=>{let{path:s,headers:r,query:n,body:o}=rl(t),i=this.props.toAxiosRequest??A,p=this.props.toApiError??w,c=i({method:"post",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};updateConversation=async t=>{let{path:s,headers:r,query:n,body:o}=nl(t),i=this.props.toAxiosRequest??A,p=this.props.toApiError??w,c=i({method:"put",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};deleteConversation=async t=>{let{path:s,headers:r,query:n,body:o}=il(t),i=this.props.toAxiosRequest??A,p=this.props.toApiError??w,c=i({method:"delete",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};listParticipants=async t=>{let{path:s,headers:r,query:n,body:o}=cl(t),i=this.props.toAxiosRequest??A,p=this.props.toApiError??w,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};addParticipant=async t=>{let{path:s,headers:r,query:n,body:o}=dl(t),i=this.props.toAxiosRequest??A,p=this.props.toApiError??w,c=i({method:"post",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};getParticipant=async t=>{let{path:s,headers:r,query:n,body:o}=gl(t),i=this.props.toAxiosRequest??A,p=this.props.toApiError??w,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};removeParticipant=async t=>{let{path:s,headers:r,query:n,body:o}=yl(t),i=this.props.toAxiosRequest??A,p=this.props.toApiError??w,c=i({method:"delete",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};createEvent=async t=>{let{path:s,headers:r,query:n,body:o}=hl(t),i=this.props.toAxiosRequest??A,p=this.props.toApiError??w,c=i({method:"post",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};getEvent=async t=>{let{path:s,headers:r,query:n,body:o}=fl(t),i=this.props.toAxiosRequest??A,p=this.props.toApiError??w,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};listEvents=async t=>{let{path:s,headers:r,query:n,body:o}=xl(t),i=this.props.toAxiosRequest??A,p=this.props.toApiError??w,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};createMessage=async t=>{let{path:s,headers:r,query:n,body:o}=vl(t),i=this.props.toAxiosRequest??A,p=this.props.toApiError??w,c=i({method:"post",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};getOrCreateMessage=async t=>{let{path:s,headers:r,query:n,body:o}=Pl(t),i=this.props.toAxiosRequest??A,p=this.props.toApiError??w,c=i({method:"post",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};getMessage=async t=>{let{path:s,headers:r,query:n,body:o}=Tl(t),i=this.props.toAxiosRequest??A,p=this.props.toApiError??w,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};updateMessage=async t=>{let{path:s,headers:r,query:n,body:o}=Bl(t),i=this.props.toAxiosRequest??A,p=this.props.toApiError??w,c=i({method:"put",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};listMessages=async t=>{let{path:s,headers:r,query:n,body:o}=Ul(t),i=this.props.toAxiosRequest??A,p=this.props.toApiError??w,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};deleteMessage=async t=>{let{path:s,headers:r,query:n,body:o}=Ll(t),i=this.props.toAxiosRequest??A,p=this.props.toApiError??w,c=i({method:"delete",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};createUser=async t=>{let{path:s,headers:r,query:n,body:o}=Sl(t),i=this.props.toAxiosRequest??A,p=this.props.toApiError??w,c=i({method:"post",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};getUser=async t=>{let{path:s,headers:r,query:n,body:o}=_l(t),i=this.props.toAxiosRequest??A,p=this.props.toApiError??w,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};listUsers=async t=>{let{path:s,headers:r,query:n,body:o}=Hl(t),i=this.props.toAxiosRequest??A,p=this.props.toApiError??w,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};getOrCreateUser=async t=>{let{path:s,headers:r,query:n,body:o}=Fl(t),i=this.props.toAxiosRequest??A,p=this.props.toApiError??w,c=i({method:"post",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};updateUser=async t=>{let{path:s,headers:r,query:n,body:o}=Ml(t),i=this.props.toAxiosRequest??A,p=this.props.toApiError??w,c=i({method:"put",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};deleteUser=async t=>{let{path:s,headers:r,query:n,body:o}=Vl(t),i=this.props.toAxiosRequest??A,p=this.props.toApiError??w,c=i({method:"delete",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};setStateExpiry=async t=>{let{path:s,headers:r,query:n,body:o}=jl(t),i=this.props.toAxiosRequest??A,p=this.props.toApiError??w,c=i({method:"post",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};getState=async t=>{let{path:s,headers:r,query:n,body:o}=zl(t),i=this.props.toAxiosRequest??A,p=this.props.toApiError??w,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};setState=async t=>{let{path:s,headers:r,query:n,body:o}=Yl(t),i=this.props.toAxiosRequest??A,p=this.props.toApiError??w,c=i({method:"post",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};getOrSetState=async t=>{let{path:s,headers:r,query:n,body:o}=Zl(t),i=this.props.toAxiosRequest??A,p=this.props.toApiError??w,c=i({method:"post",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};patchState=async t=>{let{path:s,headers:r,query:n,body:o}=tg(t),i=this.props.toAxiosRequest??A,p=this.props.toApiError??w,c=i({method:"patch",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};callAction=async t=>{let{path:s,headers:r,query:n,body:o}=rg(t),i=this.props.toAxiosRequest??A,p=this.props.toApiError??w,c=i({method:"post",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};configureIntegration=async t=>{let{path:s,headers:r,query:n,body:o}=ng(t),i=this.props.toAxiosRequest??A,p=this.props.toApiError??w,c=i({method:"post",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};getTask=async t=>{let{path:s,headers:r,query:n,body:o}=ig(t),i=this.props.toAxiosRequest??A,p=this.props.toApiError??w,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};createTask=async t=>{let{path:s,headers:r,query:n,body:o}=cg(t),i=this.props.toAxiosRequest??A,p=this.props.toApiError??w,c=i({method:"post",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};updateTask=async t=>{let{path:s,headers:r,query:n,body:o}=dg(t),i=this.props.toAxiosRequest??A,p=this.props.toApiError??w,c=i({method:"put",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};deleteTask=async t=>{let{path:s,headers:r,query:n,body:o}=gg(t),i=this.props.toAxiosRequest??A,p=this.props.toApiError??w,c=i({method:"delete",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};listTasks=async t=>{let{path:s,headers:r,query:n,body:o}=yg(t),i=this.props.toAxiosRequest??A,p=this.props.toApiError??w,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};createWorkflow=async t=>{let{path:s,headers:r,query:n,body:o}=hg(t),i=this.props.toAxiosRequest??A,p=this.props.toApiError??w,c=i({method:"post",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};getWorkflow=async t=>{let{path:s,headers:r,query:n,body:o}=fg(t),i=this.props.toAxiosRequest??A,p=this.props.toApiError??w,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};updateWorkflow=async t=>{let{path:s,headers:r,query:n,body:o}=xg(t),i=this.props.toAxiosRequest??A,p=this.props.toApiError??w,c=i({method:"put",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};deleteWorkflow=async t=>{let{path:s,headers:r,query:n,body:o}=vg(t),i=this.props.toAxiosRequest??A,p=this.props.toApiError??w,c=i({method:"delete",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};listWorkflows=async t=>{let{path:s,headers:r,query:n,body:o}=Pg(t),i=this.props.toAxiosRequest??A,p=this.props.toApiError??w,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};getOrCreateWorkflow=async t=>{let{path:s,headers:r,query:n,body:o}=Tg(t),i=this.props.toAxiosRequest??A,p=this.props.toApiError??w,c=i({method:"post",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};listTagValues=async t=>{let{path:s,headers:r,query:n,body:o}=Bg(t),i=this.props.toAxiosRequest??A,p=this.props.toApiError??w,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};trackAnalytics=async t=>{let{path:s,headers:r,query:n,body:o}=Ug(t),i=this.props.toAxiosRequest??A,p=this.props.toApiError??w,c=i({method:"post",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})}};function w(e){return H.isAxiosError(e)&&e.response?.data?za(e.response.data):za(e)}var fn=class extends ur{config;constructor(t){let s=be.getClientConfig(t),r=xe.createAxios(s),n=H.create(r);super(n,{toApiError:Ie.toApiError}),t.retry&&Ae(n,t.retry),this.config=s}get list(){return{conversations:t=>new B.AsyncCollection(({nextToken:s})=>this.listConversations({nextToken:s,...t}).then(r=>({...r,items:r.conversations}))),participants:t=>new B.AsyncCollection(({nextToken:s})=>this.listParticipants({nextToken:s,...t}).then(r=>({...r,items:r.participants}))),events:t=>new B.AsyncCollection(({nextToken:s})=>this.listEvents({nextToken:s,...t}).then(r=>({...r,items:r.events}))),messages:t=>new B.AsyncCollection(({nextToken:s})=>this.listMessages({nextToken:s,...t}).then(r=>({...r,items:r.messages}))),users:t=>new B.AsyncCollection(({nextToken:s})=>this.listUsers({nextToken:s,...t}).then(r=>({...r,items:r.users}))),tasks:t=>new B.AsyncCollection(({nextToken:s})=>this.listTasks({nextToken:s,...t}).then(r=>({...r,items:r.tasks})))}}};var jn={};he(jn,{Client:()=>Kn});var Lg=Q(require("crypto"));var gU={getRandomValues:e=>new Uint8Array(e.map(()=>Math.floor(Math.random()*256)))},xn=typeof window<"u"&&typeof window.document<"u"?window.crypto:Lg.default;xn.getRandomValues||(xn=gU);var W=class extends Error{constructor(s,r,n,o,i,p,c){super(o);this.code=s;this.description=r;this.type=n;this.message=o;this.error=i;this.id=p;this.metadata=c;this.id||(this.id=W.generateId())}isApiError=!0;format(){return`[${this.type}] ${this.message} (Error ID: ${this.id})`}toJSON(){return{id:this.id,code:this.code,type:this.type,message:this.message,metadata:this.metadata}}static generateId(){let s=this.getPrefix(),r=new Date().toISOString().replace(/[\-:TZ]/g,"").split(".")[0],n=4,o=Array.from(xn.getRandomValues(new Uint8Array(n))).map(i=>i.toString(16).padStart(2,"0")).join("").toUpperCase();return`${s}_${r}x${o}`}static getPrefix(){return typeof window<"u"&&typeof window.document<"u"?"err_bwsr":"err"}},mU=e=>typeof e=="object"&&!Array.isArray(e)&&e!==null,yU=e=>e instanceof W||mU(e)&&e.isApiError===!0,pt=class extends W{constructor(t,s,r,n){super(500,"An unknown error occurred","Unknown",t,s,r,n)}},In=class extends W{constructor(t,s,r,n){super(500,"An internal error occurred","Internal",t,s,r,n)}},vn=class extends W{constructor(t,s,r,n){super(401,"The request requires to be authenticated.","Unauthorized",t,s,r,n)}},kn=class extends W{constructor(t,s,r,n){super(403,"The requested action can't be peform by this resource.","Forbidden",t,s,r,n)}},Pn=class extends W{constructor(t,s,r,n){super(413,"The request payload is too large.","PayloadTooLarge",t,s,r,n)}},An=class extends W{constructor(t,s,r,n){super(400,"The request payload is invalid.","InvalidPayload",t,s,r,n)}},Tn=class extends W{constructor(t,s,r,n){super(415,"The request is invalid because the content-type is not supported.","UnsupportedMediaType",t,s,r,n)}},wn=class extends W{constructor(t,s,r,n){super(405,"The requested method does not exist.","MethodNotFound",t,s,r,n)}},Bn=class extends W{constructor(t,s,r,n){super(404,"The requested resource does not exist.","ResourceNotFound",t,s,r,n)}},Cn=class extends W{constructor(t,s,r,n){super(400,"The provided JSON schema is invalid.","InvalidJsonSchema",t,s,r,n)}},Un=class extends W{constructor(t,s,r,n){super(400,"The provided data doesn't respect the provided JSON schema.","InvalidDataFormat",t,s,r,n)}},En=class extends W{constructor(t,s,r,n){super(400,"The provided identifier is not valid. An identifier must start with a lowercase letter, be between 2 and 100 characters long and use only alphanumeric characters.","InvalidIdentifier",t,s,r,n)}},Ln=class extends W{constructor(t,s,r,n){super(409,"The resource is related with a different resource that the one referenced in the request. This is usually caused when providing two resource identifiers that aren't linked together.","RelationConflict",t,s,r,n)}},Gn=class extends W{constructor(t,s,r,n){super(409,"The resource cannot be deleted because it's referenced by another resource","ReferenceConstraint",t,s,r,n)}},Sn=class extends W{constructor(t,s,r,n){super(409,"The resource is current locked and cannot be operated on until the lock is released.","ResourceLockedConflict",t,s,r,n)}},Wn=class extends W{constructor(t,s,r,n){super(400,"The provided resource reference is missing. This is usually caused when providing an invalid id inside the payload of a request.","ReferenceNotFound",t,s,r,n)}},_n=class extends W{constructor(t,s,r,n){super(400,"The provided query is invalid. This is usually caused when providing an invalid parameter for querying a resource.","InvalidQuery",t,s,r,n)}},Dn=class extends W{constructor(t,s,r,n){super(400,"An error happened during the execution of a runtime (bot or integration).","Runtime",t,s,r,n)}},Hn=class extends W{constructor(t,s,r,n){super(409,"The record attempted to be created already exists.","AlreadyExists",t,s,r,n)}},Qn=class extends W{constructor(t,s,r,n){super(429,"The request has been rate limited.","RateLimited",t,s,r,n)}},Fn=class extends W{constructor(t,s,r,n){super(402,"A payment is required to perform this request.","PaymentRequired",t,s,r,n)}},On=class extends W{constructor(t,s,r,n){super(403,"The request exceeds the allowed quota. Quotas are a soft limit that can be increased.","QuotaExceeded",t,s,r,n)}},Mn=class extends W{constructor(t,s,r,n){super(413,"The request exceeds the allowed limit. Limits are a hard limit that cannot be increased.","LimitExceeded",t,s,r,n)}},Nn=class extends W{constructor(t,s,r,n){super(400,"Request payload contains breaking changes which is not allowed for this resource without a version increment.","BreakingChanges",t,s,r,n)}},RU={Unknown:pt,Internal:In,Unauthorized:vn,Forbidden:kn,PayloadTooLarge:Pn,InvalidPayload:An,UnsupportedMediaType:Tn,MethodNotFound:wn,ResourceNotFound:Bn,InvalidJsonSchema:Cn,InvalidDataFormat:Un,InvalidIdentifier:En,RelationConflict:Ln,ReferenceConstraint:Gn,ResourceLockedConflict:Sn,ReferenceNotFound:Wn,InvalidQuery:_n,Runtime:Dn,AlreadyExists:Hn,RateLimited:Qn,PaymentRequired:Fn,QuotaExceeded:On,LimitExceeded:Mn,BreakingChanges:Nn},Vn=e=>yU(e)?e:e instanceof Error?new pt(e.message,e):typeof e=="string"?new pt(e):hU(e);function hU(e){if(typeof e=="object"&&"code"in e&&"type"in e&&"id"in e&&"message"in e&&typeof e.type=="string"&&typeof e.message=="string"){let t=RU[e.type];return t?new t(e.message,void 0,e.id||"UNKNOWN",e.metadata):new pt(`An unclassified API error occurred: ${e.message} (Type: ${e.type}, Code: ${e.code})`)}return new pt("An invalid error occurred: "+JSON.stringify(e))}var Gg=Q(Et()),qU=e=>e[1]!==void 0,h=e=>{let{method:t,path:s,query:r,headers:n,body:o}=e,i=Object.entries(n).filter(qU),p=Object.fromEntries(i),c=Gg.default.stringify(r,{encode:!0,arrayFormat:"repeat",allowDots:!0}),a=c?[s,c].join("?"):s,u=["put","post","delete","patch"].includes(t.toLowerCase())?o:void 0;return{method:t,url:a,headers:p,data:u}};var Sg=e=>({path:"/v1/admin/helper/vrl",headers:{},query:{},params:{},body:{data:e.data,script:e.script}});var _g=e=>({path:"/v1/admin/account/me",headers:{},query:{},params:{},body:{}});var Hg=e=>({path:"/v1/admin/account/me",headers:{},query:{},params:{},body:{displayName:e.displayName,profilePicture:e.profilePicture,refresh:e.refresh}});var Fg=e=>({path:"/v1/admin/account/pats",headers:{},query:{},params:{},body:{}});var Mg=e=>({path:"/v1/admin/account/pats",headers:{},query:{},params:{},body:{note:e.note}});var Vg=e=>({path:`/v1/admin/account/pats/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var jg=e=>({path:`/v1/admin/account/preferences/${encodeURIComponent(e.key)}`,headers:{},query:{},params:{key:e.key},body:{value:e.value}});var zg=e=>({path:`/v1/admin/account/preferences/${encodeURIComponent(e.key)}`,headers:{},query:{},params:{key:e.key},body:{}});var Yg=e=>({path:"/v1/admin/hub/integrations",headers:{},query:{nextToken:e.nextToken,limit:e.limit,name:e.name,version:e.version,interfaceId:e.interfaceId,interfaceName:e.interfaceName,installedByBotId:e.installedByBotId,verificationStatus:e.verificationStatus,search:e.search,sortBy:e.sortBy,direction:e.direction},params:{},body:{}});var Zg=e=>({path:`/v1/admin/hub/integrations/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var tm=e=>({path:`/v1/admin/hub/integrations/${encodeURIComponent(e.name)}/${encodeURIComponent(e.version)}`,headers:{},query:{},params:{name:e.name,version:e.version},body:{}});var rm=e=>({path:"/v1/admin/hub/plugins",headers:{},query:{nextToken:e.nextToken,name:e.name,version:e.version},params:{},body:{}});var nm=e=>({path:`/v1/admin/hub/plugins/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var im=e=>({path:`/v1/admin/hub/plugins/${encodeURIComponent(e.name)}/${encodeURIComponent(e.version)}`,headers:{},query:{},params:{name:e.name,version:e.version},body:{}});var cm=e=>({path:`/v1/admin/hub/plugins/${encodeURIComponent(e.id)}/code/${encodeURIComponent(e.platform)}`,headers:{},query:{},params:{id:e.id,platform:e.platform},body:{}});var dm=e=>({path:"/v1/admin/hub/interfaces",headers:{},query:{nextToken:e.nextToken,name:e.name,version:e.version},params:{},body:{}});var gm=e=>({path:`/v1/admin/hub/interfaces/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var ym=e=>({path:`/v1/admin/hub/interfaces/${encodeURIComponent(e.name)}/${encodeURIComponent(e.version)}`,headers:{},query:{},params:{name:e.name,version:e.version},body:{}});var hm=e=>({path:"/v1/admin/bots",headers:{},query:{},params:{},body:{states:e.states,events:e.events,recurringEvents:e.recurringEvents,subscriptions:e.subscriptions,actions:e.actions,configuration:e.configuration,user:e.user,conversation:e.conversation,message:e.message,tags:e.tags,code:e.code,name:e.name,medias:e.medias,url:e.url,dev:e.dev}});var fm=e=>({path:`/v1/admin/bots/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{url:e.url,authentication:e.authentication,configuration:e.configuration,tags:e.tags,blocked:e.blocked,alwaysAlive:e.alwaysAlive,user:e.user,message:e.message,conversation:e.conversation,events:e.events,actions:e.actions,states:e.states,recurringEvents:e.recurringEvents,integrations:e.integrations,plugins:e.plugins,subscriptions:e.subscriptions,code:e.code,name:e.name,medias:e.medias,layers:e.layers}});var xm=e=>({path:`/v1/admin/bots/${encodeURIComponent(e.id)}/transfer`,headers:{},query:{},params:{id:e.id},body:{targetWorkspaceId:e.targetWorkspaceId}});var vm=e=>({path:"/v1/admin/bots",headers:{},query:{dev:e.dev,tags:e.tags,nextToken:e.nextToken,sortField:e.sortField,sortDirection:e.sortDirection},params:{},body:{}});var Pm=e=>({path:`/v1/admin/bots/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var Tm=e=>({path:`/v1/admin/bots/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var Bm=e=>({path:`/v1/admin/bots/${encodeURIComponent(e.id)}/logs`,headers:{},query:{timeStart:e.timeStart,timeEnd:e.timeEnd,level:e.level,userId:e.userId,workflowId:e.workflowId,conversationId:e.conversationId,messageContains:e.messageContains,nextToken:e.nextToken},params:{id:e.id},body:{}});var Um=e=>({path:`/v1/admin/bots/${encodeURIComponent(e.id)}/webchat`,headers:{},query:{type:e.type},params:{id:e.id},body:{}});var Lm=e=>({path:`/v1/admin/bots/${encodeURIComponent(e.id)}/analytics`,headers:{},query:{startDate:e.startDate,endDate:e.endDate},params:{id:e.id},body:{}});var Sm=e=>({path:`/v1/admin/bots/${encodeURIComponent(e.id)}/issues/${encodeURIComponent(e.issueId)}`,headers:{},query:{},params:{id:e.id,issueId:e.issueId},body:{}});var _m=e=>({path:`/v1/admin/bots/${encodeURIComponent(e.id)}/issues`,headers:{},query:{nextToken:e.nextToken},params:{id:e.id},body:{}});var Hm=e=>({path:`/v1/admin/bots/${encodeURIComponent(e.id)}/issues/${encodeURIComponent(e.issueId)}`,headers:{},query:{},params:{id:e.id,issueId:e.issueId},body:{}});var Fm=e=>({path:`/v1/admin/bots/${encodeURIComponent(e.id)}/issues/${encodeURIComponent(e.issueId)}/events`,headers:{},query:{},params:{id:e.id,issueId:e.issueId},body:{}});var Mm=e=>({path:`/v1/admin/bots/${encodeURIComponent(e.id)}/versions`,headers:{},query:{},params:{id:e.id},body:{}});var Vm=e=>({path:`/v1/admin/bots/${encodeURIComponent(e.id)}/versions/${encodeURIComponent(e.versionId)}`,headers:{},query:{},params:{id:e.id,versionId:e.versionId},body:{}});var jm=e=>({path:`/v1/admin/bots/${encodeURIComponent(e.id)}/versions`,headers:{},query:{},params:{id:e.id},body:{name:e.name,description:e.description}});var zm=e=>({path:`/v1/admin/bots/${encodeURIComponent(e.id)}/versions/deploy`,headers:{},query:{},params:{id:e.id},body:{versionId:e.versionId}});var Ym=e=>({path:`/v1/admin/bots/${encodeURIComponent(e.botId)}/integrations/${encodeURIComponent(e.integrationId)}/shareable-id`,headers:{},query:{},params:{botId:e.botId,integrationId:e.integrationId},body:{}});var Zm=e=>({path:`/v1/admin/bots/${encodeURIComponent(e.botId)}/integrations/${encodeURIComponent(e.integrationId)}/shareable-id`,headers:{},query:{},params:{botId:e.botId,integrationId:e.integrationId},body:{}});var ty=e=>({path:`/v1/admin/bots/${encodeURIComponent(e.botId)}/integrations/${encodeURIComponent(e.integrationId)}/shareable-id`,headers:{},query:{},params:{botId:e.botId,integrationId:e.integrationId},body:{}});var ry=e=>({path:`/v1/admin/bots/${encodeURIComponent(e.botId)}/integrations/${encodeURIComponent(e.integrationId)}/sandboxed-conversations`,headers:{},query:{},params:{botId:e.botId,integrationId:e.integrationId},body:{}});var ny=e=>({path:"/v1/admin/bots/baks",headers:{},query:{botId:e.botId},params:{},body:{}});var iy=e=>({path:"/v1/admin/bots/baks",headers:{},query:{},params:{},body:{botId:e.botId,note:e.note}});var cy=e=>({path:`/v1/admin/bots/baks/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var dy=e=>({path:`/v1/admin/workspaces/${encodeURIComponent(e.id)}/billing/invoices`,headers:{},query:{},params:{id:e.id},body:{}});var gy=e=>({path:`/v1/admin/workspaces/${encodeURIComponent(e.id)}/billing/upcoming-invoice`,headers:{},query:{},params:{id:e.id},body:{}});var yy=e=>({path:`/v1/admin/workspaces/${encodeURIComponent(e.id)}/billing/invoices/charge-unpaid`,headers:{},query:{},params:{id:e.id},body:{invoiceIds:e.invoiceIds}});var hy=e=>({path:"/v1/admin/workspaces",headers:{},query:{},params:{},body:{name:e.name}});var fy=e=>({path:`/v1/admin/workspaces/${encodeURIComponent(e.id)}/public`,headers:{},query:{},params:{id:e.id},body:{}});var xy=e=>({path:`/v1/admin/workspaces/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var vy=e=>({path:`/v1/admin/workspaces/${encodeURIComponent(e.id)}/usages`,headers:{},query:{type:e.type,period:e.period},params:{id:e.id},body:{}});var Py=e=>({path:`/v1/admin/workspaces/${encodeURIComponent(e.id)}/usages/by-bot`,headers:{},query:{type:e.type,period:e.period},params:{id:e.id},body:{}});var Ty=e=>({path:"/v1/admin/workspaces/usages/quota-completion",headers:{},query:{},params:{},body:{}});var By=e=>({path:`/v1/admin/workspaces/${encodeURIComponent(e.id)}/quota`,headers:{},query:{type:e.type,period:e.period},params:{id:e.id},body:{}});var Uy=e=>({path:`/v1/admin/workspaces/${encodeURIComponent(e.id)}/quotas`,headers:{},query:{period:e.period},params:{id:e.id},body:{}});var Ly=e=>({path:`/v1/admin/workspaces/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{name:e.name,spendingLimit:e.spendingLimit,about:e.about,profilePicture:e.profilePicture,contactEmail:e.contactEmail,website:e.website,socialAccounts:e.socialAccounts,isPublic:e.isPublic,handle:e.handle}});var Sy=e=>({path:"/v1/admin/workspaces/handle-availability",headers:{},query:{},params:{},body:{handle:e.handle}});var _y=e=>({path:"/v1/admin/workspaces",headers:{},query:{nextToken:e.nextToken,handle:e.handle},params:{},body:{}});var Hy=e=>({path:"/v1/admin/workspaces/public",headers:{},query:{nextToken:e.nextToken,workspaceIds:e.workspaceIds,search:e.search},params:{},body:{}});var Fy=e=>({path:`/v1/admin/workspaces/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var My=e=>({path:`/v1/admin/workspaces/${encodeURIComponent(e.id)}/audit-records`,headers:{},query:{nextToken:e.nextToken},params:{id:e.id},body:{}});var Vy=e=>({path:"/v1/admin/workspace-members",headers:{},query:{nextToken:e.nextToken},params:{},body:{}});var jy=e=>({path:"/v1/admin/workspace-members/me",headers:{},query:{},params:{},body:{}});var zy=e=>({path:`/v1/admin/workspace-members/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var Yy=e=>({path:"/v1/admin/workspace-members",headers:{},query:{},params:{},body:{email:e.email,role:e.role}});var Zy=e=>({path:`/v1/admin/workspace-members/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{role:e.role}});var tR=e=>({path:"/v1/admin/integrations/iaks",headers:{},query:{integrationId:e.integrationId},params:{},body:{}});var rR=e=>({path:"/v1/admin/integrations/iaks",headers:{},query:{},params:{},body:{integrationId:e.integrationId,note:e.note}});var nR=e=>({path:`/v1/admin/integrations/iaks/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var iR=e=>({path:"/v1/admin/integrations",headers:{},query:{},params:{},body:{name:e.name,version:e.version,configuration:e.configuration,configurations:e.configurations,states:e.states,events:e.events,actions:e.actions,entities:e.entities,attributes:e.attributes,identifier:e.identifier,channels:e.channels,user:e.user,interfaces:e.interfaces,secrets:e.secrets,code:e.code,url:e.url,dev:e.dev,icon:e.icon,readme:e.readme,title:e.title,description:e.description,public:e.public,layers:e.layers}});var cR=e=>({path:"/v1/admin/integrations/validate",headers:{},query:{},params:{},body:{name:e.name,version:e.version,configuration:e.configuration,configurations:e.configurations,states:e.states,events:e.events,actions:e.actions,entities:e.entities,attributes:e.attributes,identifier:e.identifier,channels:e.channels,user:e.user,interfaces:e.interfaces,secrets:e.secrets,code:e.code,url:e.url,dev:e.dev,icon:e.icon,readme:e.readme,title:e.title,description:e.description,public:e.public,layers:e.layers}});var dR=e=>({path:`/v1/admin/integrations/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{configuration:e.configuration,configurations:e.configurations,channels:e.channels,identifier:e.identifier,actions:e.actions,events:e.events,states:e.states,user:e.user,entities:e.entities,interfaces:e.interfaces,sandbox:e.sandbox,attributes:e.attributes,secrets:e.secrets,code:e.code,icon:e.icon,readme:e.readme,title:e.title,description:e.description,url:e.url,public:e.public,layers:e.layers}});var gR=e=>({path:`/v1/admin/integrations/${encodeURIComponent(e.id)}/validate`,headers:{},query:{},params:{id:e.id},body:{configuration:e.configuration,configurations:e.configurations,channels:e.channels,identifier:e.identifier,actions:e.actions,events:e.events,states:e.states,user:e.user,entities:e.entities,interfaces:e.interfaces,sandbox:e.sandbox,attributes:e.attributes,secrets:e.secrets,code:e.code,icon:e.icon,readme:e.readme,title:e.title,description:e.description,url:e.url,public:e.public,layers:e.layers}});var yR=e=>({path:"/v1/admin/integrations",headers:{},query:{nextToken:e.nextToken,limit:e.limit,name:e.name,version:e.version,interfaceId:e.interfaceId,interfaceName:e.interfaceName,installedByBotId:e.installedByBotId,verificationStatus:e.verificationStatus,search:e.search,sortBy:e.sortBy,direction:e.direction,visibility:e.visibility,dev:e.dev},params:{},body:{}});var hR=e=>({path:`/v1/admin/integrations/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var fR=e=>({path:`/v1/admin/integrations/${encodeURIComponent(e.id)}/logs`,headers:{},query:{timeStart:e.timeStart,timeEnd:e.timeEnd,level:e.level,userId:e.userId,conversationId:e.conversationId,messageContains:e.messageContains,nextToken:e.nextToken},params:{id:e.id},body:{}});var xR=e=>({path:`/v1/admin/integrations/${encodeURIComponent(e.name)}/${encodeURIComponent(e.version)}`,headers:{},query:{},params:{name:e.name,version:e.version},body:{}});var vR=e=>({path:`/v1/admin/integrations/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var PR=e=>({path:"/v1/admin/integrations/request-verification",headers:{},query:{},params:{},body:{integrationId:e.integrationId}});var TR=e=>({path:"/v1/admin/interfaces",headers:{},query:{},params:{},body:{name:e.name,version:e.version,entities:e.entities,events:e.events,actions:e.actions,channels:e.channels,nameTemplate:e.nameTemplate,attributes:e.attributes,icon:e.icon,readme:e.readme,title:e.title,description:e.description,public:e.public}});var BR=e=>({path:`/v1/admin/interfaces/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var UR=e=>({path:`/v1/admin/interfaces/${encodeURIComponent(e.name)}/${encodeURIComponent(e.version)}`,headers:{},query:{},params:{name:e.name,version:e.version},body:{}});var LR=e=>({path:`/v1/admin/interfaces/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{entities:e.entities,events:e.events,actions:e.actions,channels:e.channels,nameTemplate:e.nameTemplate,attributes:e.attributes,icon:e.icon,readme:e.readme,title:e.title,description:e.description,public:e.public}});var SR=e=>({path:`/v1/admin/interfaces/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var _R=e=>({path:"/v1/admin/interfaces",headers:{},query:{nextToken:e.nextToken,name:e.name,version:e.version},params:{},body:{}});var HR=e=>({path:"/v1/admin/plugins",headers:{},query:{},params:{},body:{name:e.name,version:e.version,configuration:e.configuration,states:e.states,events:e.events,actions:e.actions,user:e.user,conversation:e.conversation,dependencies:e.dependencies,attributes:e.attributes,code:e.code,icon:e.icon,readme:e.readme,title:e.title,description:e.description,public:e.public}});var FR=e=>({path:`/v1/admin/plugins/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var MR=e=>({path:`/v1/admin/plugins/${encodeURIComponent(e.name)}/${encodeURIComponent(e.version)}`,headers:{},query:{},params:{name:e.name,version:e.version},body:{}});var VR=e=>({path:`/v1/admin/plugins/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{configuration:e.configuration,states:e.states,events:e.events,actions:e.actions,user:e.user,conversation:e.conversation,dependencies:e.dependencies,attributes:e.attributes,code:e.code,icon:e.icon,readme:e.readme,title:e.title,description:e.description,public:e.public}});var jR=e=>({path:`/v1/admin/plugins/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var zR=e=>({path:"/v1/admin/plugins",headers:{},query:{nextToken:e.nextToken,name:e.name,version:e.version},params:{},body:{}});var YR=e=>({path:`/v1/admin/plugins/${encodeURIComponent(e.id)}/code/${encodeURIComponent(e.platform)}`,headers:{},query:{},params:{id:e.id,platform:e.platform},body:{}});var ZR=e=>({path:`/v1/admin/usages/${encodeURIComponent(e.id)}`,headers:{},query:{type:e.type,period:e.period},params:{id:e.id},body:{}});var th=e=>({path:"/v1/admin/usages/multiple",headers:{},query:{types:e.types,ids:e.ids,period:e.period},params:{},body:{}});var rh=e=>({path:`/v1/admin/usages/${encodeURIComponent(e.id)}/history`,headers:{},query:{type:e.type},params:{id:e.id},body:{}});var nh=e=>({path:`/v1/admin/usages/${encodeURIComponent(e.id)}/activity`,headers:{},query:{type:e.type,timestampFrom:e.timestampFrom,timestampUntil:e.timestampUntil,nextToken:e.nextToken},params:{id:e.id},body:{}});var ih=e=>({path:`/v1/admin/usages/${encodeURIComponent(e.id)}/daily-activity`,headers:{},query:{type:e.type,dateFrom:e.dateFrom,dateUntil:e.dateUntil,nextToken:e.nextToken},params:{id:e.id},body:{}});var ch=e=>({path:"/v1/admin/quotas/ai-spend",headers:{},query:{},params:{},body:{monthlySpendingLimit:e.monthlySpendingLimit}});var dh=e=>({path:"/v1/admin/activities",headers:{},query:{nextToken:e.nextToken,taskId:e.taskId,botId:e.botId},params:{},body:{}});var gh=e=>({path:"/v1/admin/introspect",headers:{},query:{},params:{},body:{botId:e.botId}});var dr=class{constructor(t,s={}){this.axiosInstance=t;this.props=s}runVrl=async t=>{let{path:s,headers:r,query:n,body:o}=Sg(t),i=this.props.toAxiosRequest??h,p=this.props.toApiError??q,c=i({method:"post",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};getAccount=async t=>{let{path:s,headers:r,query:n,body:o}=_g(t),i=this.props.toAxiosRequest??h,p=this.props.toApiError??q,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};updateAccount=async t=>{let{path:s,headers:r,query:n,body:o}=Hg(t),i=this.props.toAxiosRequest??h,p=this.props.toApiError??q,c=i({method:"put",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};listPersonalAccessTokens=async t=>{let{path:s,headers:r,query:n,body:o}=Fg(t),i=this.props.toAxiosRequest??h,p=this.props.toApiError??q,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};createPersonalAccessToken=async t=>{let{path:s,headers:r,query:n,body:o}=Mg(t),i=this.props.toAxiosRequest??h,p=this.props.toApiError??q,c=i({method:"post",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};deletePersonalAccessToken=async t=>{let{path:s,headers:r,query:n,body:o}=Vg(t),i=this.props.toAxiosRequest??h,p=this.props.toApiError??q,c=i({method:"delete",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};setAccountPreference=async t=>{let{path:s,headers:r,query:n,body:o}=jg(t),i=this.props.toAxiosRequest??h,p=this.props.toApiError??q,c=i({method:"post",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};getAccountPreference=async t=>{let{path:s,headers:r,query:n,body:o}=zg(t),i=this.props.toAxiosRequest??h,p=this.props.toApiError??q,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};listPublicIntegrations=async t=>{let{path:s,headers:r,query:n,body:o}=Yg(t),i=this.props.toAxiosRequest??h,p=this.props.toApiError??q,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};getPublicIntegrationById=async t=>{let{path:s,headers:r,query:n,body:o}=Zg(t),i=this.props.toAxiosRequest??h,p=this.props.toApiError??q,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};getPublicIntegration=async t=>{let{path:s,headers:r,query:n,body:o}=tm(t),i=this.props.toAxiosRequest??h,p=this.props.toApiError??q,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};listPublicPlugins=async t=>{let{path:s,headers:r,query:n,body:o}=rm(t),i=this.props.toAxiosRequest??h,p=this.props.toApiError??q,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};getPublicPluginById=async t=>{let{path:s,headers:r,query:n,body:o}=nm(t),i=this.props.toAxiosRequest??h,p=this.props.toApiError??q,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};getPublicPlugin=async t=>{let{path:s,headers:r,query:n,body:o}=im(t),i=this.props.toAxiosRequest??h,p=this.props.toApiError??q,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};getPublicPluginCode=async t=>{let{path:s,headers:r,query:n,body:o}=cm(t),i=this.props.toAxiosRequest??h,p=this.props.toApiError??q,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};listPublicInterfaces=async t=>{let{path:s,headers:r,query:n,body:o}=dm(t),i=this.props.toAxiosRequest??h,p=this.props.toApiError??q,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};getPublicInterfaceById=async t=>{let{path:s,headers:r,query:n,body:o}=gm(t),i=this.props.toAxiosRequest??h,p=this.props.toApiError??q,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};getPublicInterface=async t=>{let{path:s,headers:r,query:n,body:o}=ym(t),i=this.props.toAxiosRequest??h,p=this.props.toApiError??q,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};createBot=async t=>{let{path:s,headers:r,query:n,body:o}=hm(t),i=this.props.toAxiosRequest??h,p=this.props.toApiError??q,c=i({method:"post",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};updateBot=async t=>{let{path:s,headers:r,query:n,body:o}=fm(t),i=this.props.toAxiosRequest??h,p=this.props.toApiError??q,c=i({method:"put",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};transferBot=async t=>{let{path:s,headers:r,query:n,body:o}=xm(t),i=this.props.toAxiosRequest??h,p=this.props.toApiError??q,c=i({method:"post",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};listBots=async t=>{let{path:s,headers:r,query:n,body:o}=vm(t),i=this.props.toAxiosRequest??h,p=this.props.toApiError??q,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};getBot=async t=>{let{path:s,headers:r,query:n,body:o}=Pm(t),i=this.props.toAxiosRequest??h,p=this.props.toApiError??q,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};deleteBot=async t=>{let{path:s,headers:r,query:n,body:o}=Tm(t),i=this.props.toAxiosRequest??h,p=this.props.toApiError??q,c=i({method:"delete",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};getBotLogs=async t=>{let{path:s,headers:r,query:n,body:o}=Bm(t),i=this.props.toAxiosRequest??h,p=this.props.toApiError??q,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};getBotWebchat=async t=>{let{path:s,headers:r,query:n,body:o}=Um(t),i=this.props.toAxiosRequest??h,p=this.props.toApiError??q,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};getBotAnalytics=async t=>{let{path:s,headers:r,query:n,body:o}=Lm(t),i=this.props.toAxiosRequest??h,p=this.props.toApiError??q,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};getBotIssue=async t=>{let{path:s,headers:r,query:n,body:o}=Sm(t),i=this.props.toAxiosRequest??h,p=this.props.toApiError??q,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};listBotIssues=async t=>{let{path:s,headers:r,query:n,body:o}=_m(t),i=this.props.toAxiosRequest??h,p=this.props.toApiError??q,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};deleteBotIssue=async t=>{let{path:s,headers:r,query:n,body:o}=Hm(t),i=this.props.toAxiosRequest??h,p=this.props.toApiError??q,c=i({method:"delete",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};listBotIssueEvents=async t=>{let{path:s,headers:r,query:n,body:o}=Fm(t),i=this.props.toAxiosRequest??h,p=this.props.toApiError??q,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};listBotVersions=async t=>{let{path:s,headers:r,query:n,body:o}=Mm(t),i=this.props.toAxiosRequest??h,p=this.props.toApiError??q,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};getBotVersion=async t=>{let{path:s,headers:r,query:n,body:o}=Vm(t),i=this.props.toAxiosRequest??h,p=this.props.toApiError??q,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};createBotVersion=async t=>{let{path:s,headers:r,query:n,body:o}=jm(t),i=this.props.toAxiosRequest??h,p=this.props.toApiError??q,c=i({method:"post",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};deployBotVersion=async t=>{let{path:s,headers:r,query:n,body:o}=zm(t),i=this.props.toAxiosRequest??h,p=this.props.toApiError??q,c=i({method:"post",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};createIntegrationShareableId=async t=>{let{path:s,headers:r,query:n,body:o}=Ym(t),i=this.props.toAxiosRequest??h,p=this.props.toApiError??q,c=i({method:"post",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};deleteIntegrationShareableId=async t=>{let{path:s,headers:r,query:n,body:o}=Zm(t),i=this.props.toAxiosRequest??h,p=this.props.toApiError??q,c=i({method:"delete",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};getIntegrationShareableId=async t=>{let{path:s,headers:r,query:n,body:o}=ty(t),i=this.props.toAxiosRequest??h,p=this.props.toApiError??q,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};unlinkSandboxedConversations=async t=>{let{path:s,headers:r,query:n,body:o}=ry(t),i=this.props.toAxiosRequest??h,p=this.props.toApiError??q,c=i({method:"delete",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};listBotApiKeys=async t=>{let{path:s,headers:r,query:n,body:o}=ny(t),i=this.props.toAxiosRequest??h,p=this.props.toApiError??q,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};createBotApiKey=async t=>{let{path:s,headers:r,query:n,body:o}=iy(t),i=this.props.toAxiosRequest??h,p=this.props.toApiError??q,c=i({method:"post",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};deleteBotApiKey=async t=>{let{path:s,headers:r,query:n,body:o}=cy(t),i=this.props.toAxiosRequest??h,p=this.props.toApiError??q,c=i({method:"delete",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};listWorkspaceInvoices=async t=>{let{path:s,headers:r,query:n,body:o}=dy(t),i=this.props.toAxiosRequest??h,p=this.props.toApiError??q,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};getUpcomingInvoice=async t=>{let{path:s,headers:r,query:n,body:o}=gy(t),i=this.props.toAxiosRequest??h,p=this.props.toApiError??q,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};chargeWorkspaceUnpaidInvoices=async t=>{let{path:s,headers:r,query:n,body:o}=yy(t),i=this.props.toAxiosRequest??h,p=this.props.toApiError??q,c=i({method:"post",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};createWorkspace=async t=>{let{path:s,headers:r,query:n,body:o}=hy(t),i=this.props.toAxiosRequest??h,p=this.props.toApiError??q,c=i({method:"post",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};getPublicWorkspace=async t=>{let{path:s,headers:r,query:n,body:o}=fy(t),i=this.props.toAxiosRequest??h,p=this.props.toApiError??q,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};getWorkspace=async t=>{let{path:s,headers:r,query:n,body:o}=xy(t),i=this.props.toAxiosRequest??h,p=this.props.toApiError??q,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};listWorkspaceUsages=async t=>{let{path:s,headers:r,query:n,body:o}=vy(t),i=this.props.toAxiosRequest??h,p=this.props.toApiError??q,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};breakDownWorkspaceUsageByBot=async t=>{let{path:s,headers:r,query:n,body:o}=Py(t),i=this.props.toAxiosRequest??h,p=this.props.toApiError??q,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};getAllWorkspaceQuotaCompletion=async t=>{let{path:s,headers:r,query:n,body:o}=Ty(t),i=this.props.toAxiosRequest??h,p=this.props.toApiError??q,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};getWorkspaceQuota=async t=>{let{path:s,headers:r,query:n,body:o}=By(t),i=this.props.toAxiosRequest??h,p=this.props.toApiError??q,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};listWorkspaceQuotas=async t=>{let{path:s,headers:r,query:n,body:o}=Uy(t),i=this.props.toAxiosRequest??h,p=this.props.toApiError??q,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};updateWorkspace=async t=>{let{path:s,headers:r,query:n,body:o}=Ly(t),i=this.props.toAxiosRequest??h,p=this.props.toApiError??q,c=i({method:"put",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};checkHandleAvailability=async t=>{let{path:s,headers:r,query:n,body:o}=Sy(t),i=this.props.toAxiosRequest??h,p=this.props.toApiError??q,c=i({method:"put",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};listWorkspaces=async t=>{let{path:s,headers:r,query:n,body:o}=_y(t),i=this.props.toAxiosRequest??h,p=this.props.toApiError??q,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};listPublicWorkspaces=async t=>{let{path:s,headers:r,query:n,body:o}=Hy(t),i=this.props.toAxiosRequest??h,p=this.props.toApiError??q,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};deleteWorkspace=async t=>{let{path:s,headers:r,query:n,body:o}=Fy(t),i=this.props.toAxiosRequest??h,p=this.props.toApiError??q,c=i({method:"delete",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};getAuditRecords=async t=>{let{path:s,headers:r,query:n,body:o}=My(t),i=this.props.toAxiosRequest??h,p=this.props.toApiError??q,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};listWorkspaceMembers=async t=>{let{path:s,headers:r,query:n,body:o}=Vy(t),i=this.props.toAxiosRequest??h,p=this.props.toApiError??q,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};getWorkspaceMember=async t=>{let{path:s,headers:r,query:n,body:o}=jy(t),i=this.props.toAxiosRequest??h,p=this.props.toApiError??q,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};deleteWorkspaceMember=async t=>{let{path:s,headers:r,query:n,body:o}=zy(t),i=this.props.toAxiosRequest??h,p=this.props.toApiError??q,c=i({method:"delete",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};createWorkspaceMember=async t=>{let{path:s,headers:r,query:n,body:o}=Yy(t),i=this.props.toAxiosRequest??h,p=this.props.toApiError??q,c=i({method:"post",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};updateWorkspaceMember=async t=>{let{path:s,headers:r,query:n,body:o}=Zy(t),i=this.props.toAxiosRequest??h,p=this.props.toApiError??q,c=i({method:"put",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};listIntegrationApiKeys=async t=>{let{path:s,headers:r,query:n,body:o}=tR(t),i=this.props.toAxiosRequest??h,p=this.props.toApiError??q,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};createIntegrationApiKey=async t=>{let{path:s,headers:r,query:n,body:o}=rR(t),i=this.props.toAxiosRequest??h,p=this.props.toApiError??q,c=i({method:"post",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};deleteIntegrationApiKey=async t=>{let{path:s,headers:r,query:n,body:o}=nR(t),i=this.props.toAxiosRequest??h,p=this.props.toApiError??q,c=i({method:"delete",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};createIntegration=async t=>{let{path:s,headers:r,query:n,body:o}=iR(t),i=this.props.toAxiosRequest??h,p=this.props.toApiError??q,c=i({method:"post",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};validateIntegrationCreation=async t=>{let{path:s,headers:r,query:n,body:o}=cR(t),i=this.props.toAxiosRequest??h,p=this.props.toApiError??q,c=i({method:"post",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};updateIntegration=async t=>{let{path:s,headers:r,query:n,body:o}=dR(t),i=this.props.toAxiosRequest??h,p=this.props.toApiError??q,c=i({method:"put",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};validateIntegrationUpdate=async t=>{let{path:s,headers:r,query:n,body:o}=gR(t),i=this.props.toAxiosRequest??h,p=this.props.toApiError??q,c=i({method:"put",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};listIntegrations=async t=>{let{path:s,headers:r,query:n,body:o}=yR(t),i=this.props.toAxiosRequest??h,p=this.props.toApiError??q,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};getIntegration=async t=>{let{path:s,headers:r,query:n,body:o}=hR(t),i=this.props.toAxiosRequest??h,p=this.props.toApiError??q,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};getIntegrationLogs=async t=>{let{path:s,headers:r,query:n,body:o}=fR(t),i=this.props.toAxiosRequest??h,p=this.props.toApiError??q,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};getIntegrationByName=async t=>{let{path:s,headers:r,query:n,body:o}=xR(t),i=this.props.toAxiosRequest??h,p=this.props.toApiError??q,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};deleteIntegration=async t=>{let{path:s,headers:r,query:n,body:o}=vR(t),i=this.props.toAxiosRequest??h,p=this.props.toApiError??q,c=i({method:"delete",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};requestIntegrationVerification=async t=>{let{path:s,headers:r,query:n,body:o}=PR(t),i=this.props.toAxiosRequest??h,p=this.props.toApiError??q,c=i({method:"post",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};createInterface=async t=>{let{path:s,headers:r,query:n,body:o}=TR(t),i=this.props.toAxiosRequest??h,p=this.props.toApiError??q,c=i({method:"post",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};getInterface=async t=>{let{path:s,headers:r,query:n,body:o}=BR(t),i=this.props.toAxiosRequest??h,p=this.props.toApiError??q,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};getInterfaceByName=async t=>{let{path:s,headers:r,query:n,body:o}=UR(t),i=this.props.toAxiosRequest??h,p=this.props.toApiError??q,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};updateInterface=async t=>{let{path:s,headers:r,query:n,body:o}=LR(t),i=this.props.toAxiosRequest??h,p=this.props.toApiError??q,c=i({method:"put",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};deleteInterface=async t=>{let{path:s,headers:r,query:n,body:o}=SR(t),i=this.props.toAxiosRequest??h,p=this.props.toApiError??q,c=i({method:"delete",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};listInterfaces=async t=>{let{path:s,headers:r,query:n,body:o}=_R(t),i=this.props.toAxiosRequest??h,p=this.props.toApiError??q,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};createPlugin=async t=>{let{path:s,headers:r,query:n,body:o}=HR(t),i=this.props.toAxiosRequest??h,p=this.props.toApiError??q,c=i({method:"post",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};getPlugin=async t=>{let{path:s,headers:r,query:n,body:o}=FR(t),i=this.props.toAxiosRequest??h,p=this.props.toApiError??q,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};getPluginByName=async t=>{let{path:s,headers:r,query:n,body:o}=MR(t),i=this.props.toAxiosRequest??h,p=this.props.toApiError??q,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};updatePlugin=async t=>{let{path:s,headers:r,query:n,body:o}=VR(t),i=this.props.toAxiosRequest??h,p=this.props.toApiError??q,c=i({method:"put",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};deletePlugin=async t=>{let{path:s,headers:r,query:n,body:o}=jR(t),i=this.props.toAxiosRequest??h,p=this.props.toApiError??q,c=i({method:"delete",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};listPlugins=async t=>{let{path:s,headers:r,query:n,body:o}=zR(t),i=this.props.toAxiosRequest??h,p=this.props.toApiError??q,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};getPluginCode=async t=>{let{path:s,headers:r,query:n,body:o}=YR(t),i=this.props.toAxiosRequest??h,p=this.props.toApiError??q,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};getUsage=async t=>{let{path:s,headers:r,query:n,body:o}=ZR(t),i=this.props.toAxiosRequest??h,p=this.props.toApiError??q,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};getMultipleUsages=async t=>{let{path:s,headers:r,query:n,body:o}=th(t),i=this.props.toAxiosRequest??h,p=this.props.toApiError??q,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};listUsageHistory=async t=>{let{path:s,headers:r,query:n,body:o}=rh(t),i=this.props.toAxiosRequest??h,p=this.props.toApiError??q,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};listUsageActivity=async t=>{let{path:s,headers:r,query:n,body:o}=nh(t),i=this.props.toAxiosRequest??h,p=this.props.toApiError??q,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};listUsageActivityDaily=async t=>{let{path:s,headers:r,query:n,body:o}=ih(t),i=this.props.toAxiosRequest??h,p=this.props.toApiError??q,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};changeAISpendQuota=async t=>{let{path:s,headers:r,query:n,body:o}=ch(t),i=this.props.toAxiosRequest??h,p=this.props.toApiError??q,c=i({method:"put",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};listActivities=async t=>{let{path:s,headers:r,query:n,body:o}=dh(t),i=this.props.toAxiosRequest??h,p=this.props.toApiError??q,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};introspect=async t=>{let{path:s,headers:r,query:n,body:o}=gh(t),i=this.props.toAxiosRequest??h,p=this.props.toApiError??q,c=i({method:"post",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})}};function q(e){return H.isAxiosError(e)&&e.response?.data?Vn(e.response.data):Vn(e)}var Kn=class extends dr{config;constructor(t){let s=be.getClientConfig(t),r=xe.createAxios(s),n=H.create(r);super(n,{toApiError:Ie.toApiError}),t.retry&&Ae(n,t.retry),this.config=s}get list(){return{publicIntegrations:t=>new B.AsyncCollection(({nextToken:s})=>this.listPublicIntegrations({nextToken:s,...t}).then(r=>({...r,items:r.integrations}))),bots:t=>new B.AsyncCollection(({nextToken:s})=>this.listBots({nextToken:s,...t}).then(r=>({...r,items:r.bots}))),botIssues:t=>new B.AsyncCollection(({nextToken:s})=>this.listBotIssues({nextToken:s,...t}).then(r=>({...r,items:r.issues}))),workspaces:t=>new B.AsyncCollection(({nextToken:s})=>this.listWorkspaces({nextToken:s,...t}).then(r=>({...r,items:r.workspaces}))),publicWorkspaces:t=>new B.AsyncCollection(({nextToken:s})=>this.listPublicWorkspaces({nextToken:s,...t}).then(r=>({...r,items:r.workspaces}))),workspaceMembers:t=>new B.AsyncCollection(({nextToken:s})=>this.listWorkspaceMembers({nextToken:s,...t}).then(r=>({...r,items:r.members}))),integrations:t=>new B.AsyncCollection(({nextToken:s})=>this.listIntegrations({nextToken:s,...t}).then(r=>({...r,items:r.integrations}))),interfaces:t=>new B.AsyncCollection(({nextToken:s})=>this.listInterfaces({nextToken:s,...t}).then(r=>({...r,items:r.interfaces}))),activities:t=>new B.AsyncCollection(({nextToken:s})=>this.listActivities({nextToken:s,...t}).then(r=>({...r,items:r.activities}))),usageActivity:t=>new B.AsyncCollection(({nextToken:s})=>this.listUsageActivity({nextToken:s,...t}).then(r=>({...r,items:r.data}))),usageActivityDaily:t=>new B.AsyncCollection(({nextToken:s})=>this.listUsageActivityDaily({nextToken:s,...t}).then(r=>({...r,items:r.data})))}}};var xo={};he(xo,{Client:()=>bo});var yh=Q(require("crypto"));var bU={getRandomValues:e=>new Uint8Array(e.map(()=>Math.floor(Math.random()*256)))},$n=typeof window<"u"&&typeof window.document<"u"?window.crypto:yh.default;$n.getRandomValues||($n=bU);var _=class extends Error{constructor(s,r,n,o,i,p,c){super(o);this.code=s;this.description=r;this.type=n;this.message=o;this.error=i;this.id=p;this.metadata=c;this.id||(this.id=_.generateId())}isApiError=!0;format(){return`[${this.type}] ${this.message} (Error ID: ${this.id})`}toJSON(){return{id:this.id,code:this.code,type:this.type,message:this.message,metadata:this.metadata}}static generateId(){let s=this.getPrefix(),r=new Date().toISOString().replace(/[\-:TZ]/g,"").split(".")[0],n=4,o=Array.from($n.getRandomValues(new Uint8Array(n))).map(i=>i.toString(16).padStart(2,"0")).join("").toUpperCase();return`${s}_${r}x${o}`}static getPrefix(){return typeof window<"u"&&typeof window.document<"u"?"err_bwsr":"err"}},xU=e=>typeof e=="object"&&!Array.isArray(e)&&e!==null,IU=e=>e instanceof _||xU(e)&&e.isApiError===!0,ct=class extends _{constructor(t,s,r,n){super(500,"An unknown error occurred","Unknown",t,s,r,n)}},zn=class extends _{constructor(t,s,r,n){super(500,"An internal error occurred","Internal",t,s,r,n)}},Jn=class extends _{constructor(t,s,r,n){super(401,"The request requires to be authenticated.","Unauthorized",t,s,r,n)}},Yn=class extends _{constructor(t,s,r,n){super(403,"The requested action can't be peform by this resource.","Forbidden",t,s,r,n)}},Xn=class extends _{constructor(t,s,r,n){super(413,"The request payload is too large.","PayloadTooLarge",t,s,r,n)}},Zn=class extends _{constructor(t,s,r,n){super(400,"The request payload is invalid.","InvalidPayload",t,s,r,n)}},eo=class extends _{constructor(t,s,r,n){super(415,"The request is invalid because the content-type is not supported.","UnsupportedMediaType",t,s,r,n)}},to=class extends _{constructor(t,s,r,n){super(405,"The requested method does not exist.","MethodNotFound",t,s,r,n)}},so=class extends _{constructor(t,s,r,n){super(404,"The requested resource does not exist.","ResourceNotFound",t,s,r,n)}},ro=class extends _{constructor(t,s,r,n){super(400,"The provided JSON schema is invalid.","InvalidJsonSchema",t,s,r,n)}},ao=class extends _{constructor(t,s,r,n){super(400,"The provided data doesn't respect the provided JSON schema.","InvalidDataFormat",t,s,r,n)}},no=class extends _{constructor(t,s,r,n){super(400,"The provided identifier is not valid. An identifier must start with a lowercase letter, be between 2 and 100 characters long and use only alphanumeric characters.","InvalidIdentifier",t,s,r,n)}},oo=class extends _{constructor(t,s,r,n){super(409,"The resource is related with a different resource that the one referenced in the request. This is usually caused when providing two resource identifiers that aren't linked together.","RelationConflict",t,s,r,n)}},io=class extends _{constructor(t,s,r,n){super(409,"The resource cannot be deleted because it's referenced by another resource","ReferenceConstraint",t,s,r,n)}},po=class extends _{constructor(t,s,r,n){super(409,"The resource is current locked and cannot be operated on until the lock is released.","ResourceLockedConflict",t,s,r,n)}},co=class extends _{constructor(t,s,r,n){super(400,"The provided resource reference is missing. This is usually caused when providing an invalid id inside the payload of a request.","ReferenceNotFound",t,s,r,n)}},uo=class extends _{constructor(t,s,r,n){super(400,"The provided query is invalid. This is usually caused when providing an invalid parameter for querying a resource.","InvalidQuery",t,s,r,n)}},lo=class extends _{constructor(t,s,r,n){super(400,"An error happened during the execution of a runtime (bot or integration).","Runtime",t,s,r,n)}},go=class extends _{constructor(t,s,r,n){super(409,"The record attempted to be created already exists.","AlreadyExists",t,s,r,n)}},mo=class extends _{constructor(t,s,r,n){super(429,"The request has been rate limited.","RateLimited",t,s,r,n)}},yo=class extends _{constructor(t,s,r,n){super(402,"A payment is required to perform this request.","PaymentRequired",t,s,r,n)}},Ro=class extends _{constructor(t,s,r,n){super(403,"The request exceeds the allowed quota. Quotas are a soft limit that can be increased.","QuotaExceeded",t,s,r,n)}},ho=class extends _{constructor(t,s,r,n){super(413,"The request exceeds the allowed limit. Limits are a hard limit that cannot be increased.","LimitExceeded",t,s,r,n)}},qo=class extends _{constructor(t,s,r,n){super(400,"Request payload contains breaking changes which is not allowed for this resource without a version increment.","BreakingChanges",t,s,r,n)}},vU={Unknown:ct,Internal:zn,Unauthorized:Jn,Forbidden:Yn,PayloadTooLarge:Xn,InvalidPayload:Zn,UnsupportedMediaType:eo,MethodNotFound:to,ResourceNotFound:so,InvalidJsonSchema:ro,InvalidDataFormat:ao,InvalidIdentifier:no,RelationConflict:oo,ReferenceConstraint:io,ResourceLockedConflict:po,ReferenceNotFound:co,InvalidQuery:uo,Runtime:lo,AlreadyExists:go,RateLimited:mo,PaymentRequired:yo,QuotaExceeded:Ro,LimitExceeded:ho,BreakingChanges:qo},fo=e=>IU(e)?e:e instanceof Error?new ct(e.message,e):typeof e=="string"?new ct(e):kU(e);function kU(e){if(typeof e=="object"&&"code"in e&&"type"in e&&"id"in e&&"message"in e&&typeof e.type=="string"&&typeof e.message=="string"){let t=vU[e.type];return t?new t(e.message,void 0,e.id||"UNKNOWN",e.metadata):new ct(`An unclassified API error occurred: ${e.message} (Type: ${e.type}, Code: ${e.code})`)}return new ct("An invalid error occurred: "+JSON.stringify(e))}var Rh=Q(Et()),PU=e=>e[1]!==void 0,re=e=>{let{method:t,path:s,query:r,headers:n,body:o}=e,i=Object.entries(n).filter(PU),p=Object.fromEntries(i),c=Rh.default.stringify(r,{encode:!0,arrayFormat:"repeat",allowDots:!0}),a=c?[s,c].join("?"):s,u=["put","post","delete","patch"].includes(t.toLowerCase())?o:void 0;return{method:t,url:a,headers:p,data:u}};var hh=e=>({path:"/v1/files",headers:{},query:{},params:{},body:{key:e.key,tags:e.tags,size:e.size,index:e.index,indexing:e.indexing,accessPolicies:e.accessPolicies,contentType:e.contentType,expiresAt:e.expiresAt,publicContentImmediatelyAccessible:e.publicContentImmediatelyAccessible,metadata:e.metadata}});var fh=e=>({path:`/v1/files/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var xh=e=>({path:"/v1/files",headers:{},query:{nextToken:e.nextToken,sortField:e.sortField,sortDirection:e.sortDirection,tags:e.tags,ids:e.ids},params:{},body:{}});var vh=e=>({path:`/v1/files/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var Ph=e=>({path:`/v1/files/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{metadata:e.metadata,tags:e.tags,accessPolicies:e.accessPolicies,expiresAt:e.expiresAt}});var Th=e=>({path:`/v1/files/${encodeURIComponent(e.idOrKey)}/${encodeURIComponent(e.destinationKey)}`,headers:{"x-destination-bot-id":e["x-destination-bot-id"]},query:{},params:{idOrKey:e.idOrKey,destinationKey:e.destinationKey},body:{overwrite:e.overwrite}});var Bh=e=>({path:"/v1/files/search",headers:{},query:{tags:e.tags,query:e.query,contextDepth:e.contextDepth,limit:e.limit,consolidate:e.consolidate,includeBreadcrumb:e.includeBreadcrumb},params:{},body:{}});var Uh=e=>({path:`/v1/files/${encodeURIComponent(e.id)}/passages`,headers:{},query:{nextToken:e.nextToken,limit:e.limit},params:{id:e.id},body:{}});var Lh=e=>({path:`/v1/files/${encodeURIComponent(e.id)}/passages`,headers:{},query:{},params:{id:e.id},body:{passages:e.passages}});var Sh=e=>({path:"/v1/files/tags",headers:{},query:{nextToken:e.nextToken},params:{},body:{}});var _h=e=>({path:`/v1/files/tags/${encodeURIComponent(e.tag)}/values`,headers:{},query:{nextToken:e.nextToken},params:{tag:e.tag},body:{}});var Hh=e=>({path:"/v1/files/knowledge-bases",headers:{},query:{},params:{},body:{name:e.name}});var Fh=e=>({path:`/v1/files/knowledge-bases/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var Mh=e=>({path:`/v1/files/knowledge-bases/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{name:e.name}});var Vh=e=>({path:"/v1/files/knowledge-bases",headers:{},query:{nextToken:e.nextToken},params:{},body:{}});var lr=class{constructor(t,s={}){this.axiosInstance=t;this.props=s}upsertFile=async t=>{let{path:s,headers:r,query:n,body:o}=hh(t),i=this.props.toAxiosRequest??re,p=this.props.toApiError??ie,c=i({method:"put",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};deleteFile=async t=>{let{path:s,headers:r,query:n,body:o}=fh(t),i=this.props.toAxiosRequest??re,p=this.props.toApiError??ie,c=i({method:"delete",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};listFiles=async t=>{let{path:s,headers:r,query:n,body:o}=xh(t),i=this.props.toAxiosRequest??re,p=this.props.toApiError??ie,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};getFile=async t=>{let{path:s,headers:r,query:n,body:o}=vh(t),i=this.props.toAxiosRequest??re,p=this.props.toApiError??ie,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};updateFileMetadata=async t=>{let{path:s,headers:r,query:n,body:o}=Ph(t),i=this.props.toAxiosRequest??re,p=this.props.toApiError??ie,c=i({method:"put",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};copyFile=async t=>{let{path:s,headers:r,query:n,body:o}=Th(t),i=this.props.toAxiosRequest??re,p=this.props.toApiError??ie,c=i({method:"post",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};searchFiles=async t=>{let{path:s,headers:r,query:n,body:o}=Bh(t),i=this.props.toAxiosRequest??re,p=this.props.toApiError??ie,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};listFilePassages=async t=>{let{path:s,headers:r,query:n,body:o}=Uh(t),i=this.props.toAxiosRequest??re,p=this.props.toApiError??ie,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};setFilePassages=async t=>{let{path:s,headers:r,query:n,body:o}=Lh(t),i=this.props.toAxiosRequest??re,p=this.props.toApiError??ie,c=i({method:"put",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};listFileTags=async t=>{let{path:s,headers:r,query:n,body:o}=Sh(t),i=this.props.toAxiosRequest??re,p=this.props.toApiError??ie,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};listFileTagValues=async t=>{let{path:s,headers:r,query:n,body:o}=_h(t),i=this.props.toAxiosRequest??re,p=this.props.toApiError??ie,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};createKnowledgeBase=async t=>{let{path:s,headers:r,query:n,body:o}=Hh(t),i=this.props.toAxiosRequest??re,p=this.props.toApiError??ie,c=i({method:"post",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};deleteKnowledgeBase=async t=>{let{path:s,headers:r,query:n,body:o}=Fh(t),i=this.props.toAxiosRequest??re,p=this.props.toApiError??ie,c=i({method:"delete",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};updateKnowledgeBase=async t=>{let{path:s,headers:r,query:n,body:o}=Mh(t),i=this.props.toAxiosRequest??re,p=this.props.toApiError??ie,c=i({method:"put",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};listKnowledgeBases=async t=>{let{path:s,headers:r,query:n,body:o}=Vh(t),i=this.props.toAxiosRequest??re,p=this.props.toApiError??ie,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})}};function ie(e){return H.isAxiosError(e)&&e.response?.data?fo(e.response.data):fo(e)}var gr=async(e,{key:t,index:s,tags:r,contentType:n,accessPolicies:o,content:i,url:p,indexing:c,expiresAt:a,metadata:u,publicContentImmediatelyAccessible:y})=>{if(p&&i)throw new We("Cannot provide both content and URL, please provide only one of them");if(p&&(i=await H.get(p,{responseType:"arraybuffer"}).then(k=>k.data).catch(k=>{throw new We(`Failed to download file from provided URL: ${k.message}`,k)})),!i)throw new We("No content was provided for the file");let R,x;if(typeof i=="string"){let I=new TextEncoder().encode(i);R=I,x=I.byteLength}else if(i instanceof Uint8Array)R=i,x=R.byteLength;else if(i instanceof ArrayBuffer)R=i,x=R.byteLength;else if(i instanceof Blob)R=i,x=i.size;else throw new We("The provided content is not supported");let{file:m}=await e.upsertFile({key:t,tags:r,index:s,accessPolicies:o,contentType:n,metadata:u,size:x,expiresAt:a,indexing:c,publicContentImmediatelyAccessible:y}),f={"Content-Type":m.contentType};y&&(f["x-amz-tagging"]="public=true");try{await H.put(m.uploadUrl,R,{maxBodyLength:1/0,headers:f})}catch(k){let I=k instanceof Error?k:new Error(String(k));throw new We(`Failed to upload file: ${I.message}`,I,m)}return{file:{...m,size:x}}};var bo=class extends lr{config;constructor(t){let s=be.getClientConfig(t),r=xe.createAxios(s),n=H.create(r);super(n,{toApiError:Ie.toApiError}),t.retry&&Ae(n,t.retry),this.config=s}get list(){return{files:t=>new B.AsyncCollection(({nextToken:s})=>this.listFiles({nextToken:s,...t}).then(r=>({...r,items:r.files}))),filePassages:t=>new B.AsyncCollection(({nextToken:s})=>this.listFilePassages({nextToken:s,...t}).then(r=>({...r,items:r.passages})))}}async uploadFile(t){return await gr(this,t)}};var $o={};he($o,{Client:()=>jo});var $h=Q(require("crypto"));var TU={getRandomValues:e=>new Uint8Array(e.map(()=>Math.floor(Math.random()*256)))},Io=typeof window<"u"&&typeof window.document<"u"?window.crypto:$h.default;Io.getRandomValues||(Io=TU);var D=class extends Error{constructor(s,r,n,o,i,p,c){super(o);this.code=s;this.description=r;this.type=n;this.message=o;this.error=i;this.id=p;this.metadata=c;this.id||(this.id=D.generateId())}isApiError=!0;format(){return`[${this.type}] ${this.message} (Error ID: ${this.id})`}toJSON(){return{id:this.id,code:this.code,type:this.type,message:this.message,metadata:this.metadata}}static generateId(){let s=this.getPrefix(),r=new Date().toISOString().replace(/[\-:TZ]/g,"").split(".")[0],n=4,o=Array.from(Io.getRandomValues(new Uint8Array(n))).map(i=>i.toString(16).padStart(2,"0")).join("").toUpperCase();return`${s}_${r}x${o}`}static getPrefix(){return typeof window<"u"&&typeof window.document<"u"?"err_bwsr":"err"}},wU=e=>typeof e=="object"&&!Array.isArray(e)&&e!==null,BU=e=>e instanceof D||wU(e)&&e.isApiError===!0,ut=class extends D{constructor(t,s,r,n){super(500,"An unknown error occurred","Unknown",t,s,r,n)}},vo=class extends D{constructor(t,s,r,n){super(500,"An internal error occurred","Internal",t,s,r,n)}},ko=class extends D{constructor(t,s,r,n){super(401,"The request requires to be authenticated.","Unauthorized",t,s,r,n)}},Po=class extends D{constructor(t,s,r,n){super(403,"The requested action can't be peform by this resource.","Forbidden",t,s,r,n)}},Ao=class extends D{constructor(t,s,r,n){super(413,"The request payload is too large.","PayloadTooLarge",t,s,r,n)}},To=class extends D{constructor(t,s,r,n){super(400,"The request payload is invalid.","InvalidPayload",t,s,r,n)}},wo=class extends D{constructor(t,s,r,n){super(415,"The request is invalid because the content-type is not supported.","UnsupportedMediaType",t,s,r,n)}},Bo=class extends D{constructor(t,s,r,n){super(405,"The requested method does not exist.","MethodNotFound",t,s,r,n)}},Co=class extends D{constructor(t,s,r,n){super(404,"The requested resource does not exist.","ResourceNotFound",t,s,r,n)}},Uo=class extends D{constructor(t,s,r,n){super(400,"The provided JSON schema is invalid.","InvalidJsonSchema",t,s,r,n)}},Eo=class extends D{constructor(t,s,r,n){super(400,"The provided data doesn't respect the provided JSON schema.","InvalidDataFormat",t,s,r,n)}},Lo=class extends D{constructor(t,s,r,n){super(400,"The provided identifier is not valid. An identifier must start with a lowercase letter, be between 2 and 100 characters long and use only alphanumeric characters.","InvalidIdentifier",t,s,r,n)}},Go=class extends D{constructor(t,s,r,n){super(409,"The resource is related with a different resource that the one referenced in the request. This is usually caused when providing two resource identifiers that aren't linked together.","RelationConflict",t,s,r,n)}},So=class extends D{constructor(t,s,r,n){super(409,"The resource cannot be deleted because it's referenced by another resource","ReferenceConstraint",t,s,r,n)}},Wo=class extends D{constructor(t,s,r,n){super(409,"The resource is current locked and cannot be operated on until the lock is released.","ResourceLockedConflict",t,s,r,n)}},_o=class extends D{constructor(t,s,r,n){super(400,"The provided resource reference is missing. This is usually caused when providing an invalid id inside the payload of a request.","ReferenceNotFound",t,s,r,n)}},Do=class extends D{constructor(t,s,r,n){super(400,"The provided query is invalid. This is usually caused when providing an invalid parameter for querying a resource.","InvalidQuery",t,s,r,n)}},Ho=class extends D{constructor(t,s,r,n){super(400,"An error happened during the execution of a runtime (bot or integration).","Runtime",t,s,r,n)}},Qo=class extends D{constructor(t,s,r,n){super(409,"The record attempted to be created already exists.","AlreadyExists",t,s,r,n)}},Fo=class extends D{constructor(t,s,r,n){super(429,"The request has been rate limited.","RateLimited",t,s,r,n)}},Oo=class extends D{constructor(t,s,r,n){super(402,"A payment is required to perform this request.","PaymentRequired",t,s,r,n)}},Mo=class extends D{constructor(t,s,r,n){super(403,"The request exceeds the allowed quota. Quotas are a soft limit that can be increased.","QuotaExceeded",t,s,r,n)}},No=class extends D{constructor(t,s,r,n){super(413,"The request exceeds the allowed limit. Limits are a hard limit that cannot be increased.","LimitExceeded",t,s,r,n)}},Vo=class extends D{constructor(t,s,r,n){super(400,"Request payload contains breaking changes which is not allowed for this resource without a version increment.","BreakingChanges",t,s,r,n)}},CU={Unknown:ut,Internal:vo,Unauthorized:ko,Forbidden:Po,PayloadTooLarge:Ao,InvalidPayload:To,UnsupportedMediaType:wo,MethodNotFound:Bo,ResourceNotFound:Co,InvalidJsonSchema:Uo,InvalidDataFormat:Eo,InvalidIdentifier:Lo,RelationConflict:Go,ReferenceConstraint:So,ResourceLockedConflict:Wo,ReferenceNotFound:_o,InvalidQuery:Do,Runtime:Ho,AlreadyExists:Qo,RateLimited:Fo,PaymentRequired:Oo,QuotaExceeded:Mo,LimitExceeded:No,BreakingChanges:Vo},Ko=e=>BU(e)?e:e instanceof Error?new ut(e.message,e):typeof e=="string"?new ut(e):UU(e);function UU(e){if(typeof e=="object"&&"code"in e&&"type"in e&&"id"in e&&"message"in e&&typeof e.type=="string"&&typeof e.message=="string"){let t=CU[e.type];return t?new t(e.message,void 0,e.id||"UNKNOWN",e.metadata):new ut(`An unclassified API error occurred: ${e.message} (Type: ${e.type}, Code: ${e.code})`)}return new ut("An invalid error occurred: "+JSON.stringify(e))}var zh=Q(Et()),EU=e=>e[1]!==void 0,Z=e=>{let{method:t,path:s,query:r,headers:n,body:o}=e,i=Object.entries(n).filter(EU),p=Object.fromEntries(i),c=zh.default.stringify(r,{encode:!0,arrayFormat:"repeat",allowDots:!0}),a=c?[s,c].join("?"):s,u=["put","post","delete","patch"].includes(t.toLowerCase())?o:void 0;return{method:t,url:a,headers:p,data:u}};var Jh=e=>({path:"/v1/tables",headers:{},query:{tags:e.tags},params:{},body:{}});var Xh=e=>({path:`/v1/tables/${encodeURIComponent(e.table)}`,headers:{},query:{},params:{table:e.table},body:{}});var eq=e=>({path:`/v1/tables/${encodeURIComponent(e.table)}`,headers:{},query:{},params:{table:e.table},body:{factor:e.factor,frozen:e.frozen,schema:e.schema,tags:e.tags,isComputeEnabled:e.isComputeEnabled}});var sq=e=>({path:"/v1/tables",headers:{},query:{},params:{},body:{name:e.name,factor:e.factor,frozen:e.frozen,schema:e.schema,tags:e.tags,isComputeEnabled:e.isComputeEnabled}});var aq=e=>({path:`/v1/tables/${encodeURIComponent(e.sourceTableId)}/duplicate`,headers:{},query:{},params:{sourceTableId:e.sourceTableId},body:{tableName:e.tableName,schemaOnly:e.schemaOnly,factor:e.factor}});var oq=e=>({path:`/v1/tables/${encodeURIComponent(e.table)}/export`,headers:{},query:{format:e.format,compress:e.compress},params:{table:e.table},body:{}});var pq=e=>({path:`/v1/tables/${encodeURIComponent(e.table)}/jobs`,headers:{},query:{},params:{table:e.table},body:{}});var uq=e=>({path:`/v1/tables/${encodeURIComponent(e.table)}/import`,headers:{},query:{},params:{table:e.table},body:{fileId:e.fileId}});var lq=e=>({path:`/v1/tables/${encodeURIComponent(e.table)}`,headers:{},query:{},params:{table:e.table},body:{name:e.name,frozen:e.frozen,schema:e.schema,tags:e.tags,isComputeEnabled:e.isComputeEnabled}});var mq=e=>({path:`/v1/tables/${encodeURIComponent(e.table)}/column`,headers:{},query:{},params:{table:e.table},body:{name:e.name,newName:e.newName}});var Rq=e=>({path:`/v1/tables/${encodeURIComponent(e.table)}`,headers:{},query:{},params:{table:e.table},body:{}});var qq=e=>({path:`/v1/tables/${encodeURIComponent(e.table)}/row`,headers:{},query:{id:e.id},params:{table:e.table},body:{}});var bq=e=>({path:`/v1/tables/${encodeURIComponent(e.table)}/rows/find`,headers:{},query:{},params:{table:e.table},body:{limit:e.limit,offset:e.offset,filter:e.filter,group:e.group,search:e.search,orderBy:e.orderBy,orderDirection:e.orderDirection}});var Iq=e=>({path:`/v1/tables/${encodeURIComponent(e.table)}/rows`,headers:{},query:{},params:{table:e.table},body:{rows:e.rows,waitComputed:e.waitComputed}});var kq=e=>({path:`/v1/tables/${encodeURIComponent(e.table)}/rows/delete`,headers:{},query:{},params:{table:e.table},body:{ids:e.ids,filter:e.filter,deleteAllRows:e.deleteAllRows}});var Aq=e=>({path:`/v1/tables/${encodeURIComponent(e.table)}/rows`,headers:{},query:{},params:{table:e.table},body:{rows:e.rows,waitComputed:e.waitComputed}});var wq=e=>({path:`/v1/tables/${encodeURIComponent(e.table)}/rows/upsert`,headers:{},query:{},params:{table:e.table},body:{rows:e.rows,keyColumn:e.keyColumn,waitComputed:e.waitComputed}});var mr=class{constructor(t,s={}){this.axiosInstance=t;this.props=s}listTables=async t=>{let{path:s,headers:r,query:n,body:o}=Jh(t),i=this.props.toAxiosRequest??Z,p=this.props.toApiError??te,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};getTable=async t=>{let{path:s,headers:r,query:n,body:o}=Xh(t),i=this.props.toAxiosRequest??Z,p=this.props.toApiError??te,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};getOrCreateTable=async t=>{let{path:s,headers:r,query:n,body:o}=eq(t),i=this.props.toAxiosRequest??Z,p=this.props.toApiError??te,c=i({method:"post",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};createTable=async t=>{let{path:s,headers:r,query:n,body:o}=sq(t),i=this.props.toAxiosRequest??Z,p=this.props.toApiError??te,c=i({method:"post",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};duplicateTable=async t=>{let{path:s,headers:r,query:n,body:o}=aq(t),i=this.props.toAxiosRequest??Z,p=this.props.toApiError??te,c=i({method:"post",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};exportTable=async t=>{let{path:s,headers:r,query:n,body:o}=oq(t),i=this.props.toAxiosRequest??Z,p=this.props.toApiError??te,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};getTableJobs=async t=>{let{path:s,headers:r,query:n,body:o}=pq(t),i=this.props.toAxiosRequest??Z,p=this.props.toApiError??te,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};importTable=async t=>{let{path:s,headers:r,query:n,body:o}=uq(t),i=this.props.toAxiosRequest??Z,p=this.props.toApiError??te,c=i({method:"post",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};updateTable=async t=>{let{path:s,headers:r,query:n,body:o}=lq(t),i=this.props.toAxiosRequest??Z,p=this.props.toApiError??te,c=i({method:"put",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};renameTableColumn=async t=>{let{path:s,headers:r,query:n,body:o}=mq(t),i=this.props.toAxiosRequest??Z,p=this.props.toApiError??te,c=i({method:"put",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};deleteTable=async t=>{let{path:s,headers:r,query:n,body:o}=Rq(t),i=this.props.toAxiosRequest??Z,p=this.props.toApiError??te,c=i({method:"delete",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};getTableRow=async t=>{let{path:s,headers:r,query:n,body:o}=qq(t),i=this.props.toAxiosRequest??Z,p=this.props.toApiError??te,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};findTableRows=async t=>{let{path:s,headers:r,query:n,body:o}=bq(t),i=this.props.toAxiosRequest??Z,p=this.props.toApiError??te,c=i({method:"post",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};createTableRows=async t=>{let{path:s,headers:r,query:n,body:o}=Iq(t),i=this.props.toAxiosRequest??Z,p=this.props.toApiError??te,c=i({method:"post",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};deleteTableRows=async t=>{let{path:s,headers:r,query:n,body:o}=kq(t),i=this.props.toAxiosRequest??Z,p=this.props.toApiError??te,c=i({method:"post",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};updateTableRows=async t=>{let{path:s,headers:r,query:n,body:o}=Aq(t),i=this.props.toAxiosRequest??Z,p=this.props.toApiError??te,c=i({method:"put",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};upsertTableRows=async t=>{let{path:s,headers:r,query:n,body:o}=wq(t),i=this.props.toAxiosRequest??Z,p=this.props.toApiError??te,c=i({method:"post",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})}};function te(e){return H.isAxiosError(e)&&e.response?.data?Ko(e.response.data):Ko(e)}var jo=class extends mr{config;constructor(t){let s=be.getClientConfig(t),r=xe.createAxios(s),n=H.create(r);super(n,{toApiError:Ie.toApiError}),t.retry&&Ae(n,t.retry),this.config=s}};var Cq=Q(Et()),GU=e=>e[1]!==void 0,d=e=>{let{method:t,path:s,query:r,headers:n,body:o}=e,i=Object.entries(n).filter(GU),p=Object.fromEntries(i),c=Cq.default.stringify(r,{encode:!0,arrayFormat:"repeat",allowDots:!0}),a=c?[s,c].join("?"):s,u=["put","post","delete","patch"].includes(t.toLowerCase())?o:void 0;return{method:t,url:a,headers:p,data:u}};var Uq=e=>({path:"/v1/chat/conversations",headers:{},query:{},params:{},body:{channel:e.channel,tags:e.tags,integrationName:e.integrationName}});var Lq=e=>({path:`/v1/chat/conversations/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var Sq=e=>({path:"/v1/chat/conversations",headers:{},query:{nextToken:e.nextToken,sortField:e.sortField,sortDirection:e.sortDirection,tags:e.tags,participantIds:e.participantIds,integrationName:e.integrationName,channel:e.channel},params:{},body:{}});var _q=e=>({path:"/v1/chat/conversations/get-or-create",headers:{},query:{},params:{},body:{channel:e.channel,tags:e.tags,integrationName:e.integrationName,discriminateByTags:e.discriminateByTags}});var Hq=e=>({path:`/v1/chat/conversations/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{currentTaskId:e.currentTaskId,tags:e.tags}});var Fq=e=>({path:`/v1/chat/conversations/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var Mq=e=>({path:`/v1/chat/conversations/${encodeURIComponent(e.id)}/participants`,headers:{},query:{nextToken:e.nextToken},params:{id:e.id},body:{}});var Vq=e=>({path:`/v1/chat/conversations/${encodeURIComponent(e.id)}/participants`,headers:{},query:{},params:{id:e.id},body:{userId:e.userId}});var jq=e=>({path:`/v1/chat/conversations/${encodeURIComponent(e.id)}/participants/${encodeURIComponent(e.userId)}`,headers:{},query:{},params:{id:e.id,userId:e.userId},body:{}});var zq=e=>({path:`/v1/chat/conversations/${encodeURIComponent(e.id)}/participants/${encodeURIComponent(e.userId)}`,headers:{},query:{},params:{id:e.id,userId:e.userId},body:{}});var Yq=e=>({path:"/v1/chat/events",headers:{},query:{},params:{},body:{type:e.type,payload:e.payload,schedule:e.schedule,conversationId:e.conversationId,userId:e.userId,messageId:e.messageId}});var Zq=e=>({path:`/v1/chat/events/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var tf=e=>({path:"/v1/chat/events",headers:{},query:{nextToken:e.nextToken,type:e.type,conversationId:e.conversationId,userId:e.userId,messageId:e.messageId,status:e.status},params:{},body:{}});var rf=e=>({path:"/v1/chat/messages",headers:{},query:{},params:{},body:{payload:e.payload,userId:e.userId,conversationId:e.conversationId,type:e.type,tags:e.tags,schedule:e.schedule}});var nf=e=>({path:"/v1/chat/messages/get-or-create",headers:{},query:{},params:{},body:{payload:e.payload,userId:e.userId,conversationId:e.conversationId,type:e.type,tags:e.tags,schedule:e.schedule,discriminateByTags:e.discriminateByTags}});var pf=e=>({path:`/v1/chat/messages/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var uf=e=>({path:`/v1/chat/messages/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{tags:e.tags,payload:e.payload}});var lf=e=>({path:"/v1/chat/messages",headers:{},query:{nextToken:e.nextToken,conversationId:e.conversationId,tags:e.tags},params:{},body:{}});var mf=e=>({path:`/v1/chat/messages/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var Rf=e=>({path:"/v1/chat/users",headers:{},query:{},params:{},body:{tags:e.tags,integrationName:e.integrationName,name:e.name,pictureUrl:e.pictureUrl}});var qf=e=>({path:`/v1/chat/users/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var bf=e=>({path:"/v1/chat/users",headers:{},query:{nextToken:e.nextToken,conversationId:e.conversationId,tags:e.tags},params:{},body:{}});var If=e=>({path:"/v1/chat/users/get-or-create",headers:{},query:{},params:{},body:{tags:e.tags,integrationName:e.integrationName,name:e.name,pictureUrl:e.pictureUrl,discriminateByTags:e.discriminateByTags}});var kf=e=>({path:`/v1/chat/users/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{tags:e.tags,name:e.name,pictureUrl:e.pictureUrl}});var Af=e=>({path:`/v1/chat/users/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var wf=e=>({path:`/v1/chat/states/${encodeURIComponent(e.type)}/${encodeURIComponent(e.id)}/${encodeURIComponent(e.name)}/expiry`,headers:{},query:{},params:{type:e.type,id:e.id,name:e.name},body:{expiry:e.expiry}});var Cf=e=>({path:`/v1/chat/states/${encodeURIComponent(e.type)}/${encodeURIComponent(e.id)}/${encodeURIComponent(e.name)}`,headers:{},query:{},params:{type:e.type,id:e.id,name:e.name},body:{}});var Ef=e=>({path:`/v1/chat/states/${encodeURIComponent(e.type)}/${encodeURIComponent(e.id)}/${encodeURIComponent(e.name)}`,headers:{},query:{},params:{type:e.type,id:e.id,name:e.name},body:{payload:e.payload,expiry:e.expiry}});var Gf=e=>({path:`/v1/chat/states/${encodeURIComponent(e.type)}/${encodeURIComponent(e.id)}/${encodeURIComponent(e.name)}/get-or-set`,headers:{},query:{},params:{type:e.type,id:e.id,name:e.name},body:{payload:e.payload,expiry:e.expiry}});var Wf=e=>({path:`/v1/chat/states/${encodeURIComponent(e.type)}/${encodeURIComponent(e.id)}/${encodeURIComponent(e.name)}`,headers:{},query:{},params:{type:e.type,id:e.id,name:e.name},body:{payload:e.payload}});var Df=e=>({path:"/v1/chat/actions",headers:{},query:{},params:{},body:{type:e.type,input:e.input}});var Qf=e=>({path:"/v1/chat/integrations/configure",headers:{},query:{},params:{},body:{identifier:e.identifier,scheduleRegisterCall:e.scheduleRegisterCall,sandboxIdentifiers:e.sandboxIdentifiers}});var Of=e=>({path:`/v1/chat/tasks/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var Nf=e=>({path:"/v1/chat/tasks",headers:{},query:{},params:{},body:{title:e.title,description:e.description,type:e.type,data:e.data,parentTaskId:e.parentTaskId,conversationId:e.conversationId,userId:e.userId,timeoutAt:e.timeoutAt,tags:e.tags}});var Kf=e=>({path:`/v1/chat/tasks/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{title:e.title,description:e.description,data:e.data,timeoutAt:e.timeoutAt,status:e.status,tags:e.tags}});var $f=e=>({path:`/v1/chat/tasks/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var Jf=e=>({path:"/v1/chat/tasks",headers:{},query:{nextToken:e.nextToken,tags:e.tags,conversationId:e.conversationId,userId:e.userId,parentTaskId:e.parentTaskId,status:e.status,type:e.type},params:{},body:{}});var Xf=e=>({path:"/v1/chat/workflows",headers:{},query:{},params:{},body:{name:e.name,input:e.input,parentWorkflowId:e.parentWorkflowId,conversationId:e.conversationId,userId:e.userId,timeoutAt:e.timeoutAt,tags:e.tags,status:e.status,eventId:e.eventId}});var eb=e=>({path:`/v1/chat/workflows/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var sb=e=>({path:`/v1/chat/workflows/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{output:e.output,timeoutAt:e.timeoutAt,status:e.status,failureReason:e.failureReason,tags:e.tags,userId:e.userId,eventId:e.eventId}});var ab=e=>({path:`/v1/chat/workflows/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var ob=e=>({path:"/v1/chat/workflows",headers:{},query:{nextToken:e.nextToken,tags:e.tags,conversationId:e.conversationId,userId:e.userId,parentWorkflowId:e.parentWorkflowId,statuses:e.statuses,name:e.name},params:{},body:{}});var pb=e=>({path:"/v1/chat/workflows/get-or-create",headers:{},query:{},params:{},body:{name:e.name,input:e.input,parentWorkflowId:e.parentWorkflowId,conversationId:e.conversationId,userId:e.userId,timeoutAt:e.timeoutAt,tags:e.tags,status:e.status,eventId:e.eventId}});var ub=e=>({path:`/v1/chat/tags/${encodeURIComponent(e.key)}/values`,headers:{},query:{nextToken:e.nextToken,type:e.type},params:{key:e.key},body:{}});var lb=e=>({path:"/v1/chat/analytics",headers:{},query:{},params:{},body:{name:e.name,count:e.count}});var mb=e=>({path:"/v1/admin/helper/vrl",headers:{},query:{},params:{},body:{data:e.data,script:e.script}});var Rb=e=>({path:"/v1/admin/account/me",headers:{},query:{},params:{},body:{}});var qb=e=>({path:"/v1/admin/account/me",headers:{},query:{},params:{},body:{displayName:e.displayName,profilePicture:e.profilePicture,refresh:e.refresh}});var bb=e=>({path:"/v1/admin/account/pats",headers:{},query:{},params:{},body:{}});var Ib=e=>({path:"/v1/admin/account/pats",headers:{},query:{},params:{},body:{note:e.note}});var kb=e=>({path:`/v1/admin/account/pats/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var Ab=e=>({path:`/v1/admin/account/preferences/${encodeURIComponent(e.key)}`,headers:{},query:{},params:{key:e.key},body:{value:e.value}});var wb=e=>({path:`/v1/admin/account/preferences/${encodeURIComponent(e.key)}`,headers:{},query:{},params:{key:e.key},body:{}});var Cb=e=>({path:"/v1/admin/hub/integrations",headers:{},query:{nextToken:e.nextToken,limit:e.limit,name:e.name,version:e.version,interfaceId:e.interfaceId,interfaceName:e.interfaceName,installedByBotId:e.installedByBotId,verificationStatus:e.verificationStatus,search:e.search,sortBy:e.sortBy,direction:e.direction},params:{},body:{}});var Eb=e=>({path:`/v1/admin/hub/integrations/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var Gb=e=>({path:`/v1/admin/hub/integrations/${encodeURIComponent(e.name)}/${encodeURIComponent(e.version)}`,headers:{},query:{},params:{name:e.name,version:e.version},body:{}});var Wb=e=>({path:"/v1/admin/hub/plugins",headers:{},query:{nextToken:e.nextToken,name:e.name,version:e.version},params:{},body:{}});var Db=e=>({path:`/v1/admin/hub/plugins/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var Qb=e=>({path:`/v1/admin/hub/plugins/${encodeURIComponent(e.name)}/${encodeURIComponent(e.version)}`,headers:{},query:{},params:{name:e.name,version:e.version},body:{}});var Ob=e=>({path:`/v1/admin/hub/plugins/${encodeURIComponent(e.id)}/code/${encodeURIComponent(e.platform)}`,headers:{},query:{},params:{id:e.id,platform:e.platform},body:{}});var Nb=e=>({path:"/v1/admin/hub/interfaces",headers:{},query:{nextToken:e.nextToken,name:e.name,version:e.version},params:{},body:{}});var Kb=e=>({path:`/v1/admin/hub/interfaces/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var $b=e=>({path:`/v1/admin/hub/interfaces/${encodeURIComponent(e.name)}/${encodeURIComponent(e.version)}`,headers:{},query:{},params:{name:e.name,version:e.version},body:{}});var Jb=e=>({path:"/v1/admin/bots",headers:{},query:{},params:{},body:{states:e.states,events:e.events,recurringEvents:e.recurringEvents,subscriptions:e.subscriptions,actions:e.actions,configuration:e.configuration,user:e.user,conversation:e.conversation,message:e.message,tags:e.tags,code:e.code,name:e.name,medias:e.medias,url:e.url,dev:e.dev}});var Xb=e=>({path:`/v1/admin/bots/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{url:e.url,authentication:e.authentication,configuration:e.configuration,tags:e.tags,blocked:e.blocked,alwaysAlive:e.alwaysAlive,user:e.user,message:e.message,conversation:e.conversation,events:e.events,actions:e.actions,states:e.states,recurringEvents:e.recurringEvents,integrations:e.integrations,plugins:e.plugins,subscriptions:e.subscriptions,code:e.code,name:e.name,medias:e.medias,layers:e.layers}});var ex=e=>({path:`/v1/admin/bots/${encodeURIComponent(e.id)}/transfer`,headers:{},query:{},params:{id:e.id},body:{targetWorkspaceId:e.targetWorkspaceId}});var sx=e=>({path:"/v1/admin/bots",headers:{},query:{dev:e.dev,tags:e.tags,nextToken:e.nextToken,sortField:e.sortField,sortDirection:e.sortDirection},params:{},body:{}});var ax=e=>({path:`/v1/admin/bots/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var ox=e=>({path:`/v1/admin/bots/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var px=e=>({path:`/v1/admin/bots/${encodeURIComponent(e.id)}/logs`,headers:{},query:{timeStart:e.timeStart,timeEnd:e.timeEnd,level:e.level,userId:e.userId,workflowId:e.workflowId,conversationId:e.conversationId,messageContains:e.messageContains,nextToken:e.nextToken},params:{id:e.id},body:{}});var ux=e=>({path:`/v1/admin/bots/${encodeURIComponent(e.id)}/webchat`,headers:{},query:{type:e.type},params:{id:e.id},body:{}});var lx=e=>({path:`/v1/admin/bots/${encodeURIComponent(e.id)}/analytics`,headers:{},query:{startDate:e.startDate,endDate:e.endDate},params:{id:e.id},body:{}});var mx=e=>({path:`/v1/admin/bots/${encodeURIComponent(e.id)}/issues/${encodeURIComponent(e.issueId)}`,headers:{},query:{},params:{id:e.id,issueId:e.issueId},body:{}});var Rx=e=>({path:`/v1/admin/bots/${encodeURIComponent(e.id)}/issues`,headers:{},query:{nextToken:e.nextToken},params:{id:e.id},body:{}});var qx=e=>({path:`/v1/admin/bots/${encodeURIComponent(e.id)}/issues/${encodeURIComponent(e.issueId)}`,headers:{},query:{},params:{id:e.id,issueId:e.issueId},body:{}});var bx=e=>({path:`/v1/admin/bots/${encodeURIComponent(e.id)}/issues/${encodeURIComponent(e.issueId)}/events`,headers:{},query:{},params:{id:e.id,issueId:e.issueId},body:{}});var Ix=e=>({path:`/v1/admin/bots/${encodeURIComponent(e.id)}/versions`,headers:{},query:{},params:{id:e.id},body:{}});var kx=e=>({path:`/v1/admin/bots/${encodeURIComponent(e.id)}/versions/${encodeURIComponent(e.versionId)}`,headers:{},query:{},params:{id:e.id,versionId:e.versionId},body:{}});var Ax=e=>({path:`/v1/admin/bots/${encodeURIComponent(e.id)}/versions`,headers:{},query:{},params:{id:e.id},body:{name:e.name,description:e.description}});var wx=e=>({path:`/v1/admin/bots/${encodeURIComponent(e.id)}/versions/deploy`,headers:{},query:{},params:{id:e.id},body:{versionId:e.versionId}});var Cx=e=>({path:`/v1/admin/bots/${encodeURIComponent(e.botId)}/integrations/${encodeURIComponent(e.integrationId)}/shareable-id`,headers:{},query:{},params:{botId:e.botId,integrationId:e.integrationId},body:{}});var Ex=e=>({path:`/v1/admin/bots/${encodeURIComponent(e.botId)}/integrations/${encodeURIComponent(e.integrationId)}/shareable-id`,headers:{},query:{},params:{botId:e.botId,integrationId:e.integrationId},body:{}});var Gx=e=>({path:`/v1/admin/bots/${encodeURIComponent(e.botId)}/integrations/${encodeURIComponent(e.integrationId)}/shareable-id`,headers:{},query:{},params:{botId:e.botId,integrationId:e.integrationId},body:{}});var Wx=e=>({path:`/v1/admin/bots/${encodeURIComponent(e.botId)}/integrations/${encodeURIComponent(e.integrationId)}/sandboxed-conversations`,headers:{},query:{},params:{botId:e.botId,integrationId:e.integrationId},body:{}});var Dx=e=>({path:"/v1/admin/bots/baks",headers:{},query:{botId:e.botId},params:{},body:{}});var Qx=e=>({path:"/v1/admin/bots/baks",headers:{},query:{},params:{},body:{botId:e.botId,note:e.note}});var Ox=e=>({path:`/v1/admin/bots/baks/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var Nx=e=>({path:`/v1/admin/workspaces/${encodeURIComponent(e.id)}/billing/invoices`,headers:{},query:{},params:{id:e.id},body:{}});var Kx=e=>({path:`/v1/admin/workspaces/${encodeURIComponent(e.id)}/billing/upcoming-invoice`,headers:{},query:{},params:{id:e.id},body:{}});var $x=e=>({path:`/v1/admin/workspaces/${encodeURIComponent(e.id)}/billing/invoices/charge-unpaid`,headers:{},query:{},params:{id:e.id},body:{invoiceIds:e.invoiceIds}});var Jx=e=>({path:"/v1/admin/workspaces",headers:{},query:{},params:{},body:{name:e.name}});var Xx=e=>({path:`/v1/admin/workspaces/${encodeURIComponent(e.id)}/public`,headers:{},query:{},params:{id:e.id},body:{}});var eI=e=>({path:`/v1/admin/workspaces/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var sI=e=>({path:`/v1/admin/workspaces/${encodeURIComponent(e.id)}/usages`,headers:{},query:{type:e.type,period:e.period},params:{id:e.id},body:{}});var aI=e=>({path:`/v1/admin/workspaces/${encodeURIComponent(e.id)}/usages/by-bot`,headers:{},query:{type:e.type,period:e.period},params:{id:e.id},body:{}});var oI=e=>({path:"/v1/admin/workspaces/usages/quota-completion",headers:{},query:{},params:{},body:{}});var pI=e=>({path:`/v1/admin/workspaces/${encodeURIComponent(e.id)}/quota`,headers:{},query:{type:e.type,period:e.period},params:{id:e.id},body:{}});var uI=e=>({path:`/v1/admin/workspaces/${encodeURIComponent(e.id)}/quotas`,headers:{},query:{period:e.period},params:{id:e.id},body:{}});var lI=e=>({path:`/v1/admin/workspaces/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{name:e.name,spendingLimit:e.spendingLimit,about:e.about,profilePicture:e.profilePicture,contactEmail:e.contactEmail,website:e.website,socialAccounts:e.socialAccounts,isPublic:e.isPublic,handle:e.handle}});var mI=e=>({path:"/v1/admin/workspaces/handle-availability",headers:{},query:{},params:{},body:{handle:e.handle}});var RI=e=>({path:"/v1/admin/workspaces",headers:{},query:{nextToken:e.nextToken,handle:e.handle},params:{},body:{}});var qI=e=>({path:"/v1/admin/workspaces/public",headers:{},query:{nextToken:e.nextToken,workspaceIds:e.workspaceIds,search:e.search},params:{},body:{}});var bI=e=>({path:`/v1/admin/workspaces/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var II=e=>({path:`/v1/admin/workspaces/${encodeURIComponent(e.id)}/audit-records`,headers:{},query:{nextToken:e.nextToken},params:{id:e.id},body:{}});var kI=e=>({path:"/v1/admin/workspace-members",headers:{},query:{nextToken:e.nextToken},params:{},body:{}});var AI=e=>({path:"/v1/admin/workspace-members/me",headers:{},query:{},params:{},body:{}});var wI=e=>({path:`/v1/admin/workspace-members/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var CI=e=>({path:"/v1/admin/workspace-members",headers:{},query:{},params:{},body:{email:e.email,role:e.role}});var EI=e=>({path:`/v1/admin/workspace-members/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{role:e.role}});var GI=e=>({path:"/v1/admin/integrations/iaks",headers:{},query:{integrationId:e.integrationId},params:{},body:{}});var WI=e=>({path:"/v1/admin/integrations/iaks",headers:{},query:{},params:{},body:{integrationId:e.integrationId,note:e.note}});var DI=e=>({path:`/v1/admin/integrations/iaks/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var QI=e=>({path:"/v1/admin/integrations",headers:{},query:{},params:{},body:{name:e.name,version:e.version,configuration:e.configuration,configurations:e.configurations,states:e.states,events:e.events,actions:e.actions,entities:e.entities,attributes:e.attributes,identifier:e.identifier,channels:e.channels,user:e.user,interfaces:e.interfaces,secrets:e.secrets,code:e.code,url:e.url,dev:e.dev,icon:e.icon,readme:e.readme,title:e.title,description:e.description,public:e.public,layers:e.layers}});var OI=e=>({path:"/v1/admin/integrations/validate",headers:{},query:{},params:{},body:{name:e.name,version:e.version,configuration:e.configuration,configurations:e.configurations,states:e.states,events:e.events,actions:e.actions,entities:e.entities,attributes:e.attributes,identifier:e.identifier,channels:e.channels,user:e.user,interfaces:e.interfaces,secrets:e.secrets,code:e.code,url:e.url,dev:e.dev,icon:e.icon,readme:e.readme,title:e.title,description:e.description,public:e.public,layers:e.layers}});var NI=e=>({path:`/v1/admin/integrations/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{configuration:e.configuration,configurations:e.configurations,channels:e.channels,identifier:e.identifier,actions:e.actions,events:e.events,states:e.states,user:e.user,entities:e.entities,interfaces:e.interfaces,sandbox:e.sandbox,attributes:e.attributes,secrets:e.secrets,code:e.code,icon:e.icon,readme:e.readme,title:e.title,description:e.description,url:e.url,public:e.public,layers:e.layers}});var KI=e=>({path:`/v1/admin/integrations/${encodeURIComponent(e.id)}/validate`,headers:{},query:{},params:{id:e.id},body:{configuration:e.configuration,configurations:e.configurations,channels:e.channels,identifier:e.identifier,actions:e.actions,events:e.events,states:e.states,user:e.user,entities:e.entities,interfaces:e.interfaces,sandbox:e.sandbox,attributes:e.attributes,secrets:e.secrets,code:e.code,icon:e.icon,readme:e.readme,title:e.title,description:e.description,url:e.url,public:e.public,layers:e.layers}});var $I=e=>({path:"/v1/admin/integrations",headers:{},query:{nextToken:e.nextToken,limit:e.limit,name:e.name,version:e.version,interfaceId:e.interfaceId,interfaceName:e.interfaceName,installedByBotId:e.installedByBotId,verificationStatus:e.verificationStatus,search:e.search,sortBy:e.sortBy,direction:e.direction,visibility:e.visibility,dev:e.dev},params:{},body:{}});var JI=e=>({path:`/v1/admin/integrations/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var XI=e=>({path:`/v1/admin/integrations/${encodeURIComponent(e.id)}/logs`,headers:{},query:{timeStart:e.timeStart,timeEnd:e.timeEnd,level:e.level,userId:e.userId,conversationId:e.conversationId,messageContains:e.messageContains,nextToken:e.nextToken},params:{id:e.id},body:{}});var ev=e=>({path:`/v1/admin/integrations/${encodeURIComponent(e.name)}/${encodeURIComponent(e.version)}`,headers:{},query:{},params:{name:e.name,version:e.version},body:{}});var sv=e=>({path:`/v1/admin/integrations/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var av=e=>({path:"/v1/admin/integrations/request-verification",headers:{},query:{},params:{},body:{integrationId:e.integrationId}});var ov=e=>({path:"/v1/admin/interfaces",headers:{},query:{},params:{},body:{name:e.name,version:e.version,entities:e.entities,events:e.events,actions:e.actions,channels:e.channels,nameTemplate:e.nameTemplate,attributes:e.attributes,icon:e.icon,readme:e.readme,title:e.title,description:e.description,public:e.public}});var pv=e=>({path:`/v1/admin/interfaces/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var uv=e=>({path:`/v1/admin/interfaces/${encodeURIComponent(e.name)}/${encodeURIComponent(e.version)}`,headers:{},query:{},params:{name:e.name,version:e.version},body:{}});var lv=e=>({path:`/v1/admin/interfaces/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{entities:e.entities,events:e.events,actions:e.actions,channels:e.channels,nameTemplate:e.nameTemplate,attributes:e.attributes,icon:e.icon,readme:e.readme,title:e.title,description:e.description,public:e.public}});var mv=e=>({path:`/v1/admin/interfaces/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var Rv=e=>({path:"/v1/admin/interfaces",headers:{},query:{nextToken:e.nextToken,name:e.name,version:e.version},params:{},body:{}});var qv=e=>({path:"/v1/admin/plugins",headers:{},query:{},params:{},body:{name:e.name,version:e.version,configuration:e.configuration,states:e.states,events:e.events,actions:e.actions,user:e.user,conversation:e.conversation,dependencies:e.dependencies,attributes:e.attributes,code:e.code,icon:e.icon,readme:e.readme,title:e.title,description:e.description,public:e.public}});var bv=e=>({path:`/v1/admin/plugins/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var Iv=e=>({path:`/v1/admin/plugins/${encodeURIComponent(e.name)}/${encodeURIComponent(e.version)}`,headers:{},query:{},params:{name:e.name,version:e.version},body:{}});var kv=e=>({path:`/v1/admin/plugins/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{configuration:e.configuration,states:e.states,events:e.events,actions:e.actions,user:e.user,conversation:e.conversation,dependencies:e.dependencies,attributes:e.attributes,code:e.code,icon:e.icon,readme:e.readme,title:e.title,description:e.description,public:e.public}});var Av=e=>({path:`/v1/admin/plugins/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var wv=e=>({path:"/v1/admin/plugins",headers:{},query:{nextToken:e.nextToken,name:e.name,version:e.version},params:{},body:{}});var Cv=e=>({path:`/v1/admin/plugins/${encodeURIComponent(e.id)}/code/${encodeURIComponent(e.platform)}`,headers:{},query:{},params:{id:e.id,platform:e.platform},body:{}});var Ev=e=>({path:`/v1/admin/usages/${encodeURIComponent(e.id)}`,headers:{},query:{type:e.type,period:e.period},params:{id:e.id},body:{}});var Gv=e=>({path:"/v1/admin/usages/multiple",headers:{},query:{types:e.types,ids:e.ids,period:e.period},params:{},body:{}});var Wv=e=>({path:`/v1/admin/usages/${encodeURIComponent(e.id)}/history`,headers:{},query:{type:e.type},params:{id:e.id},body:{}});var Dv=e=>({path:`/v1/admin/usages/${encodeURIComponent(e.id)}/activity`,headers:{},query:{type:e.type,timestampFrom:e.timestampFrom,timestampUntil:e.timestampUntil,nextToken:e.nextToken},params:{id:e.id},body:{}});var Qv=e=>({path:`/v1/admin/usages/${encodeURIComponent(e.id)}/daily-activity`,headers:{},query:{type:e.type,dateFrom:e.dateFrom,dateUntil:e.dateUntil,nextToken:e.nextToken},params:{id:e.id},body:{}});var Ov=e=>({path:"/v1/admin/quotas/ai-spend",headers:{},query:{},params:{},body:{monthlySpendingLimit:e.monthlySpendingLimit}});var Nv=e=>({path:"/v1/admin/activities",headers:{},query:{nextToken:e.nextToken,taskId:e.taskId,botId:e.botId},params:{},body:{}});var Kv=e=>({path:"/v1/admin/introspect",headers:{},query:{},params:{},body:{botId:e.botId}});var $v=e=>({path:"/v1/files",headers:{},query:{},params:{},body:{key:e.key,tags:e.tags,size:e.size,index:e.index,indexing:e.indexing,accessPolicies:e.accessPolicies,contentType:e.contentType,expiresAt:e.expiresAt,publicContentImmediatelyAccessible:e.publicContentImmediatelyAccessible,metadata:e.metadata}});var Jv=e=>({path:`/v1/files/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var Xv=e=>({path:"/v1/files",headers:{},query:{nextToken:e.nextToken,sortField:e.sortField,sortDirection:e.sortDirection,tags:e.tags,ids:e.ids},params:{},body:{}});var ek=e=>({path:`/v1/files/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var sk=e=>({path:`/v1/files/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{metadata:e.metadata,tags:e.tags,accessPolicies:e.accessPolicies,expiresAt:e.expiresAt}});var ak=e=>({path:`/v1/files/${encodeURIComponent(e.idOrKey)}/${encodeURIComponent(e.destinationKey)}`,headers:{"x-destination-bot-id":e["x-destination-bot-id"]},query:{},params:{idOrKey:e.idOrKey,destinationKey:e.destinationKey},body:{overwrite:e.overwrite}});var ok=e=>({path:"/v1/files/search",headers:{},query:{tags:e.tags,query:e.query,contextDepth:e.contextDepth,limit:e.limit,consolidate:e.consolidate,includeBreadcrumb:e.includeBreadcrumb},params:{},body:{}});var pk=e=>({path:`/v1/files/${encodeURIComponent(e.id)}/passages`,headers:{},query:{nextToken:e.nextToken,limit:e.limit},params:{id:e.id},body:{}});var uk=e=>({path:`/v1/files/${encodeURIComponent(e.id)}/passages`,headers:{},query:{},params:{id:e.id},body:{passages:e.passages}});var lk=e=>({path:"/v1/files/tags",headers:{},query:{nextToken:e.nextToken},params:{},body:{}});var mk=e=>({path:`/v1/files/tags/${encodeURIComponent(e.tag)}/values`,headers:{},query:{nextToken:e.nextToken},params:{tag:e.tag},body:{}});var Rk=e=>({path:"/v1/files/knowledge-bases",headers:{},query:{},params:{},body:{name:e.name}});var qk=e=>({path:`/v1/files/knowledge-bases/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{}});var bk=e=>({path:`/v1/files/knowledge-bases/${encodeURIComponent(e.id)}`,headers:{},query:{},params:{id:e.id},body:{name:e.name}});var Ik=e=>({path:"/v1/files/knowledge-bases",headers:{},query:{nextToken:e.nextToken},params:{},body:{}});var kk=e=>({path:"/v1/tables",headers:{},query:{tags:e.tags},params:{},body:{}});var Ak=e=>({path:`/v1/tables/${encodeURIComponent(e.table)}`,headers:{},query:{},params:{table:e.table},body:{}});var wk=e=>({path:`/v1/tables/${encodeURIComponent(e.table)}`,headers:{},query:{},params:{table:e.table},body:{factor:e.factor,frozen:e.frozen,schema:e.schema,tags:e.tags,isComputeEnabled:e.isComputeEnabled}});var Ck=e=>({path:"/v1/tables",headers:{},query:{},params:{},body:{name:e.name,factor:e.factor,frozen:e.frozen,schema:e.schema,tags:e.tags,isComputeEnabled:e.isComputeEnabled}});var Ek=e=>({path:`/v1/tables/${encodeURIComponent(e.sourceTableId)}/duplicate`,headers:{},query:{},params:{sourceTableId:e.sourceTableId},body:{tableName:e.tableName,schemaOnly:e.schemaOnly,factor:e.factor}});var Gk=e=>({path:`/v1/tables/${encodeURIComponent(e.table)}/export`,headers:{},query:{format:e.format,compress:e.compress},params:{table:e.table},body:{}});var Wk=e=>({path:`/v1/tables/${encodeURIComponent(e.table)}/jobs`,headers:{},query:{},params:{table:e.table},body:{}});var Dk=e=>({path:`/v1/tables/${encodeURIComponent(e.table)}/import`,headers:{},query:{},params:{table:e.table},body:{fileId:e.fileId}});var Qk=e=>({path:`/v1/tables/${encodeURIComponent(e.table)}`,headers:{},query:{},params:{table:e.table},body:{name:e.name,frozen:e.frozen,schema:e.schema,tags:e.tags,isComputeEnabled:e.isComputeEnabled}});var Ok=e=>({path:`/v1/tables/${encodeURIComponent(e.table)}/column`,headers:{},query:{},params:{table:e.table},body:{name:e.name,newName:e.newName}});var Nk=e=>({path:`/v1/tables/${encodeURIComponent(e.table)}`,headers:{},query:{},params:{table:e.table},body:{}});var Kk=e=>({path:`/v1/tables/${encodeURIComponent(e.table)}/row`,headers:{},query:{id:e.id},params:{table:e.table},body:{}});var $k=e=>({path:`/v1/tables/${encodeURIComponent(e.table)}/rows/find`,headers:{},query:{},params:{table:e.table},body:{limit:e.limit,offset:e.offset,filter:e.filter,group:e.group,search:e.search,orderBy:e.orderBy,orderDirection:e.orderDirection}});var Jk=e=>({path:`/v1/tables/${encodeURIComponent(e.table)}/rows`,headers:{},query:{},params:{table:e.table},body:{rows:e.rows,waitComputed:e.waitComputed}});var Xk=e=>({path:`/v1/tables/${encodeURIComponent(e.table)}/rows/delete`,headers:{},query:{},params:{table:e.table},body:{ids:e.ids,filter:e.filter,deleteAllRows:e.deleteAllRows}});var eP=e=>({path:`/v1/tables/${encodeURIComponent(e.table)}/rows`,headers:{},query:{},params:{table:e.table},body:{rows:e.rows,waitComputed:e.waitComputed}});var sP=e=>({path:`/v1/tables/${encodeURIComponent(e.table)}/rows/upsert`,headers:{},query:{},params:{table:e.table},body:{rows:e.rows,keyColumn:e.keyColumn,waitComputed:e.waitComputed}});var yr=class{constructor(t,s={}){this.axiosInstance=t;this.props=s}createConversation=async t=>{let{path:s,headers:r,query:n,body:o}=Uq(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"post",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};getConversation=async t=>{let{path:s,headers:r,query:n,body:o}=Lq(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};listConversations=async t=>{let{path:s,headers:r,query:n,body:o}=Sq(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};getOrCreateConversation=async t=>{let{path:s,headers:r,query:n,body:o}=_q(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"post",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};updateConversation=async t=>{let{path:s,headers:r,query:n,body:o}=Hq(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"put",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};deleteConversation=async t=>{let{path:s,headers:r,query:n,body:o}=Fq(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"delete",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};listParticipants=async t=>{let{path:s,headers:r,query:n,body:o}=Mq(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};addParticipant=async t=>{let{path:s,headers:r,query:n,body:o}=Vq(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"post",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};getParticipant=async t=>{let{path:s,headers:r,query:n,body:o}=jq(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};removeParticipant=async t=>{let{path:s,headers:r,query:n,body:o}=zq(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"delete",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};createEvent=async t=>{let{path:s,headers:r,query:n,body:o}=Yq(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"post",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};getEvent=async t=>{let{path:s,headers:r,query:n,body:o}=Zq(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};listEvents=async t=>{let{path:s,headers:r,query:n,body:o}=tf(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};createMessage=async t=>{let{path:s,headers:r,query:n,body:o}=rf(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"post",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};getOrCreateMessage=async t=>{let{path:s,headers:r,query:n,body:o}=nf(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"post",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};getMessage=async t=>{let{path:s,headers:r,query:n,body:o}=pf(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};updateMessage=async t=>{let{path:s,headers:r,query:n,body:o}=uf(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"put",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};listMessages=async t=>{let{path:s,headers:r,query:n,body:o}=lf(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};deleteMessage=async t=>{let{path:s,headers:r,query:n,body:o}=mf(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"delete",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};createUser=async t=>{let{path:s,headers:r,query:n,body:o}=Rf(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"post",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};getUser=async t=>{let{path:s,headers:r,query:n,body:o}=qf(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};listUsers=async t=>{let{path:s,headers:r,query:n,body:o}=bf(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};getOrCreateUser=async t=>{let{path:s,headers:r,query:n,body:o}=If(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"post",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};updateUser=async t=>{let{path:s,headers:r,query:n,body:o}=kf(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"put",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};deleteUser=async t=>{let{path:s,headers:r,query:n,body:o}=Af(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"delete",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};setStateExpiry=async t=>{let{path:s,headers:r,query:n,body:o}=wf(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"post",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};getState=async t=>{let{path:s,headers:r,query:n,body:o}=Cf(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};setState=async t=>{let{path:s,headers:r,query:n,body:o}=Ef(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"post",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};getOrSetState=async t=>{let{path:s,headers:r,query:n,body:o}=Gf(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"post",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};patchState=async t=>{let{path:s,headers:r,query:n,body:o}=Wf(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"patch",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};callAction=async t=>{let{path:s,headers:r,query:n,body:o}=Df(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"post",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};configureIntegration=async t=>{let{path:s,headers:r,query:n,body:o}=Qf(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"post",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};getTask=async t=>{let{path:s,headers:r,query:n,body:o}=Of(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};createTask=async t=>{let{path:s,headers:r,query:n,body:o}=Nf(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"post",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};updateTask=async t=>{let{path:s,headers:r,query:n,body:o}=Kf(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"put",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};deleteTask=async t=>{let{path:s,headers:r,query:n,body:o}=$f(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"delete",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};listTasks=async t=>{let{path:s,headers:r,query:n,body:o}=Jf(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};createWorkflow=async t=>{let{path:s,headers:r,query:n,body:o}=Xf(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"post",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};getWorkflow=async t=>{let{path:s,headers:r,query:n,body:o}=eb(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};updateWorkflow=async t=>{let{path:s,headers:r,query:n,body:o}=sb(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"put",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};deleteWorkflow=async t=>{let{path:s,headers:r,query:n,body:o}=ab(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"delete",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};listWorkflows=async t=>{let{path:s,headers:r,query:n,body:o}=ob(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};getOrCreateWorkflow=async t=>{let{path:s,headers:r,query:n,body:o}=pb(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"post",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};listTagValues=async t=>{let{path:s,headers:r,query:n,body:o}=ub(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};trackAnalytics=async t=>{let{path:s,headers:r,query:n,body:o}=lb(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"post",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};runVrl=async t=>{let{path:s,headers:r,query:n,body:o}=mb(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"post",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};getAccount=async t=>{let{path:s,headers:r,query:n,body:o}=Rb(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};updateAccount=async t=>{let{path:s,headers:r,query:n,body:o}=qb(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"put",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};listPersonalAccessTokens=async t=>{let{path:s,headers:r,query:n,body:o}=bb(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};createPersonalAccessToken=async t=>{let{path:s,headers:r,query:n,body:o}=Ib(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"post",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};deletePersonalAccessToken=async t=>{let{path:s,headers:r,query:n,body:o}=kb(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"delete",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};setAccountPreference=async t=>{let{path:s,headers:r,query:n,body:o}=Ab(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"post",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};getAccountPreference=async t=>{let{path:s,headers:r,query:n,body:o}=wb(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};listPublicIntegrations=async t=>{let{path:s,headers:r,query:n,body:o}=Cb(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};getPublicIntegrationById=async t=>{let{path:s,headers:r,query:n,body:o}=Eb(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};getPublicIntegration=async t=>{let{path:s,headers:r,query:n,body:o}=Gb(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};listPublicPlugins=async t=>{let{path:s,headers:r,query:n,body:o}=Wb(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};getPublicPluginById=async t=>{let{path:s,headers:r,query:n,body:o}=Db(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};getPublicPlugin=async t=>{let{path:s,headers:r,query:n,body:o}=Qb(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};getPublicPluginCode=async t=>{let{path:s,headers:r,query:n,body:o}=Ob(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};listPublicInterfaces=async t=>{let{path:s,headers:r,query:n,body:o}=Nb(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};getPublicInterfaceById=async t=>{let{path:s,headers:r,query:n,body:o}=Kb(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};getPublicInterface=async t=>{let{path:s,headers:r,query:n,body:o}=$b(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};createBot=async t=>{let{path:s,headers:r,query:n,body:o}=Jb(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"post",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};updateBot=async t=>{let{path:s,headers:r,query:n,body:o}=Xb(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"put",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};transferBot=async t=>{let{path:s,headers:r,query:n,body:o}=ex(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"post",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};listBots=async t=>{let{path:s,headers:r,query:n,body:o}=sx(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};getBot=async t=>{let{path:s,headers:r,query:n,body:o}=ax(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};deleteBot=async t=>{let{path:s,headers:r,query:n,body:o}=ox(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"delete",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};getBotLogs=async t=>{let{path:s,headers:r,query:n,body:o}=px(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};getBotWebchat=async t=>{let{path:s,headers:r,query:n,body:o}=ux(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};getBotAnalytics=async t=>{let{path:s,headers:r,query:n,body:o}=lx(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};getBotIssue=async t=>{let{path:s,headers:r,query:n,body:o}=mx(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};listBotIssues=async t=>{let{path:s,headers:r,query:n,body:o}=Rx(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};deleteBotIssue=async t=>{let{path:s,headers:r,query:n,body:o}=qx(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"delete",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};listBotIssueEvents=async t=>{let{path:s,headers:r,query:n,body:o}=bx(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};listBotVersions=async t=>{let{path:s,headers:r,query:n,body:o}=Ix(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};getBotVersion=async t=>{let{path:s,headers:r,query:n,body:o}=kx(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};createBotVersion=async t=>{let{path:s,headers:r,query:n,body:o}=Ax(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"post",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};deployBotVersion=async t=>{let{path:s,headers:r,query:n,body:o}=wx(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"post",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};createIntegrationShareableId=async t=>{let{path:s,headers:r,query:n,body:o}=Cx(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"post",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};deleteIntegrationShareableId=async t=>{let{path:s,headers:r,query:n,body:o}=Ex(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"delete",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};getIntegrationShareableId=async t=>{let{path:s,headers:r,query:n,body:o}=Gx(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};unlinkSandboxedConversations=async t=>{let{path:s,headers:r,query:n,body:o}=Wx(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"delete",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};listBotApiKeys=async t=>{let{path:s,headers:r,query:n,body:o}=Dx(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};createBotApiKey=async t=>{let{path:s,headers:r,query:n,body:o}=Qx(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"post",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};deleteBotApiKey=async t=>{let{path:s,headers:r,query:n,body:o}=Ox(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"delete",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};listWorkspaceInvoices=async t=>{let{path:s,headers:r,query:n,body:o}=Nx(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};getUpcomingInvoice=async t=>{let{path:s,headers:r,query:n,body:o}=Kx(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};chargeWorkspaceUnpaidInvoices=async t=>{let{path:s,headers:r,query:n,body:o}=$x(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"post",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};createWorkspace=async t=>{let{path:s,headers:r,query:n,body:o}=Jx(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"post",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};getPublicWorkspace=async t=>{let{path:s,headers:r,query:n,body:o}=Xx(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};getWorkspace=async t=>{let{path:s,headers:r,query:n,body:o}=eI(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};listWorkspaceUsages=async t=>{let{path:s,headers:r,query:n,body:o}=sI(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};breakDownWorkspaceUsageByBot=async t=>{let{path:s,headers:r,query:n,body:o}=aI(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};getAllWorkspaceQuotaCompletion=async t=>{let{path:s,headers:r,query:n,body:o}=oI(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};getWorkspaceQuota=async t=>{let{path:s,headers:r,query:n,body:o}=pI(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};listWorkspaceQuotas=async t=>{let{path:s,headers:r,query:n,body:o}=uI(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};updateWorkspace=async t=>{let{path:s,headers:r,query:n,body:o}=lI(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"put",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};checkHandleAvailability=async t=>{let{path:s,headers:r,query:n,body:o}=mI(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"put",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};listWorkspaces=async t=>{let{path:s,headers:r,query:n,body:o}=RI(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};listPublicWorkspaces=async t=>{let{path:s,headers:r,query:n,body:o}=qI(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};deleteWorkspace=async t=>{let{path:s,headers:r,query:n,body:o}=bI(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"delete",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};getAuditRecords=async t=>{let{path:s,headers:r,query:n,body:o}=II(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};listWorkspaceMembers=async t=>{let{path:s,headers:r,query:n,body:o}=kI(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};getWorkspaceMember=async t=>{let{path:s,headers:r,query:n,body:o}=AI(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};deleteWorkspaceMember=async t=>{let{path:s,headers:r,query:n,body:o}=wI(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"delete",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};createWorkspaceMember=async t=>{let{path:s,headers:r,query:n,body:o}=CI(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"post",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};updateWorkspaceMember=async t=>{let{path:s,headers:r,query:n,body:o}=EI(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"put",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};listIntegrationApiKeys=async t=>{let{path:s,headers:r,query:n,body:o}=GI(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};createIntegrationApiKey=async t=>{let{path:s,headers:r,query:n,body:o}=WI(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"post",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};deleteIntegrationApiKey=async t=>{let{path:s,headers:r,query:n,body:o}=DI(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"delete",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};createIntegration=async t=>{let{path:s,headers:r,query:n,body:o}=QI(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"post",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};validateIntegrationCreation=async t=>{let{path:s,headers:r,query:n,body:o}=OI(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"post",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};updateIntegration=async t=>{let{path:s,headers:r,query:n,body:o}=NI(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"put",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};validateIntegrationUpdate=async t=>{let{path:s,headers:r,query:n,body:o}=KI(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"put",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};listIntegrations=async t=>{let{path:s,headers:r,query:n,body:o}=$I(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};getIntegration=async t=>{let{path:s,headers:r,query:n,body:o}=JI(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};getIntegrationLogs=async t=>{let{path:s,headers:r,query:n,body:o}=XI(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};getIntegrationByName=async t=>{let{path:s,headers:r,query:n,body:o}=ev(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};deleteIntegration=async t=>{let{path:s,headers:r,query:n,body:o}=sv(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"delete",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};requestIntegrationVerification=async t=>{let{path:s,headers:r,query:n,body:o}=av(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"post",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};createInterface=async t=>{let{path:s,headers:r,query:n,body:o}=ov(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"post",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};getInterface=async t=>{let{path:s,headers:r,query:n,body:o}=pv(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};getInterfaceByName=async t=>{let{path:s,headers:r,query:n,body:o}=uv(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};updateInterface=async t=>{let{path:s,headers:r,query:n,body:o}=lv(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"put",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};deleteInterface=async t=>{let{path:s,headers:r,query:n,body:o}=mv(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"delete",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};listInterfaces=async t=>{let{path:s,headers:r,query:n,body:o}=Rv(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};createPlugin=async t=>{let{path:s,headers:r,query:n,body:o}=qv(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"post",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};getPlugin=async t=>{let{path:s,headers:r,query:n,body:o}=bv(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};getPluginByName=async t=>{let{path:s,headers:r,query:n,body:o}=Iv(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};updatePlugin=async t=>{let{path:s,headers:r,query:n,body:o}=kv(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"put",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};deletePlugin=async t=>{let{path:s,headers:r,query:n,body:o}=Av(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"delete",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};listPlugins=async t=>{let{path:s,headers:r,query:n,body:o}=wv(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};getPluginCode=async t=>{let{path:s,headers:r,query:n,body:o}=Cv(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};getUsage=async t=>{let{path:s,headers:r,query:n,body:o}=Ev(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};getMultipleUsages=async t=>{let{path:s,headers:r,query:n,body:o}=Gv(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};listUsageHistory=async t=>{let{path:s,headers:r,query:n,body:o}=Wv(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};listUsageActivity=async t=>{let{path:s,headers:r,query:n,body:o}=Dv(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};listUsageActivityDaily=async t=>{let{path:s,headers:r,query:n,body:o}=Qv(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};changeAISpendQuota=async t=>{let{path:s,headers:r,query:n,body:o}=Ov(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"put",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};listActivities=async t=>{let{path:s,headers:r,query:n,body:o}=Nv(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};introspect=async t=>{let{path:s,headers:r,query:n,body:o}=Kv(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"post",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};upsertFile=async t=>{let{path:s,headers:r,query:n,body:o}=$v(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"put",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};deleteFile=async t=>{let{path:s,headers:r,query:n,body:o}=Jv(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"delete",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};listFiles=async t=>{let{path:s,headers:r,query:n,body:o}=Xv(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};getFile=async t=>{let{path:s,headers:r,query:n,body:o}=ek(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};updateFileMetadata=async t=>{let{path:s,headers:r,query:n,body:o}=sk(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"put",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};copyFile=async t=>{let{path:s,headers:r,query:n,body:o}=ak(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"post",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};searchFiles=async t=>{let{path:s,headers:r,query:n,body:o}=ok(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};listFilePassages=async t=>{let{path:s,headers:r,query:n,body:o}=pk(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};setFilePassages=async t=>{let{path:s,headers:r,query:n,body:o}=uk(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"put",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};listFileTags=async t=>{let{path:s,headers:r,query:n,body:o}=lk(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};listFileTagValues=async t=>{let{path:s,headers:r,query:n,body:o}=mk(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};createKnowledgeBase=async t=>{let{path:s,headers:r,query:n,body:o}=Rk(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"post",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};deleteKnowledgeBase=async t=>{let{path:s,headers:r,query:n,body:o}=qk(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"delete",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};updateKnowledgeBase=async t=>{let{path:s,headers:r,query:n,body:o}=bk(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"put",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};listKnowledgeBases=async t=>{let{path:s,headers:r,query:n,body:o}=Ik(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};listTables=async t=>{let{path:s,headers:r,query:n,body:o}=kk(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};getTable=async t=>{let{path:s,headers:r,query:n,body:o}=Ak(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};getOrCreateTable=async t=>{let{path:s,headers:r,query:n,body:o}=wk(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"post",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};createTable=async t=>{let{path:s,headers:r,query:n,body:o}=Ck(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"post",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};duplicateTable=async t=>{let{path:s,headers:r,query:n,body:o}=Ek(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"post",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};exportTable=async t=>{let{path:s,headers:r,query:n,body:o}=Gk(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};getTableJobs=async t=>{let{path:s,headers:r,query:n,body:o}=Wk(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};importTable=async t=>{let{path:s,headers:r,query:n,body:o}=Dk(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"post",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};updateTable=async t=>{let{path:s,headers:r,query:n,body:o}=Qk(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"put",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};renameTableColumn=async t=>{let{path:s,headers:r,query:n,body:o}=Ok(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"put",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};deleteTable=async t=>{let{path:s,headers:r,query:n,body:o}=Nk(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"delete",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};getTableRow=async t=>{let{path:s,headers:r,query:n,body:o}=Kk(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"get",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};findTableRows=async t=>{let{path:s,headers:r,query:n,body:o}=$k(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"post",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};createTableRows=async t=>{let{path:s,headers:r,query:n,body:o}=Jk(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"post",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};deleteTableRows=async t=>{let{path:s,headers:r,query:n,body:o}=Xk(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"post",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};updateTableRows=async t=>{let{path:s,headers:r,query:n,body:o}=eP(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"put",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})};upsertTableRows=async t=>{let{path:s,headers:r,query:n,body:o}=sP(t),i=this.props.toAxiosRequest??d,p=this.props.toApiError??l,c=i({method:"post",path:s,headers:{...r},query:{...n},body:o});return this.axiosInstance.request(c).then(a=>a.data).catch(a=>{throw p(a)})}};function l(e){return H.isAxiosError(e)&&e.response?.data?nt(e.response.data):nt(e)}var zo=class extends yr{config;constructor(t={}){let s=be.getClientConfig(t),r=xe.createAxios(s),n=H.create(r);super(n,{toApiError:Ie.toApiError}),t.retry&&Ae(n,t.retry),this.config=s}get list(){return{conversations:t=>new B.AsyncCollection(({nextToken:s})=>this.listConversations({nextToken:s,...t}).then(r=>({...r,items:r.conversations}))),participants:t=>new B.AsyncCollection(({nextToken:s})=>this.listParticipants({nextToken:s,...t}).then(r=>({...r,items:r.participants}))),events:t=>new B.AsyncCollection(({nextToken:s})=>this.listEvents({nextToken:s,...t}).then(r=>({...r,items:r.events}))),messages:t=>new B.AsyncCollection(({nextToken:s})=>this.listMessages({nextToken:s,...t}).then(r=>({...r,items:r.messages}))),users:t=>new B.AsyncCollection(({nextToken:s})=>this.listUsers({nextToken:s,...t}).then(r=>({...r,items:r.users}))),tasks:t=>new B.AsyncCollection(({nextToken:s})=>this.listTasks({nextToken:s,...t}).then(r=>({...r,items:r.tasks}))),publicIntegrations:t=>new B.AsyncCollection(({nextToken:s})=>this.listPublicIntegrations({nextToken:s,...t}).then(r=>({...r,items:r.integrations}))),bots:t=>new B.AsyncCollection(({nextToken:s})=>this.listBots({nextToken:s,...t}).then(r=>({...r,items:r.bots}))),botIssues:t=>new B.AsyncCollection(({nextToken:s})=>this.listBotIssues({nextToken:s,...t}).then(r=>({...r,items:r.issues}))),workspaces:t=>new B.AsyncCollection(({nextToken:s})=>this.listWorkspaces({nextToken:s,...t}).then(r=>({...r,items:r.workspaces}))),publicWorkspaces:t=>new B.AsyncCollection(({nextToken:s})=>this.listPublicWorkspaces({nextToken:s,...t}).then(r=>({...r,items:r.workspaces}))),workspaceMembers:t=>new B.AsyncCollection(({nextToken:s})=>this.listWorkspaceMembers({nextToken:s,...t}).then(r=>({...r,items:r.members}))),integrations:t=>new B.AsyncCollection(({nextToken:s})=>this.listIntegrations({nextToken:s,...t}).then(r=>({...r,items:r.integrations}))),interfaces:t=>new B.AsyncCollection(({nextToken:s})=>this.listInterfaces({nextToken:s,...t}).then(r=>({...r,items:r.interfaces}))),activities:t=>new B.AsyncCollection(({nextToken:s})=>this.listActivities({nextToken:s,...t}).then(r=>({...r,items:r.activities}))),files:t=>new B.AsyncCollection(({nextToken:s})=>this.listFiles({nextToken:s,...t}).then(r=>({...r,items:r.files}))),filePassages:t=>new B.AsyncCollection(({nextToken:s})=>this.listFilePassages({nextToken:s,...t}).then(r=>({...r,items:r.passages}))),usageActivity:t=>new B.AsyncCollection(({nextToken:s})=>this.listUsageActivity({nextToken:s,...t}).then(r=>({...r,items:r.data}))),usageActivityDaily:t=>new B.AsyncCollection(({nextToken:s})=>this.listUsageActivityDaily({nextToken:s,...t}).then(r=>({...r,items:r.data})))}}uploadFile=async t=>await gr(this,t)};0&&(module.exports={AlreadyExistsError,BreakingChangesError,Client,ForbiddenError,InternalError,InvalidDataFormatError,InvalidIdentifierError,InvalidJsonSchemaError,InvalidPayloadError,InvalidQueryError,LimitExceededError,MethodNotFoundError,PayloadTooLargeError,PaymentRequiredError,QuotaExceededError,RateLimitedError,ReferenceConstraintError,ReferenceNotFoundError,RelationConflictError,ResourceLockedConflictError,ResourceNotFoundError,RuntimeError,UnauthorizedError,UnknownError,UnsupportedMediaTypeError,UploadFileError,admin,axios,axiosRetry,errorFrom,files,isApiError,runtime,tables});
/*! Bundled license information:

mime-db/index.js:
  (*!
   * mime-db
   * Copyright(c) 2014 Jonathan Ong
   * Copyright(c) 2015-2022 Douglas Christopher Wilson
   * MIT Licensed
   *)

mime-types/index.js:
  (*!
   * mime-types
   * Copyright(c) 2014 Jonathan Ong
   * Copyright(c) 2015 Douglas Christopher Wilson
   * MIT Licensed
   *)
*/
//# sourceMappingURL=bundle.cjs.map
