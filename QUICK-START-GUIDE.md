# 🚀 QUICK START: Connect Your Botpress Chatbot to MySQL

## What You Have ✅
- ✅ Botpress chatbot collecting: `confNumber`, `dateTime`, `email`, `partySize`
- ✅ MySQL database with proper tables
- ✅ Backend API ready at `/api/botpress-reservations`

## What We're Doing
Adding one action to Botpress Studio to save data to your MySQL database.

---

## OPTION 1: Automated Setup (Recommended)

### Run the batch file:
```
start-botpress-integration.bat
```

This will:
1. Start your backend server
2. Start ngrok tunnel
3. Show you the ngrok URL to copy

---

## OPTION 2: Manual Setup

### 1. Start Backend
```bash
cd backend
npm start
```

### 2. Start ngrok (new terminal)
```bash
ngrok http 8080
```

### 3. Copy the HTTPS URL
Look for: `https://abc123.ngrok.io`

---

## Add Action to Botpress Studio

### 1. Go to Botpress Studio
https://studio.botpress.cloud/

### 2. Code Editor → Create File
`src/actions/saveReservationToMySQL.js`

### 3. Paste This Code:
```javascript
const saveReservationToMySQL = async ({ confNumber, dateTime, email, partySize, customerName, phone, specialRequests }) => {
  const axios = require('axios');
  
  try {
    console.log('🍽️ Abraham Restaurant - Saving to MySQL');
    
    // REPLACE WITH YOUR NGROK URL
    const API_URL = 'https://YOUR_NGROK_URL_HERE.ngrok.io/api/botpress-reservations';
    
    if (!email || !dateTime || !partySize) {
      throw new Error('Missing required fields');
    }

    const dateTimeObj = new Date(dateTime);
    const parsedDate = dateTimeObj.toISOString().split('T')[0];
    const parsedTime = dateTimeObj.toTimeString().split(' ')[0].substring(0, 5);

    const reservationData = {
      customerName: customerName || email.split('@')[0],
      email: email,
      phone: phone || '',
      date: parsedDate,
      time: parsedTime,
      datetime: dateTime,
      partySize: parseInt(partySize),
      specialRequests: specialRequests || ''
    };

    const response = await axios.post(API_URL, reservationData, {
      headers: { 'Content-Type': 'application/json' },
      timeout: 15000
    });

    return {
      success: true,
      reservationId: response.data.reservationId,
      confNumber: confNumber,
      message: `🎉 Reservation confirmed!\n📅 ${parsedDate} at ${parsedTime}\n👥 ${partySize} people\n📧 ${email}\n🔢 Confirmation: ${confNumber}`
    };

  } catch (error) {
    return {
      success: false,
      message: `Sorry, technical issue. Please call (*************.\nReference: ${confNumber}`
    };
  }
};

module.exports = { saveReservationToMySQL };
```

### 4. Update API URL
Replace `YOUR_NGROK_URL_HERE` with your actual ngrok URL

### 5. Save File

---

## Update Your Botpress Flow

### 1. Open Your Reservation Flow
Go to Flows → Your reservation flow

### 2. Add Execute Code Node
After collecting all data, add:
- **Action**: `saveReservationToMySQL`
- **Parameters**:
  ```
  confNumber: {{event.state.confNumber}}
  dateTime: {{event.state.dateTime}}
  email: {{event.state.email}}
  partySize: {{event.state.partySize}}
  customerName: {{event.state.customerName}}
  phone: {{event.state.phone}}
  specialRequests: {{event.state.specialRequests}}
  ```

### 3. Add Router
- Success: `{{temp.actionResult.success}} === true`
- Error: `{{temp.actionResult.success}} === false`

### 4. Add Responses
- **Success**: `{{temp.actionResult.message}}`
- **Error**: `{{temp.actionResult.message}}`

---

## Test It!

### 1. Publish Bot
Click "Publish" in Botpress Studio

### 2. Test Reservation
1. Go to http://localhost:3001
2. Open chatbot
3. Make a test reservation

### 3. Check MySQL
Look for new entries in:
- `reservations` table
- `botpress_reservations` table

---

## Success Indicators ✅

✅ **Backend logs**: Shows API calls from Botpress
✅ **MySQL**: New reservation entries
✅ **Chatbot**: Shows confirmation message
✅ **No errors**: In Botpress Studio logs

---

## Files Created for You:

📄 **abraham-restaurant-botpress-action.js** - Complete action code
📄 **ABRAHAM-RESTAURANT-BOTPRESS-MYSQL-SETUP.md** - Detailed setup guide
📄 **start-botpress-integration.bat** - Automated startup script

## Need Help?

If you get stuck:
1. Check backend is running on port 8080
2. Verify ngrok URL is correct in action
3. Ensure variable names match your Botpress flow
4. Check Botpress Studio logs for errors

Your integration is ready to go! 🚀
