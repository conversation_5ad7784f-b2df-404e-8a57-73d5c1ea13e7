/**
 * <PERSON> Restaurant - Botpress to MySQL Integration
 * Connects your Botpress chatbot directly to MySQL database
 * 
 * YOUR BOTPRESS WORKFLOW VARIABLES:
 * - confNumber: Confirmation number (generated by Bot<PERSON>)
 * - dateTime: Date and time combined
 * - email: Customer email
 * - partySize: Number of people
 * 
 * @title Save Reservation to MySQL
 * @category Abraham Restaurant
 * <AUTHOR> Restaurant System
 */

const saveReservationToMySQL = async ({ confNumber, dateTime, email, partySize, customerName, phone, specialRequests }) => {
  const axios = require('axios');
  
  try {
    console.log('🍽️ Abraham Restaurant - Saving reservation to MySQL');
    console.log('Botpress Input:', { confNumber, dateTime, email, partySize, customerName, phone, specialRequests });

    // STEP 1: Replace with your ngrok URL (see instructions below)
    // Get this URL by running: ngrok http 8080
    const API_URL = 'https://YOUR_NGROK_URL_HERE.ngrok.io/api/botpress-reservations';
    
    // STEP 2: Validate required fields from your Botpress workflow
    if (!email || !dateTime || !partySize) {
      throw new Error('Missing required fields from Botpress: email, dateTime, partySize');
    }

    // STEP 3: Parse dateTime from Botpress format
    let parsedDate, parsedTime;
    
    if (dateTime) {
      // Handle different datetime formats from Botpress
      const dateTimeObj = new Date(dateTime);
      if (isNaN(dateTimeObj.getTime())) {
        throw new Error('Invalid dateTime format from Botpress');
      }
      
      // Convert to MySQL format
      parsedDate = dateTimeObj.toISOString().split('T')[0]; // YYYY-MM-DD
      parsedTime = dateTimeObj.toTimeString().split(' ')[0].substring(0, 5); // HH:MM
    }

    // STEP 4: Prepare data for your MySQL database
    const reservationData = {
      // Map Botpress variables to your database fields
      customerName: customerName || email.split('@')[0] || 'Botpress Customer',
      email: email,
      phone: phone || '',
      date: parsedDate,
      time: parsedTime,
      datetime: dateTime, // Keep original for botpress_reservations table
      partySize: parseInt(partySize),
      specialRequests: specialRequests || '',
      confNumber: confNumber || `BP-${Date.now()}` // Use Botpress confNumber or generate one
    };

    console.log('📤 Sending to Abraham Restaurant MySQL database');
    console.log('📝 Formatted data:', reservationData);

    // STEP 5: Send to your existing API endpoint
    const response = await axios.post(API_URL, reservationData, {
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'User-Agent': 'Botpress-Abraham-Restaurant'
      },
      timeout: 15000 // 15 second timeout
    });

    console.log('✅ SUCCESS: Reservation saved to MySQL!');
    console.log('📊 Database response:', response.data);

    // STEP 6: Return success response to Botpress
    return {
      success: true,
      reservationId: response.data.reservationId,
      botpressId: response.data.id,
      confNumber: reservationData.confNumber,
      message: `🎉 Perfect! Your reservation has been confirmed and saved to Abraham Restaurant's system.\n\n📅 Date: ${parsedDate}\n⏰ Time: ${parsedTime}\n👥 Party Size: ${partySize} people\n📧 Email: ${email}\n🔢 Confirmation: ${reservationData.confNumber}\n\nWe look forward to serving you at Abraham Restaurant!`,
      details: {
        mysqlReservationId: response.data.reservationId,
        botpressReservationId: response.data.id,
        confirmationNumber: reservationData.confNumber,
        customerName: reservationData.customerName,
        email: reservationData.email,
        phone: reservationData.phone,
        date: parsedDate,
        time: parsedTime,
        partySize: reservationData.partySize,
        specialRequests: reservationData.specialRequests,
        savedAt: new Date().toISOString()
      }
    };

  } catch (error) {
    console.error('❌ ERROR: Failed to save reservation to MySQL');
    console.error('Error details:', error.message);
    console.error('Full error:', error);
    
    // Return error response to Botpress
    return {
      success: false,
      error: error.message,
      confNumber: confNumber || `BP-ERROR-${Date.now()}`,
      message: `I apologize, but there was a technical issue saving your reservation to our main system.\n\n📝 Your request details have been recorded:\n📧 Email: ${email}\n📅 DateTime: ${dateTime}\n👥 Party Size: ${partySize}\n\n📞 Please call us at (************* to confirm your reservation manually.\n\nReference: ${confNumber || 'BP-ERROR-' + Date.now()}`
    };
  }
};

module.exports = { saveReservationToMySQL };
