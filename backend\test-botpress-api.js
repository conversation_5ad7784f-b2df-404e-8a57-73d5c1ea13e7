const axios = require('axios');

async function testBotpressAPI() {
  try {
    console.log('🧪 Testing Botpress API endpoint...');
    
    const testData = {
      email: '<EMAIL>',
      datetime: '2024-12-25T19:00:00',
      partySize: 4
    };
    
    console.log('📤 Sending test data:', testData);
    
    const response = await axios.post('http://localhost:8080/api/botpress-reservations', testData, {
      headers: {
        'Content-Type': 'application/json'
      },
      timeout: 10000
    });
    
    console.log('✅ API Response Status:', response.status);
    console.log('✅ API Response Data:', JSON.stringify(response.data, null, 2));
    
  } catch (error) {
    console.error('❌ API Test Failed:');
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Data:', error.response.data);
    } else {
      console.error('Error:', error.message);
    }
  }
}

testBotpressAPI();
