# Production Environment Variables for Abraham Restaurant
# Copy these to your production server

# Database Configuration
DB_HOST=your-production-db-host
DB_USER=your-production-db-user
DB_PASSWORD=your-production-db-password
DB_NAME=abraham_restaurant

# MongoDB (if using)
DB=mongodb://your-production-mongo-url/theabraham-restaurant

# Server Configuration
PORT=8080
JWT_SECRET_KEY=your-super-secure-jwt-secret-key-here

# Botpress Configuration
BOTPRESS_API_URL=https://your-production-domain.com
BOTPRESS_PAT=your-botpress-personal-access-token
BOTPRESS_BOT_ID=your-bot-id
BOTPRESS_WORKSPACE_ID=your-workspace-id

# CORS Configuration
ALLOWED_ORIGINS=https://your-frontend-domain.com,https://cdn.botpress.cloud

# Security
NODE_ENV=production
